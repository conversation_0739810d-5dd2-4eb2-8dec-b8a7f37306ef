import{g as m}from"./-7hijANM.js";var x,s={exports:{}},f,v,g,y=(x||(x=1,v=s.exports,g=function(j){function u(e,i){var t,n;for(t=-1,n=e.length;++t<n;)i(e[t],t,e)}function p(e,i){var t;return t=Array(e.length),u(e,function(n,r,a){t[r]=i(n,r,a)}),t}function l(e,i,t){return u(e,function(n,r,a){t=i(n,r,a)}),t}function h(e,i){var t,n,r;return r=this._matrix=[],e==i?this.distance=0:e==""?this.distance=i.length:i==""?this.distance=e.length:(t=[0],u(e,function(a,o){o++,t[o]=o}),r[0]=t,u(i,function(a,o){n=[++o],u(e,function(A,c){c++,e.charAt(c-1)==i.charAt(o-1)?n[c]=t[c-1]:n[c]=Math.min(t[c]+1,n[c-1]+1,t[c-1]+1)}),t=n,r[r.length]=t}),this.distance=n[n.length-1])}return h.prototype.toString=h.prototype.inspect=function(e){var i,t,n,r;for(t=l(i=this.getMatrix(),function(a,o){return Math.max(a,l(o,Math.max,0))},0),n=Array((t+"").length).join(" "),r=[];r.length<(i[0]&&i[0].length||0);)r[r.length]=Array(n.length+1).join("-");return r=r.join("-+")+"-",p(i,function(a){return p(a,function(o){return(n+o).slice(-n.length)}).join(" |")+" "}).join(`
`+r+`
`)},h.prototype.getMatrix=function(){return this._matrix.slice()},h.prototype.valueOf=function(){return this.distance},h},(f=s)&&f.exports?f.exports=g():v.Levenshtein=g()),s.exports);const d=m(y);export{d as L};
