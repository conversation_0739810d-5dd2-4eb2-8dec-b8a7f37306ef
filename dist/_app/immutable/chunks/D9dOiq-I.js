/*
object-assign
(c) <PERSON><PERSON> Sorhus
@license MIT
*/var s,l;function O(){if(l)return s;l=1;var u=Object.getOwnPropertySymbols,j=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;return s=function(){try{if(!Object.assign)return!1;var o=new String("abc");if(o[5]="de",Object.getOwnPropertyNames(o)[0]==="5")return!1;for(var a={},r=0;r<10;r++)a["_"+String.fromCharCode(r)]=r;if(Object.getOwnPropertyNames(a).map(function(t){return a[t]}).join("")!=="**********")return!1;var e={};return"abcdefghijklmnopqrst".split("").forEach(function(t){e[t]=t}),Object.keys(Object.assign({},e)).join("")==="abcdefghijklmnopqrst"}catch(t){return!1}}()?Object.assign:function(o,a){for(var r,e,t=function(f){if(f==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(f)}(o),c=1;c<arguments.length;c++){for(var i in r=Object(arguments[c]))j.call(r,i)&&(t[i]=r[i]);if(u){e=u(r);for(var n=0;n<e.length;n++)p.call(r,e[n])&&(t[e[n]]=r[e[n]])}}return t},s}export{O as r};
