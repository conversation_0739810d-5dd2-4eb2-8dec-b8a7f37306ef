# 确保正确的 MIME 类型
<IfModule mod_mime.c>
    # JavaScript
    AddType application/javascript .js
    AddType application/javascript .mjs
    AddType text/javascript .js
    
    # CSS
    AddType text/css .css
    
    # JSON
    AddType application/json .json
    
    # Web fonts
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    
    # Images
    AddType image/svg+xml .svg
    AddType image/webp .webp
    
    # 确保模块脚本的正确 MIME 类型
    <FilesMatch "\.(js|mjs)$">
        Header set Content-Type "application/javascript"
    </FilesMatch>
    
    <FilesMatch "\.css$">
        Header set Content-Type "text/css"
    </FilesMatch>
</IfModule>

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# 缓存控制
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# 安全头
<IfModule mod_headers.c>
    # 防止 MIME 类型嗅探
    Header always set X-Content-Type-Options nosniff
    
    # 启用 XSS 保护
    Header always set X-XSS-Protection "1; mode=block"
    
    # 防止点击劫持
    Header always set X-Frame-Options DENY
    
    # 强制 HTTPS（如果使用 HTTPS）
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</IfModule>
