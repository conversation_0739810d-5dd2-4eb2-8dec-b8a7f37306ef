{"name": "speed-reading", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build && cp -rf public/. dist/", "build:prod": "rimraf dist && vite build --mode production && cp -f public/.gkdeply.config.json dist/", "postbuild": "node postbuild.js", "preview": "vite preview --host", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "svelte-kit sync || echo ''"}, "devDependencies": {"@rollup/plugin-replace": "^6.0.2", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.16", "glob": "^10.3.10", "moment": "^2.30.1", "postcss": "^8.4.32", "rimraf": "^6.0.1", "svelte": "^5.0.0", "svelte-preprocess": "^5.1.1", "tailwindcss": "^3.4.0", "terser": "^5.39.0", "vite": "^6.2.5", "vite-plugin-top-level-await": "^1.4.4"}, "dependencies": {"@sveltejs/adapter-static": "^3.0.8", "@tiptap/core": "^2.12.0", "@tiptap/extension-bubble-menu": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "GKAsr": "^0.1.18", "jsqr": "^1.4.0", "pinyin": "^2.11.2", "levenshtein": "^1.0.5"}, "engines": {"node": ">=14.0.0"}, "browserslist": [">0.2%", "not dead", "not op_mini all", "iOS >= 10", "Chrome >= 60", "Firefox >= 60", "Safari >= 10", "Edge >= 79", "and_chr >= 60", "and_ff >= 60", "samsung >= 7", "Android >= 7"]}