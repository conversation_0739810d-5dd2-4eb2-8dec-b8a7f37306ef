import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig, loadEnv } from 'vite';
import { resolve } from 'path';
import topLevelAwait from 'vite-plugin-top-level-await';

export default defineConfig(({ command, mode }) => {
	// 加载环境变量
	const env = loadEnv(mode, process.cwd(), '');

	// 是否为生产环境
	const isProd = mode === 'production';

	// 开发环境启用sourcemap，生产环境禁用
	const needSourcemap = !isProd;

	return {
		plugins: [
			sveltekit(),
			// 解决 esbuild 不支持 top level await 的问题
			topLevelAwait({
				promiseExportName: '__tla',
				promiseImportName: (i) => `__tla_${i}`,
			}),
		],
		resolve: {
			alias: {
				// 设置路径别名，简化导入
				'@': resolve(__dirname, './src'),
				'@components': resolve(__dirname, './src/components'),
				'@lib': resolve(__dirname, './src/lib'),
				'@services': resolve(__dirname, './src/services'),
				'@assets': resolve(__dirname, './src/assets'),
			}
		},
		// 确保静态文件直接复制
		publicDir: 'public',
		// 修改应用程序的基本路径，与 svelte.config.js 保持一致
		base: '/speed-reading/',
		build: {
			outDir: 'dist',
			assetsDir: 'assets',
			// 修改为更兼容的目标，支持更多移动端浏览器
			target: 'esnext',
			emptyOutDir: true,
			// 开发环境启用源码映射，生产环境禁用
			sourcemap: needSourcemap,
			// 压缩选项
			minify: 'terser',
			terserOptions: {
				compress: {
					// 只有在明确设置环境变量时才移除console和debugger
					// 这样可以保留兼容性调试信息
					drop_console: env.VITE_NO_CONSOLE === 'true',
					drop_debugger: env.VITE_NO_CONSOLE === 'true'
				}
			},
			// 代码分割配置
			rollupOptions: {
				output: {
					// 自定义代码分块策略
					manualChunks: (id) => {
						// 把node_modules的模块分开打包
						if (id.includes('node_modules')) {
							// 分析引入的包名
							const arr = id.toString().split('node_modules/')[1].split('/');
							// 取主包名作为chunk名
							const pkgName = arr[0];
							// 将一些相关的库组合成一个chunk
							if (['svelte', '@sveltejs'].some(pkg => pkgName.includes(pkg))) {
								return 'vendor_svelte';
							}
							// 其他第三方库
							return 'vendor_' + pkgName.replace('@', '');
						}
						// 把组件库代码独立分块
						else if (id.includes('/src/components/') && !id.endsWith('.svelte')) {
							return 'components';
						}
						// 把公共库代码独立分块
						else if (id.includes('/src/lib/')) {
							return 'lib';
						}
						// 把服务相关代码独立分块
						else if (id.includes('/src/services/')) {
							return 'services';
						}
					},
					// 将CSS提取到单独的文件
					assetFileNames: (assetInfo) => {
						const info = assetInfo.name.split('.');
						let extType = info[info.length - 1];
						if (/\.(css)$/i.test(assetInfo.name)) {
							return `assets/css/[name]-[hash][extname]`;
						}
						else if (/\.(png|jpe?g|gif|svg|webp)$/i.test(assetInfo.name)) {
							return `assets/images/[name]-[hash][extname]`;
						}
						else if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
							return `assets/fonts/[name]-[hash][extname]`;
						}
						return `assets/[name]-[hash][extname]`;
					},
					// 自定义JS文件输出
					chunkFileNames: 'assets/js/[name]-[hash].js',
					entryFileNames: 'assets/js/[name]-[hash].js',
				}
			},
			// 设置静态资源阈值，小于此值的会被转为base64
			assetsInlineLimit: isProd ? 8192 : 4096,
			// 生产构建压缩大小警告阈值
			chunkSizeWarningLimit: 1000, // 1000kb
		},
		optimizeDeps: {
			exclude: ['fsevents']
		},
		// 服务器配置
		server: {
			port: 9104,
			open: true,
			cors: true,
			// 启用压缩
			compress: true,
			// 添加代理配置，解决CORS问题
			// 使用更具体的路径前缀，避免与资源路径冲突
			proxy: {
				'/dev-api-proxy': {
					target: 'https://tools.gankao.com',
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/dev-api-proxy/, ''),
					secure: false, // 如果是https接口，需要设置为false
				},
				'/dev-ai-api': {
					target: 'http://aigate.gankaotest2.com/v1',
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/dev-ai-api/, ''),
					secure: false,
				}
			}
		},
		// 预览服务器配置
		preview: {
			port: 5000,
			open: true,
			cors: true,
			// 启用压缩
			compress: true,
		},
		esbuild: {
			// 修改为更兼容的目标，确保生成的代码能在老版本浏览器中运行
			target: 'esnext',
			// 生产环境下增强的压缩
			legalComments: isProd ? 'none' : 'eof',
			// 只有在明确设置环境变量时才移除console.log
			// 保留兼容性调试信息，方便排查问题
			drop: env.VITE_NO_CONSOLE === 'true' ? ['console', 'debugger'] : [],
		}
	};
});
