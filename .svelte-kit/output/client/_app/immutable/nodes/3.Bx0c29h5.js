import{p as it,a as De,m as fe,c as kt,o as mt,i as nt,F as Ot,G as ht,f as T,g as e,v as o,x as at,s as W,t as w,e as s,r as t,h as i,A as ze,j as A,k as ye,y as ft,z as h,q as gt,l as lt,X as Be,d as bt,N as He,n as Ae,J as Tt,b as Vt,I as Lt,Y as Xe,M as _t,u as Ee,V as We,K as zt,L as At,O as Ke}from"../chunks/CLjOhJ05.js";import{E as Ft,P as pt}from"../chunks/CFonbW8n.js";import{E as Ut}from"../chunks/DLWfJtl1.js";const Et=Object.freeze(Object.defineProperty({__proto__:null,load:async function(){return{title:"\u589E\u5F3A\u79D2\u8BCD\u6D4B\u8BD5",description:"\u57FA\u4E8E\u97F3\u7D20\u5339\u914D\u7684\u667A\u80FD\u79D2\u8BCD\u7CFB\u7EDF"}},prerender:!1,ssr:!1},Symbol.toStringTag,{value:"Module"}));var Wt=w('<div class="countdown-mask svelte-1mmrjfg"><div class="countdown-content svelte-1mmrjfg"><div class="countdown-number svelte-1mmrjfg"> </div> <div class="countdown-description svelte-1mmrjfg"> </div> <div class="countdown-ring svelte-1mmrjfg"><svg width="120" height="120" viewBox="0 0 120 120" class="svelte-1mmrjfg"><circle cx="60" cy="60" r="54" stroke="#e5e7eb" stroke-width="4" fill="none" class="svelte-1mmrjfg"></circle><circle cx="60" cy="60" r="54" stroke-width="4" fill="none" stroke-dasharray="339.3" stroke-linecap="round" class="progress-circle svelte-1mmrjfg"></circle></svg></div> <div class="ready-hint svelte-1mmrjfg"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="svelte-1mmrjfg"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2" class="svelte-1mmrjfg"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1mmrjfg"></path><path d="M12 18V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1mmrjfg"></path><path d="M8 22H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1mmrjfg"></path></svg> \u51C6\u5907\u597D\u60A8\u7684\u9EA6\u514B\u98CE</div></div> <div class="countdown-bg svelte-1mmrjfg"><div class="bg-circle circle-1 svelte-1mmrjfg"></div> <div class="bg-circle circle-2 svelte-1mmrjfg"></div> <div class="bg-circle circle-3 svelte-1mmrjfg"></div></div></div>');function Ht(Fe,le){it(le,!1);let G,ne=De(le,"duration",8,3),re=De(le,"description",8,"\u6BCF\u4E2A\u5355\u8BCD\u9650\u65F63\u79D2\uFF0C\u8BF7\u5927\u58F0\u6717\u8BFB\u51FA\u82F1\u6587\u5355\u8BCD"),u=fe(ne()),p=fe(!0);const m=kt(),oe="https://img.qiaoxuesi.com/files/65_3-2-1-go.mp3";function H(){return e(u)>0?e(u).toString():"GO!"}function ge(){return e(u)<=0?"#10b981":e(u)===1?"#f59e0b":"#3b82f6"}mt(()=>(function(){try{new Audio(oe).preload="auto"}catch(ve){}}(),function(){try{new Audio(oe).play().catch(ve=>{})}catch(ve){}G=setInterval(()=>{W(u,e(u)-1),e(u)===2&&m("prestart"),e(u)<=0&&(clearInterval(G),setTimeout(()=>{W(p,!1),m("finish")},300))},1e3)}(),()=>{G&&clearInterval(G)})),nt();var D=Ot(),d=ht(D),_=ve=>{var X=Wt(),Z=s(X),c=s(Z),k=s(c,!0);t(c);var f=i(c,2),F=s(f,!0);t(f);var J=i(f,2),ce=s(J),se=i(s(ce));t(ce),t(J),ze(2),t(Z),ze(2),t(X),A((ae,de,K)=>{ft(c,`color: ${ae!=null?ae:""}`),h(k,de),h(F,re()),gt(se,"stroke",K),gt(se,"stroke-dashoffset",e(u)/ne()*339.3)},[ge,H,ge],ye),o(ve,X)};T(d,ve=>{e(p)&&ve(_)}),o(Fe,D),at()}var Xt=w('<div class="repeat-badge svelte-1wpaoaf"> </div>'),Zt=Tt('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="svelte-1wpaoaf"><path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1wpaoaf"></path></svg>'),Nt=Tt('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="svelte-1wpaoaf"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" class="svelte-1wpaoaf"></circle><path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1wpaoaf"></path></svg>'),Yt=w("<div><!></div>"),Gt=w('<div class="progress-ring svelte-1wpaoaf"><svg width="40" height="40" viewBox="0 0 40 40" class="svelte-1wpaoaf"><circle cx="20" cy="20" r="18" stroke="#e2e8f0" stroke-width="3" fill="none" class="svelte-1wpaoaf"></circle><circle cx="20" cy="20" r="18" stroke="#3b82f6" stroke-width="3" fill="none" stroke-dasharray="113" stroke-dashoffset="113" stroke-linecap="round" class="progress-circle svelte-1wpaoaf"></circle></svg> <div class="progress-text svelte-1wpaoaf"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="svelte-1wpaoaf"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2" class="svelte-1wpaoaf"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1wpaoaf"></path></svg></div></div>'),Jt=w('<div class="waiting-indicator svelte-1wpaoaf"><div class="ready-dot svelte-1wpaoaf"></div></div>'),Kt=w('<div class="waiting-indicator svelte-1wpaoaf"><div class="waiting-dot svelte-1wpaoaf"></div></div>'),Qt=w('<div class="score-value svelte-1wpaoaf"> </div> <div class="confidence-text svelte-1wpaoaf"> </div>',1),es=w('<div class="score-value svelte-1wpaoaf">0</div>'),ts=w('<div><div class="wheel-item-content svelte-1wpaoaf"><div class="word-info svelte-1wpaoaf"><div class="english-word svelte-1wpaoaf"> </div> <div class="chinese-meaning svelte-1wpaoaf"> </div> <!></div> <div class="status-indicator svelte-1wpaoaf"><!></div> <div><!></div></div></div>'),ss=w('<div class="word-list svelte-1wpaoaf"><div class="wheel-wrapper svelte-1wpaoaf"><div class="scroll-list svelte-1wpaoaf"></div></div></div>');function is(Fe,le){it(le,!1);const G=fe();let ne=De(le,"wordList",24,()=>[]),re=De(le,"currentIndex",8,0),u=De(le,"results",24,()=>[]),p=De(le,"isRunning",8,!1),m=De(le,"maxTimePerWord",8,3500);lt(()=>(Be(ne()),Be(re()),Be(u())),()=>{W(G,function(D,d,_){if(!D||D.length===0)return[];const ve=Math.max(0,d-3),X=Math.min(D.length-1,d+3),Z=[];for(let c=ve;c<=X;c++){const k=D[c];if(!k)continue;const f=49*(c-d+2);Z.push({...k,idx:c,isActive:c===d,isPast:c<d,isFuture:c>d,result:_==null?void 0:_[c],topOffset:f})}return Z}(ne(),re(),u()))}),lt(()=>(Be(ne()),Be(re()),Be(u()),Be(p()),e(G)),()=>{}),lt(()=>Be(re()),()=>{}),bt(),nt();var oe=ss(),H=s(oe),ge=s(H);He(ge,5,()=>e(G),D=>D.key,(D,d)=>{var _=ts();let ve;var X=s(_),Z=s(X),c=s(Z),k=s(c,!0);t(c);var f=i(c,2),F=s(f,!0);t(f);var J=i(f,2),ce=$=>{var O=Xt(),b=s(O);t(O),A(()=>{var U;return h(b,`\u91CD\u590D ${(U=e(d).repeatCount)!=null?U:""}`)}),o($,O)};T(J,$=>{e(d).isRepeat&&$(ce)}),t(Z);var se=i(Z,2),ae=s(se),de=$=>{var O=Yt();let b;var U=s(O),r=q=>{var V=Zt();o(q,V)},y=q=>{var V=Nt();o(q,V)};T(U,q=>{e(d).result.matched?q(r):q(y,!1)}),t(O),A(q=>b=Ae(O,1,"result-icon svelte-1wpaoaf",null,b,q),[()=>({matched:e(d).result.matched})],ye),o($,O)},K=($,O)=>{var b=r=>{var y=Gt(),q=s(y),V=i(s(q));t(q),ze(2),t(y),A(()=>{var R,L;gt(y,"key",`progress-${(R=re())!=null?R:""}`),ft(V,`animation-duration: ${(L=m())!=null?L:""}ms`)}),o(r,y)},U=(r,y)=>{var q=R=>{var L=Jt();o(R,L)},V=R=>{var L=Kt();o(R,L)};T(r,R=>{e(d).isActive&&!p()?R(q):R(V,!1)},y)};T($,r=>{e(d).isActive&&p()?r(b):r(U,!1)},O)};T(ae,$=>{e(d).result?$(de):$(K,!1)}),t(se);var g=i(se,2);let B;var ee=s(g),be=$=>{var O=Qt(),b=ht(O),U=s(b,!0);t(b);var r=i(b,2),y=s(r);t(r),A(q=>{h(U,e(d).result.score),h(y,`${q!=null?q:""}%`)},[()=>Math.round(100*(e(d).result.confidence||0))],ye),o($,O)},Me=($,O)=>{var b=U=>{var r=es();o(U,r)};T($,U=>{e(d).isPast&&U(b)},O)};T(ee,$=>{e(d).result?$(be):$(Me,!1)}),t(g),t(X),t(_),A(($,O)=>{var b;ve=Ae(_,1,"wheel-item svelte-1wpaoaf",null,ve,$),ft(_,`top: ${(b=e(d).topOffset)!=null?b:""}px`),h(k,e(d).english),h(F,e(d).chinese),B=Ae(g,1,"score-section svelte-1wpaoaf",null,B,O)},[()=>({active:e(d).isActive,past:e(d).isPast,future:e(d).isFuture}),()=>{var $;return{correct:($=e(d).result)==null?void 0:$.matched,wrong:e(d).result&&!e(d).result.matched}}],ye),o(D,_)}),t(ge),t(H),t(oe),o(Fe,oe),at()}var ns=w('<div class="current-word-hint svelte-163jitm"><div class="hint-label svelte-163jitm">\u5F53\u524D\u5355\u8BCD:</div> <div class="hint-word svelte-163jitm"><span class="english svelte-163jitm"> </span> <span class="chinese svelte-163jitm"> </span></div></div>'),as=w('<button class="start-btn svelte-163jitm"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12 18V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8 22H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg> \u5F00\u59CB\u79D2\u8BCD</button>'),ls=w('<button class="quit-btn svelte-163jitm"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg> \u9000\u51FA\u6D4B\u8BD5</button>'),rs=w('<div class="enhanced-speed-card svelte-163jitm"><div class="progress-indicator svelte-163jitm"> </div>  <!> <div class="word-list-container svelte-163jitm"><!></div> <div class="controls svelte-163jitm"><!> <div class="instruction svelte-163jitm"><!></div></div> <div class="hidden-asr svelte-163jitm"><!></div> <!></div>');function os(Fe,le){it(le,!1);let G=De(le,"dataSource",24,()=>[]);De(le,"repeatTimes",8,2);const ne=3500;let re=fe(),u=fe([]),p=fe(0),m=fe([]),oe=[],H=fe(!1),ge=fe(!1),D=null,d=null,_=!1,ve=0,X=[],Z="",c=new Map,k=null,f=null,F=null,J=[];const ce=kt();function se(){W(u,G().map((n,a)=>({...n,key:`word_${a}_${Date.now()}`,index:a,offset:49*a,isRepeat:!1,originalIndex:a}))),ve=e(u).length,W(m,new Array(e(u).length).fill(null))}async function ae(){try{return await e(re).startRecording(),await async function(){try{F||(F=new Ut);const n=await e(re).getAudioStream();if(!n)return;if(await F.initialize(n,g)){const a=B();F.startDetection(a)}}catch(n){}}(),!0}catch(n){return!1}}async function de(){}function K(){if(e(p)>=e(u).length)return void r();const n=e(u)[e(p)];if(F){const j=B();F.startDetection(j)}const a={word:n,index:e(p),startTime:Date.now(),status:"waiting"};X.push(a),D=setTimeout(()=>{(function(){if(!e(H)||e(p)>=e(u).length||_)return;const j=e(u)[e(p)],M=e(p)===e(u).length-1,Q=X.find(S=>S.index===e(p)&&S.status==="waiting");if(Q&&(Q.status="timeout"),e(m)[e(p)])return void(M?r():U(300));if(M)return void(k=setTimeout(async()=>{await de(),f=setTimeout(()=>{r()},3e3)},500));(function(S){let l=[];try{l=new pt().englishToPhonemes(S.english)}catch(v){}const x={refText:S.english,asrResult:"\u8D85\u65F6\u672A\u8BC6\u522B",matched:!1,confidence:0,method:"timeout",details:{timeout:!0,timeoutDuration:ne,asrPhonemes:[],targetPhonemes:l,similarity:0,matchMethod:"timeout",reason:"\u8BC6\u522B\u8D85\u65F6"},score:0,audioUrl:"",timestamp:new Date().toISOString()};Xe(m,e(m)[e(p)]=x),oe.push({...S,resultIndex:e(p)}),U(300)})(j)})()},ne)}function g(n,a){if(typeof window!="undefined"){const j=document.createElement("div");if(j.className="environment-warning-message",j.innerHTML=`
        <div class="warning-content">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2L1 21h22L12 2zm0 3.99L19.53 19H4.47L12 5.99zM11 16h2v2h-2v-2zm0-6h2v4h-2v-4z"/>
          </svg>
          <span>${n}</span>
        </div>
      `,j.style.cssText=`
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #fff7e6;
        border: 1px solid #ffd591;
        border-radius: 6px;
        padding: 12px 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        font-size: 14px;
        color: #d46b08;
        max-width: 400px;
        animation: slideInDown 0.3s ease-out;
      `,!document.querySelector("#environment-warning-styles")){const M=document.createElement("style");M.id="environment-warning-styles",M.textContent=`
          @keyframes slideInDown {
            from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
          }
          .warning-content {
            display: flex;
            align-items: center;
            gap: 8px;
          }
          .warning-content svg {
            flex-shrink: 0;
          }
        `,document.head.appendChild(M)}document.body.appendChild(j),setTimeout(()=>{j.parentNode&&(j.style.animation="slideInDown 0.3s ease-out reverse",setTimeout(()=>{j.remove()},300))},3e3)}J.push({message:n,type:a,timestamp:new Date().toISOString()})}function B(){if(e(p)>=e(u).length)return 1;const n=e(u)[e(p)];return!n||!n.english?1:n.english.trim().split(/\s+/).length}async function ee(n,a,j){var M,Q,S,l;try{const x=new pt,v=await x.enhancedMatch(n,a,"en");return{refText:a,asrResult:n,matched:v.matched,confidence:v.confidence,method:v.method,details:{...v.details,asrPhonemes:((M=v.details)==null?void 0:M.asrPhonemes)||[],targetPhonemes:((Q=v.details)==null?void 0:Q.targetPhonemes)||[],similarity:((S=v.details)==null?void 0:S.similarity)||0,matchMethod:((l=v.details)==null?void 0:l.matchMethod)||v.method,queueMatched:!0,originalAnalysis:j},score:Math.round(100*(v.confidence||0)),audioUrl:"",timestamp:new Date().toISOString()}}catch(x){return{refText:a,asrResult:n,matched:!1,confidence:0,method:"error",details:{error:x.message,queueMatched:!0,originalAnalysis:j},score:0,audioUrl:"",timestamp:new Date().toISOString()}}}let be=!1,Me=null;function $(n){return!Z||!n?n:n.startsWith(Z)?n.substring(Z.length).trim():n}async function O(n){if(!e(H))return;let{content:a,status:j}=n.detail;if(a=function(x){return x?x.replace(/[，。！？；：""''（）【】《》〈〉「」『』〔〕［］｛｝〖〗〘〙〚〛,.!?;:"'()\[\]{}<>]/g,"").replace(/\s+/g," ").trim():""}(a),!a)return;if(f)return void function(x,v,te){var ie;f&&(clearTimeout(f),f=null);const C=e(u).length-1,I=e(u)[C],E={content:$(x||""),originalContent:x||"",matchResult:v,analysisDetails:te,timestamp:Date.now()},we={refText:I.english,asrResult:E.content,matched:!1,confidence:0,method:"unknown",details:{...E.analysisDetails,asrPhonemes:[],targetPhonemes:[],similarity:0,matchMethod:"",originalMatchResult:E.matchResult,processingTime:E.timestamp-(((ie=X.find(Ie=>Ie.index===C))==null?void 0:ie.startTime)||0),baseContentUsed:Z,originalBeforeClean:E.originalContent},score:Math.round(0),audioUrl:"",timestamp:new Date().toISOString()};Xe(m,e(m)[C]=we);const Oe=X.find(Ie=>Ie.index===C);Oe&&(Oe.status=we.matched?"completed":"failed"),we.matched?setTimeout(()=>{r()},300):(oe.push({...I,resultIndex:C}),setTimeout(()=>{r()},500))}(a,null,null);(function(){const x=X.filter(v=>v.status==="timeout");x.length!==0&&x.forEach(v=>{const te=`${v.index}_${v.word.english}`,C=(c.get(te)||0)+1;if(c.set(te,C),C>=3){const I=e(m)[v.index];I&&I.matched?v.status="completed":(v.status="failed",I||Xe(m,e(m)[v.index]={refText:v.word.english,asrResult:"\u672A\u8BC6\u522B",matched:!1,confidence:0,method:"final_timeout",details:{finalTimeout:!0,callbackCount:C,reason:"\u56DE\u8C03\u8BA1\u6570\u8D85\u65F6"},score:0,audioUrl:"",timestamp:new Date().toISOString()}),oe.push({...v.word,resultIndex:v.index})),c.delete(te)}})})();const M=$(a||"");F&&F.processASRResult(a);const Q=await async function(x,v){const te=X.filter(I=>I.status==="waiting"||I.status==="timeout");if(te.length===0)return null;const C=[];for(const I of te){const E=await ee(x,I.word.english,null);E.details.processingTime=Date.now()-I.startTime,E.details.baseContentUsed=Z,E.details.originalBeforeClean=v,E.matched&&C.push({waitingWord:I,wordResult:E,cleanContent:x,originalContent:v})}return C.length===0?null:C.length===1?{type:"single",result:C[0]}:(C.sort((I,E)=>I.waitingWord.index-E.waitingWord.index),{type:"multiple",results:C})}(M,a);var S,l;Q&&(l=a,(S=Q).type==="single"?function(x,v,te){const{waitingWord:C,wordResult:I}=x;Xe(m,e(m)[C.index]=I),C.status="completed",Z=te.trim();const E=`${C.index}_${C.word.english}`;if(c.delete(E),C.index===e(u).length-1)return k&&(clearTimeout(k),k=null),f&&(clearTimeout(f),f=null),void setTimeout(()=>{r()},300);C.index===e(p)&&U(800)}(S.result,0,l):S.type==="multiple"&&function(x,v,te){let C=!1,I=!1,E=e(p);if(x.forEach((we,Oe)=>{const{waitingWord:ie,wordResult:Ie}=we;Xe(m,e(m)[ie.index]=Ie),ie.status="completed";const tt=`${ie.index}_${ie.word.english}`;c.delete(tt),ie.index===e(u).length-1&&(C=!0),ie.index===e(p)&&(I=!0),ie.index>=E&&(E=ie.index+1)}),Z=te.trim(),C)return k&&(clearTimeout(k),k=null),f&&(clearTimeout(f),f=null),void setTimeout(()=>{r()},300);I&&U(800)}(S.results,0,l))}function b(n){if(be){Me&&(clearTimeout(Me),Me=null);const{content:a}=n.detail;a&&O({detail:{content:a,status:"completed",matchResult:null,analysisDetails:null}}),setTimeout(()=>{be=!1,r()},1e3)}}function U(n=0){D&&(clearTimeout(D),D=null),d&&(clearTimeout(d),d=null),_||(d=setTimeout(()=>{(function(){if(_)return;_=!0;const a=X.find(j=>j.index===e(p));if(a&&a.status==="waiting"&&(a.status="abandoned"),_t(p),e(p)>=G().length)return _=!1,be=!0,void(Me=setTimeout(()=>{be=!1,r()},5e3));e(H)&&K(),_=!1})()},n))}async function r(){W(H,!1),_=!1,await de(),X.filter(a=>a.status==="waiting"||a.status==="timeout").forEach(a=>{const j=e(m)[a.index];if(!(j&&j.matched)){let M=[];try{M=new pt().englishToPhonemes(a.word.english)}catch(S){}const Q={refText:a.word.english,asrResult:"\u6D4B\u8BD5\u7ED3\u675F\u65F6\u672A\u5339\u914D",matched:!1,confidence:0,method:"test_ended",details:{reason:"\u6D4B\u8BD5\u7ED3\u675F\u65F6\u4ECD\u5728\u7B49\u5F85\u961F\u5217\u4E2D",finalStatus:a.status,testEndTime:new Date().toISOString(),asrPhonemes:[],targetPhonemes:M,similarity:0,matchMethod:"test_ended"},score:0,audioUrl:"",timestamp:new Date().toISOString()};Xe(m,e(m)[a.index]=Q),oe.push({...a.word,resultIndex:a.index})}}),D&&(clearTimeout(D),D=null),d&&(clearTimeout(d),d=null),k&&(clearTimeout(k),k=null),f&&(clearTimeout(f),f=null),F&&(F.cleanup(),F=null),X=[],Z="",c.clear(),J=[];for(let a=0;a<e(u).length;a++)e(m)[a];const n=e(m).filter(a=>a!==null);ce("finish",n)}function y(){W(ge,!0)}async function q(){const n=await ae();window.prestartRecordingFailed=!n}async function V(){if(W(ge,!1),W(H,!0),W(p,0),window.prestartRecordingFailed&&!await ae())return void W(H,!1);window.prestartRecordingFailed=!1,K()}async function R(){W(H,!1),_=!1,await de(),D&&(clearTimeout(D),D=null),d&&(clearTimeout(d),d=null),k&&(clearTimeout(k),k=null),f&&(clearTimeout(f),f=null),X=[],Z="",c.clear(),ce("quit")}mt(()=>{G()&&G().length>0&&se()}),Vt(async()=>{D&&clearTimeout(D),d&&clearTimeout(d),k&&clearTimeout(k),f&&clearTimeout(f),e(H)&&await de(),X=[],Z="",c.clear()}),lt(()=>Be(G()),()=>{G()&&G().length>0&&se()}),bt(),nt();var L=rs(),qe=s(L),Se=s(qe);t(qe);var pe=i(qe,2),xe=n=>{var a=ns(),j=i(s(a),2),M=s(j),Q=s(M,!0);t(M);var S=i(M,2),l=s(S);t(S),t(j),t(a),A(()=>{var x;h(Q,e(u)[e(p)].english),h(l,`(${(x=e(u)[e(p)].chinese)!=null?x:""})`)}),o(n,a)};T(pe,n=>{e(H)&&e(u)[e(p)]&&n(xe)});var Pe=i(pe,2);is(s(Pe),{get wordList(){return e(u)},get currentIndex(){return e(p)},get results(){return e(m)},get isRunning(){return e(H)},maxTimePerWord:ne}),t(Pe);var Re=i(Pe,2),ue=s(Re),Te=n=>{var a=as();Ee("click",a,y),o(n,a)},Ze=(n,a)=>{var j=M=>{var Q=ls();Ee("click",Q,R),o(M,Q)};T(n,M=>{e(H)&&M(j)},a)};T(ue,n=>{e(H)||e(ge)?n(Ze,!1):n(Te)});var Ne=i(ue,2),rt=s(Ne),Ye=n=>{var a=We("\u8BF7\u5927\u58F0\u6717\u8BFB\u51FA\u82F1\u6587\u5355\u8BCD");o(n,a)},Qe=(n,a)=>{var j=M=>{var Q=We("\u6B63\u5728\u8FDB\u884C\u4E2D...");o(M,Q)};T(n,M=>{e(H)&&M(j)},a)};T(rt,n=>{e(H)||e(ge)?n(Qe,!1):n(Ye)}),t(Ne),t(Re);var Ge=i(Re,2),Je=s(Ge);const et=ye(()=>{var n;return((n=e(u)[e(p)])==null?void 0:n.english)||""});Lt(Ft(Je,{get targetWord(){return e(et)},language:"en",duration:600,direction:"top",showTextPanel:!1,$$events:{textInput:O,textCompleted:b,stop:b,matchCompleted:function(n){}},$$legacy:!0}),n=>W(re,n),()=>e(re)),t(Ge);var ot=i(Ge,2),ct=n=>{Ht(n,{duration:3,description:"\u6BCF\u4E2A\u5355\u8BCD\u9650\u65F63.5\u79D2\uFF0C\u8BF7\u5927\u58F0\u6717\u8BFB\u51FA\u82F1\u6587\u5355\u8BCD",$$events:{prestart:q,finish:V}})};T(ot,n=>{e(ge)&&n(ct)}),t(L),A(()=>{var n,a;return h(Se,`${(n=e(p))!=null?n:""} / ${(a=e(u).length)!=null?a:""}`)}),o(Fe,L),at()}var cs=w("<button> </button>"),ds=w("<button> </button>"),vs=w('<div><span class="word-number svelte-mi828q"></span> <span class="english svelte-mi828q"> </span> <span class="chinese svelte-mi828q"> </span> <span class="difficulty svelte-mi828q"> </span></div>'),us=w('<div class="word-preview svelte-mi828q"></div>'),ms=w('<div class="no-words svelte-mi828q"><p class="svelte-mi828q">\u6CA1\u6709\u627E\u5230\u7B26\u5408\u6761\u4EF6\u7684\u5355\u8BCD\uFF0C\u8BF7\u8C03\u6574\u96BE\u5EA6\u7B49\u7EA7</p></div>'),hs=w('<div class="settings-container svelte-mi828q"><div class="header svelte-mi828q"><h1 class="svelte-mi828q">\u589E\u5F3A\u79D2\u8BCD\u6D4B\u8BD5</h1> <p class="svelte-mi828q">\u57FA\u4E8E\u97F3\u7D20\u5339\u914D\u7684\u667A\u80FD\u8BED\u97F3\u8BC6\u522B\u7CFB\u7EDF</p></div> <div class="settings-panel svelte-mi828q"><div class="setting-group svelte-mi828q"><label class="svelte-mi828q">\u9009\u62E9\u96BE\u5EA6\u7B49\u7EA7\uFF1A</label> <div class="difficulty-buttons svelte-mi828q"></div> <div class="difficulty-desc svelte-mi828q"><!></div></div> <div class="setting-group svelte-mi828q"><label class="svelte-mi828q">\u9009\u62E9\u5355\u8BCD\u6570\u91CF\uFF1A</label> <div class="count-buttons svelte-mi828q"></div></div> <div class="preview-section svelte-mi828q"><h3 class="svelte-mi828q"> </h3> <!></div> <div class="start-section svelte-mi828q"><button class="start-btn svelte-mi828q"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M8 5V19L19 12L8 5Z" fill="currentColor"></path></svg> \u5F00\u59CB\u79D2\u8BCD\u6D4B\u8BD5</button> <div class="test-info svelte-mi828q"><p class="svelte-mi828q">\u2022 \u6BCF\u4E2A\u5355\u8BCD\u9650\u65F6 3.5 \u79D2</p> <p class="svelte-mi828q">\u2022 \u9519\u8BEF\u5355\u8BCD\u4F1A\u81EA\u52A8\u91CD\u590D</p> <p class="svelte-mi828q">\u2022 \u652F\u6301\u97F3\u7D20\u7EA7\u667A\u80FD\u5339\u914D</p></div></div></div></div>'),fs=w('<div class="method-item svelte-mi828q"><span class="method-label svelte-mi828q">\u672A\u77E5</span> <span class="method-count svelte-mi828q"> </span></div>'),gs=w('<div class="difficulty-item svelte-mi828q"><div class="difficulty-level svelte-mi828q"> </div> <div class="difficulty-score svelte-mi828q"> </div></div>'),ps=w('<div class="phoneme-row svelte-mi828q"><strong>\u8BC6\u522B\u97F3\u7D20:</strong> </div>'),ws=w('<div class="phoneme-row svelte-mi828q"><strong>\u8BC6\u522B\u97F3\u7D20:</strong> [\u65E0\u97F3\u7D20\u6570\u636E]</div>'),ys=w('<div class="phoneme-row svelte-mi828q"><strong>\u76EE\u6807\u97F3\u7D20:</strong> </div>'),qs=w('<div class="phoneme-row svelte-mi828q"><strong>\u76EE\u6807\u97F3\u7D20:</strong> [\u65E0\u97F3\u7D20\u6570\u636E]</div>'),xs=w('<div class="phoneme-section svelte-mi828q"><!> <!> <div class="phoneme-row svelte-mi828q"><strong>\u76F8\u4F3C\u5EA6:</strong> </div></div>'),ks=w('<div class="timeout-section svelte-mi828q"><div class="timeout-info svelte-mi828q"><strong>\u8BC6\u522B\u97F3\u7D20:</strong> [\u8D85\u65F6\u672A\u83B7\u53D6]</div> <div class="timeout-info svelte-mi828q"><strong>\u76EE\u6807\u97F3\u7D20:</strong> [\u8D85\u65F6\u672A\u83B7\u53D6]</div> <div class="timeout-info svelte-mi828q"><strong>\u76F8\u4F3C\u5EA6:</strong> 0.0%</div></div>'),bs=w('<div class="direct-section svelte-mi828q"><div class="direct-info svelte-mi828q"><strong>\u8BC6\u522B\u97F3\u7D20:</strong> [\u76F4\u63A5\u5339\u914D\uFF0C\u65E0\u9700\u97F3\u7D20]</div> <div class="direct-info svelte-mi828q"><strong>\u76EE\u6807\u97F3\u7D20:</strong> [\u76F4\u63A5\u5339\u914D\uFF0C\u65E0\u9700\u97F3\u7D20]</div> <div class="direct-info svelte-mi828q"><strong>\u76F8\u4F3C\u5EA6:</strong> </div></div>'),Ts=w('<div class="unknown-section svelte-mi828q"><div class="unknown-info svelte-mi828q"><strong>\u8BC6\u522B\u97F3\u7D20:</strong> [\u6570\u636E\u7F3A\u5931]</div> <div class="unknown-info svelte-mi828q"><strong>\u76EE\u6807\u97F3\u7D20:</strong> [\u6570\u636E\u7F3A\u5931]</div> <div class="unknown-info svelte-mi828q"><strong>\u76F8\u4F3C\u5EA6:</strong> </div></div>'),Cs=w('<div class="debug-info svelte-mi828q"><strong>\u5904\u7406\u5EF6\u8FDF:</strong> </div>'),$s=w('<div class="debug-info svelte-mi828q"><strong>\u57FA\u5E95\u6E05\u7406:</strong> </div>'),js=w('<div class="debug-info svelte-mi828q"><strong>\u6E05\u7406\u524D:</strong> </div>'),Ms=w('<div class="debug-details svelte-mi828q"><!> <!> <!></div>'),Ss=w('<div><div class="result-header svelte-mi828q"><div class="word-info svelte-mi828q"><span class="word-number svelte-mi828q"></span> <span class="target-word svelte-mi828q"> </span> <span class="chinese-meaning svelte-mi828q"> </span> <span class="difficulty-badge svelte-mi828q"> </span></div> <div class="result-score svelte-mi828q"><span class="score-value svelte-mi828q"> </span></div></div> <div class="result-details svelte-mi828q"><div class="recognition-result svelte-mi828q"><strong>\u8BC6\u522B\u7ED3\u679C:</strong> </div> <div> </div> <div class="method-info svelte-mi828q"><strong>\u65B9\u6CD5:</strong> </div> <div class="test-details svelte-mi828q"><!></div> <!></div></div>'),Ps=w('<div class="stats-summary svelte-mi828q"><div class="stat-item svelte-mi828q"><div class="stat-value svelte-mi828q"> </div> <div class="stat-label svelte-mi828q">\u6B63\u786E\u6570\u91CF</div></div> <div class="stat-item svelte-mi828q"><div class="stat-value svelte-mi828q"> </div> <div class="stat-label svelte-mi828q">\u51C6\u786E\u7387</div></div> <div class="stat-item svelte-mi828q"><div class="stat-value svelte-mi828q"> </div> <div class="stat-label svelte-mi828q">\u5E73\u5747\u8BC4\u5206</div></div> <div class="stat-item svelte-mi828q"><div class="stat-value svelte-mi828q"> </div> <div class="stat-label svelte-mi828q">\u5E73\u5747\u7F6E\u4FE1\u5EA6</div></div></div> <div class="method-stats svelte-mi828q"><h3 class="svelte-mi828q">\u8BC6\u522B\u65B9\u6CD5\u5206\u6790</h3> <div class="method-grid svelte-mi828q"><div class="method-item svelte-mi828q"><span class="method-label svelte-mi828q">\u76F4\u63A5\u5339\u914D</span> <span class="method-count svelte-mi828q"> </span></div> <div class="method-item svelte-mi828q"><span class="method-label svelte-mi828q">\u97F3\u7D20\u5339\u914D</span> <span class="method-count svelte-mi828q"> </span></div> <div class="method-item svelte-mi828q"><span class="method-label svelte-mi828q">\u8D85\u65F6</span> <span class="method-count svelte-mi828q"> </span></div> <!></div></div> <div class="difficulty-analysis svelte-mi828q"><h3 class="svelte-mi828q">\u96BE\u5EA6\u8868\u73B0\u5206\u6790</h3> <div class="difficulty-grid svelte-mi828q"></div></div> <div class="detailed-results-section svelte-mi828q"><h3 class="svelte-mi828q">\u{1F4DD} \u8BE6\u7EC6\u8BC6\u522B\u7ED3\u679C</h3> <div class="detailed-results-list svelte-mi828q"></div></div>',1),Rs=w('<div class="results-container svelte-mi828q"><div class="results-header svelte-mi828q"><h2 class="svelte-mi828q">\u{1F389} \u6D4B\u8BD5\u5B8C\u6210\uFF01</h2> <!></div> <div class="results-actions svelte-mi828q"><button class="secondary-btn svelte-mi828q">\u91CD\u65B0\u8BBE\u7F6E</button> <button class="primary-btn svelte-mi828q">\u518D\u6B21\u6D4B\u8BD5</button></div></div>'),Is=w('<div class="enhanced-speed-reading svelte-mi828q"><!></div>');function Ds(Fe,le){it(le,!1);const G=[{english:"hello",chinese:"\u4F60\u597D",category:"\u95EE\u5019",difficulty:1},{english:"world",chinese:"\u4E16\u754C",category:"\u57FA\u7840",difficulty:1},{english:"apple",chinese:"\u82F9\u679C",category:"\u6C34\u679C",difficulty:1},{english:"water",chinese:"\u6C34",category:"\u65E5\u5E38",difficulty:1},{english:"book",chinese:"\u4E66",category:"\u5B66\u4E60",difficulty:1},{english:"computer",chinese:"\u7535\u8111",category:"\u79D1\u6280",difficulty:2},{english:"school",chinese:"\u5B66\u6821",category:"\u6559\u80B2",difficulty:2},{english:"family",chinese:"\u5BB6\u5EAD",category:"\u5173\u7CFB",difficulty:2},{english:"friend",chinese:"\u670B\u53CB",category:"\u5173\u7CFB",difficulty:2},{english:"house",chinese:"\u623F\u5B50",category:"\u5EFA\u7B51",difficulty:2},{english:"beautiful",chinese:"\u7F8E\u4E3D\u7684",category:"\u5F62\u5BB9\u8BCD",difficulty:3},{english:"important",chinese:"\u91CD\u8981\u7684",category:"\u5F62\u5BB9\u8BCD",difficulty:3},{english:"different",chinese:"\u4E0D\u540C\u7684",category:"\u5F62\u5BB9\u8BCD",difficulty:3},{english:"language",chinese:"\u8BED\u8A00",category:"\u5B66\u4E60",difficulty:3},{english:"business",chinese:"\u5546\u4E1A",category:"\u5DE5\u4F5C",difficulty:3},{english:"pronunciation",chinese:"\u53D1\u97F3",category:"\u8BED\u8A00",difficulty:4},{english:"environment",chinese:"\u73AF\u5883",category:"\u81EA\u7136",difficulty:4},{english:"technology",chinese:"\u6280\u672F",category:"\u79D1\u6280",difficulty:4},{english:"opportunity",chinese:"\u673A\u4F1A",category:"\u62BD\u8C61",difficulty:4},{english:"development",chinese:"\u53D1\u5C55",category:"\u62BD\u8C61",difficulty:4},{english:"extraordinary",chinese:"\u975E\u51E1\u7684",category:"\u9AD8\u7EA7\u5F62\u5BB9\u8BCD",difficulty:5},{english:"responsibility",chinese:"\u8D23\u4EFB",category:"\u62BD\u8C61\u6982\u5FF5",difficulty:5},{english:"communication",chinese:"\u4EA4\u6D41",category:"\u793E\u4EA4",difficulty:5},{english:"understanding",chinese:"\u7406\u89E3",category:"\u8BA4\u77E5",difficulty:5},{english:"international",chinese:"\u56FD\u9645\u7684",category:"\u5730\u7406\u653F\u6CBB",difficulty:5}];let ne=fe(1),re=fe(10),u=fe([]),p=fe(!1),m=fe(null);function oe(){const c=[...G].sort(()=>Math.random()-.5);W(u,c.slice(0,e(re)).map((k,f)=>({...k,wid:`word_${f}`,index:f,offset:49*f})))}function H(){oe(),W(p,!0)}function ge(){W(p,!1),W(m,null)}function D(){W(p,!1),W(m,null)}function d(){if(!e(m))return null;const c=e(m).length,k=e(m).filter(g=>g.matched).length,f=c-k,F=c>0?(k/c*100).toFixed(1):0,J=c>0?(e(m).reduce((g,B)=>g+(B.confidence||0),0)/c*100).toFixed(1):0,ce={direct:e(m).filter(g=>g.method==="direct").length,phoneme:e(m).filter(g=>g.method==="phoneme").length,timeout:e(m).filter(g=>g.method==="timeout").length,unknown:e(m).filter(g=>g.method==="unknown"||!g.method).length},se=c>0?(e(m).reduce((g,B)=>g+(B.score||0),0)/c).toFixed(1):0,ae=e(m).map(g=>{const B=G.find(ee=>ee.english===g.refText);return{word:g.refText,chinese:(B==null?void 0:B.chinese)||"",difficulty:(B==null?void 0:B.difficulty)||0,recognized:g.asrResult||"\u672A\u8BC6\u522B",matched:g.matched,confidence:Math.round(100*(g.confidence||0)),score:g.score||0,method:g.method||"unknown",details:g.details,timestamp:g.timestamp}}),de=ae.filter(g=>!g.matched),K={};return e(m).forEach(g=>{const B=G.find(ee=>ee.english===g.refText);if(B){const ee=B.difficulty;K[ee]||(K[ee]={total:0,correct:0}),K[ee].total++,g.matched&&K[ee].correct++}}),{total:c,correct:k,wrong:f,accuracy:F,avgConfidence:J,avgScore:se,methodStats:ce,wrongWords:de,difficultyStats:K,detailedResults:ae}}mt(()=>{oe()}),nt();var _=Is();zt(c=>{At.title="\u589E\u5F3A\u79D2\u8BCD\u6D4B\u8BD5"});var ve=s(_),X=c=>{var k=hs(),f=i(s(k),2),F=s(f),J=i(s(F),2);He(J,4,()=>[1,2,3,4,5],Ke,(r,y)=>{var q=cs();let V;var R=s(q);t(q),A(L=>{V=Ae(q,1,"difficulty-btn svelte-mi828q",null,V,L),h(R,`\u7B49\u7EA7 ${y!=null?y:""}`)},[()=>({active:e(ne)===y})],ye),Ee("click",q,()=>{W(ne,y),oe()}),o(r,q)}),t(J);var ce=i(J,2),se=s(ce),ae=r=>{var y=We("\u57FA\u7840\u8BCD\u6C47 - \u65E5\u5E38\u751F\u6D3B\u5E38\u7528\u8BCD");o(r,y)},de=(r,y)=>{var q=R=>{var L=We("\u8FDB\u9636\u8BCD\u6C47 - \u5B66\u4E60\u5DE5\u4F5C\u5E38\u7528\u8BCD");o(R,L)},V=(R,L)=>{var qe=pe=>{var xe=We("\u4E2D\u7EA7\u8BCD\u6C47 - \u5F62\u5BB9\u8BCD\u548C\u62BD\u8C61\u6982\u5FF5");o(pe,xe)},Se=(pe,xe)=>{var Pe=ue=>{var Te=We("\u9AD8\u7EA7\u8BCD\u6C47 - \u4E13\u4E1A\u672F\u8BED\u548C\u590D\u6742\u6982\u5FF5");o(ue,Te)},Re=ue=>{var Te=We("\u6311\u6218\u8BCD\u6C47 - \u9AD8\u96BE\u5EA6\u957F\u5355\u8BCD");o(ue,Te)};T(pe,ue=>{e(ne)===4?ue(Pe):ue(Re,!1)},xe)};T(R,pe=>{e(ne)===3?pe(qe):pe(Se,!1)},L)};T(r,R=>{e(ne)===2?R(q):R(V,!1)},y)};T(se,r=>{e(ne)===1?r(ae):r(de,!1)}),t(ce),t(F);var K=i(F,2),g=i(s(K),2);He(g,4,()=>[5,10,15,20,25],Ke,(r,y)=>{var q=ds();let V;var R=s(q);t(q),A(L=>{V=Ae(q,1,"count-btn svelte-mi828q",null,V,L),h(R,`${y!=null?y:""} \u4E2A`)},[()=>({active:e(re)===y})],ye),Ee("click",q,()=>{W(re,y),oe()}),o(r,q)}),t(g),t(K);var B=i(K,2),ee=s(B),be=s(ee);t(ee);var Me=i(ee,2),$=r=>{var y=us();He(y,5,()=>e(u),Ke,(q,V,R)=>{var L=vs();Ae(L,1,"preview-word svelte-mi828q",null,{},{highlight:R<5});var qe=s(L);qe.textContent=`${R+1}.`;var Se=i(qe,2),pe=s(Se,!0);t(Se);var xe=i(Se,2),Pe=s(xe,!0);t(xe);var Re=i(xe,2),ue=s(Re);t(Re),t(L),A(()=>{var Te;h(pe,e(V).english),h(Pe,e(V).chinese),h(ue,`\u96BE\u5EA6${(Te=e(V).difficulty)!=null?Te:""}`)}),o(q,L)}),t(y),o(r,y)},O=r=>{var y=ms();o(r,y)};T(Me,r=>{e(u).length>0?r($):r(O,!1)}),t(B);var b=i(B,2),U=s(b);ze(2),t(b),t(f),t(k),A(()=>{var r;return h(be,`\u9884\u89C8\u5355\u8BCD\u5217\u8868 (${(r=e(u).length)!=null?r:""} \u4E2A)`)}),Ee("click",U,H),o(c,k)},Z=(c,k)=>{var f=J=>{os(J,{get dataSource(){return e(u)},repeatTimes:2,$$events:{finish:ce=>{return se=ce.detail,W(m,se),void W(p,!1);var se},quit:D}})},F=(J,ce)=>{var se=ae=>{var de=Rs(),K=s(de),g=i(s(K),2),B=$=>{var O=Ps();const b=ye(d);var U=ht(O),r=s(U),y=s(r),q=s(y);t(y),ze(2),t(r);var V=i(r,2),R=s(V),L=s(R);t(R),ze(2),t(V);var qe=i(V,2),Se=s(qe),pe=s(Se,!0);t(Se),ze(2),t(qe);var xe=i(qe,2),Pe=s(xe),Re=s(Pe);t(Pe),ze(2),t(xe),t(U);var ue=i(U,2),Te=i(s(ue),2),Ze=s(Te),Ne=i(s(Ze),2),rt=s(Ne,!0);t(Ne),t(Ze);var Ye=i(Ze,2),Qe=i(s(Ye),2),Ge=s(Qe,!0);t(Qe),t(Ye);var Je=i(Ye,2),et=i(s(Je),2),ot=s(et,!0);t(et),t(Je);var ct=i(Je,2),n=S=>{var l=fs(),x=i(s(l),2),v=s(x,!0);t(x),t(l),A(()=>h(v,e(b).methodStats.unknown)),o(S,l)};T(ct,S=>{e(b).methodStats.unknown>0&&S(n)}),t(Te),t(ue);var a=i(ue,2),j=i(s(a),2);He(j,5,()=>Object.entries(e(b).difficultyStats),Ke,(S,l)=>{let x=()=>e(l)[1];var v=gs(),te=s(v),C=s(te);t(te);var I=i(te,2),E=s(I);t(I),t(v),A(we=>{var Oe,ie,Ie;h(C,`\u96BE\u5EA6 ${(Oe=e(l)[0])!=null?Oe:""}`),h(E,`${(ie=x().correct)!=null?ie:""}/${(Ie=x().total)!=null?Ie:""}
                    (${we!=null?we:""}%)`)},[()=>(x().correct/x().total*100).toFixed(1)],ye),o(S,v)}),t(j),t(a);var M=i(a,2),Q=i(s(M),2);He(Q,5,()=>e(b).detailedResults,Ke,(S,l,x)=>{var v=Ss();let te;var C=s(v),I=s(C),E=s(I);E.textContent=`${x+1}.`;var we=i(E,2),Oe=s(we,!0);t(we);var ie=i(we,2),Ie=s(ie);t(ie);var tt=i(ie,2),Ct=s(tt);t(tt),t(I);var wt=i(I,2),yt=s(wt),$t=s(yt,!0);t(yt),t(wt),t(C);var qt=i(C,2),dt=s(qt),jt=i(s(dt));t(dt);var st=i(dt,2);let xt;var Mt=s(st);t(st);var vt=i(st,2),St=i(s(vt));t(vt);var ut=i(vt,2),Pt=s(ut),Rt=me=>{var Ce=xs(),$e=s(Ce),Ve=P=>{var z=ps(),ke=i(s(z));t(z),A(Ue=>h(ke,` [${Ue!=null?Ue:""}]`),[()=>e(l).details.asrPhonemes.join(", ")],ye),o(P,z)},he=P=>{var z=ws();o(P,z)};T($e,P=>{e(l).details.asrPhonemes&&e(l).details.asrPhonemes.length>0?P(Ve):P(he,!1)});var je=i($e,2),Le=P=>{var z=ys(),ke=i(s(z));t(z),A(Ue=>h(ke,` [${Ue!=null?Ue:""}]`),[()=>e(l).details.targetPhonemes.join(", ")],ye),o(P,z)},_e=P=>{var z=qs();o(P,z)};T(je,P=>{e(l).details.targetPhonemes&&e(l).details.targetPhonemes.length>0?P(Le):P(_e,!1)});var N=i(je,2),Y=i(s(N));t(N),t(Ce),A(P=>h(Y,` ${P!=null?P:""}`),[()=>e(l).details.similarity!==void 0?(100*e(l).details.similarity).toFixed(1)+"%":"\u65E0\u6570\u636E"],ye),o(me,Ce)},It=(me,Ce)=>{var $e=he=>{var je=ks();o(he,je)},Ve=(he,je)=>{var Le=N=>{var Y=bs(),P=i(s(Y),4),z=i(s(P));t(P),t(Y),A(()=>{var ke;return h(z,` ${(ke=e(l).confidence)!=null?ke:""}%`)}),o(N,Y)},_e=N=>{var Y=Ts(),P=i(s(Y),4),z=i(s(P));t(P),t(Y),A(()=>{var ke;return h(z,` ${(ke=e(l).confidence)!=null?ke:""}%`)}),o(N,Y)};T(he,N=>{e(l).method==="direct"?N(Le):N(_e,!1)},je)};T(me,he=>{e(l).method==="timeout"?he($e):he(Ve,!1)},Ce)};T(Pt,me=>{e(l).details&&(e(l).details.asrPhonemes||e(l).details.targetPhonemes)?me(Rt):me(It,!1)}),t(ut);var Dt=i(ut,2),Bt=me=>{var Ce=Ms(),$e=s(Ce),Ve=N=>{var Y=Cs(),P=i(s(Y));t(Y),A(()=>{var z;return h(P,` ${(z=e(l).details.processingTime)!=null?z:""}ms`)}),o(N,Y)};T($e,N=>{e(l).details.processingTime&&N(Ve)});var he=i($e,2),je=N=>{var Y=$s(),P=i(s(Y));t(Y),A(()=>{var z,ke;return h(P,` "${(z=e(l).details.baseContentUsed)!=null?z:""}" \u2192 "${(ke=e(l).recognized)!=null?ke:""}"`)}),o(N,Y)};T(he,N=>{e(l).details.baseContentUsed&&N(je)});var Le=i(he,2),_e=N=>{var Y=js(),P=i(s(Y));t(Y),A(()=>{var z;return h(P,` "${(z=e(l).details.originalBeforeClean)!=null?z:""}"`)}),o(N,Y)};T(Le,N=>{e(l).details.originalBeforeClean&&e(l).details.originalBeforeClean!==e(l).recognized&&N(_e)}),t(Ce),o(me,Ce)};T(Dt,me=>{e(l).details&&(e(l).details.processingTime||e(l).details.baseContentUsed)&&me(Bt)}),t(qt),t(v),A((me,Ce,$e)=>{var Ve,he,je,Le,_e;te=Ae(v,1,"result-item svelte-mi828q",null,te,me),h(Oe,e(l).word),h(Ie,`(${(Ve=e(l).chinese)!=null?Ve:""})`),h(Ct,`\u96BE\u5EA6${(he=e(l).difficulty)!=null?he:""}`),h($t,e(l).score),h(jt,` ${e(l).recognized||"\u672A\u8BC6\u522B"}`),xt=Ae(st,1,"match-status svelte-mi828q",null,xt,Ce),h(Mt,`${(je=e(l).matched?"\u2713":"\u2717")!=null?je:""} ${(Le=e(l).matched?"\u5339\u914D\u6210\u529F":"\u5339\u914D\u5931\u8D25")!=null?Le:""} \u7F6E\u4FE1\u5EA6: ${(_e=e(l).confidence)!=null?_e:""}%`),h(St,` ${$e!=null?$e:""}`)},[()=>({correct:e(l).matched,wrong:!e(l).matched}),()=>({success:e(l).matched,failure:!e(l).matched}),()=>function(me){switch(me){case"direct":return"\u76F4\u63A5\u6587\u672C\u5339\u914D";case"phoneme":return"\u97F3\u7D20\u5339\u914D";case"timeout":return"\u8BC6\u522B\u8D85\u65F6";case"final_timeout":return"\u6700\u7EC8\u8D85\u65F6";case"unknown":return"\u672A\u77E5\u65B9\u6CD5";default:return me||"\u672A\u77E5"}}(e(l).method)],ye),o(S,v)}),t(Q),t(M),A(()=>{var S,l,x,v;h(q,`${(S=e(b).correct)!=null?S:""}/${(l=e(b).total)!=null?l:""}`),h(L,`${(x=e(b).accuracy)!=null?x:""}%`),h(pe,e(b).avgScore),h(Re,`${(v=e(b).avgConfidence)!=null?v:""}%`),h(rt,e(b).methodStats.direct),h(Ge,e(b).methodStats.phoneme),h(ot,e(b).methodStats.timeout)}),o($,O)};T(g,$=>{d()&&$(B)}),t(K);var ee=i(K,2),be=s(ee),Me=i(be,2);t(ee),t(de),Ee("click",be,ge),Ee("click",Me,H),o(ae,de)};T(J,ae=>{e(m)&&ae(se)},ce)};T(c,J=>{e(p)?J(f):J(F,!1)},k)};T(ve,c=>{e(p)||e(m)?c(Z,!1):c(X)}),t(_),o(Fe,_),at()}export{Ds as component,Et as universal};
