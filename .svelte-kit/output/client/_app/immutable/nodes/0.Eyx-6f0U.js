import{p as e,i as t,t as a,N as s,v as r,x as n,X as i,Y as o,r as l,e as c,Z as d,h as u,j as v,k as f,g,n as h,z as p,u as m,_ as w,a0 as x,a as b,c as y,y as k,f as $,H as L,F as j,G as I,a1 as S,o as C,l as M,d as z,a2 as E,s as B,m as N,a3 as q,a4 as A,A as O,a5 as J,a6 as D,K as F,L as G}from"../chunks/B1xmz3ZD.js";import{E as H}from"../chunks/DD5itpR6.js";import{n as K,r as R,s as U,b as X,p as Y,m as Z,u as _,i as P,d as Q,e as T,f as V}from"../chunks/BoaXofz5.js";import"../chunks/DuTLMzPI.js";import{g as W,i as ee}from"../chunks/CwJ_S9-I.js";var te=a('<div><div class="notification-icon shrink-0"><!></div> <div class="notification-content grow"><div class="message"> </div></div> <button class="notification-close shrink-0 text-white hover:text-gray-200" aria-label="关闭通知"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button></div>'),ae=a('<div class="fixed top-4 right-4 z-50 flex flex-col gap-2 w-80"></div>');var se=a('<span class="level-indicator mr-1 svelte-1qk395a"> </span>'),re=a('<span><i class="fas fa-chevron-down text-xs"></i></span>'),ne=a('<ul class="menu-sub space-y-1 mt-1 text-sm relative svelte-1qk395a"><div></div> <!></ul>'),ie=a('<li class="menu-item relative"><div><!> <span class="flex-grow truncate"> </span> <!></div> <!></li>');function oe(a,s){e(s,!1);let i=b(s,"item",8),o=b(s,"level",8,0),d=b(s,"isExpanded",8,!1),f=b(s,"isSelected",8,!1),g=b(s,"isLastItem",8,!1);const w=y();function x(){w("toggle",{id:i().id})}function j(){w("select",i())}const I=.5+1*o();t();var S=ie(),C=c(S);k(C,`padding-left: ${I??""}rem;`);var M=c(C),z=e=>{var t=se(),a=c(t,!0);l(t),v(()=>p(a,g()?"└─":"├─")),r(e,t)};$(M,e=>{o()>0&&e(z)});var E=u(M,2),B=c(E,!0);l(E);var N=u(E,2),q=e=>{var t=re();v(()=>h(t,1,`transform transition-transform duration-200 ${(d()?"rotate-180":"")??""}`)),r(e,t)};$(N,e=>{i().children&&i().children.length>0&&e(q)}),l(C);var A=u(C,2),O=e=>{var t=ne(),a=c(t),n=u(a,2);L(n,s,"default",{}),l(t),v(()=>h(a,1,`vertical-line ${(g()?"shorter-line":"")??""}`,"svelte-1qk395a")),r(e,t)};$(A,e=>{i().children&&d()&&e(O)}),l(S),v(()=>{h(C,1,`flex items-center pr-3 py-2 text-white hover:bg-blue-700 dark:hover:bg-gray-700 rounded-md cursor-pointer transition-colors ${(f()?"bg-blue-700 dark:bg-gray-700":"")??""}`),p(B,i().name)}),m("click",C,function(...e){(i().children?x:j)?.apply(this,e)}),r(a,S),n()}var le=a('<ul class="menu-list space-y-1 text-sm"></ul>');function ce(a,c){e(c,!1);const[d,u]=i(),v=()=>o(X,"$selectedMenuItem",d);let h=b(c,"items",24,()=>[]),p=b(c,"level",8,0),m=b(c,"expandedState",28,()=>({}));const w=y();function x(e){if(!e||!e.detail)return;const t=e.detail.id;m({...m(),[t]:!m()[t]})}function k(e){if(!e||!e.detail)return;const t=e.detail;console.log("菜单项选择:",t),t.children&&0!==t.children.length?(console.log("切换展开状态:",t.name),x({detail:{id:t.id}})):(console.log("选择叶子节点:",t.name,t.mcode),U(t),w("select",t))}t();var L=le();s(L,7,()=>h()||[],e=>e?.id,(e,t,a)=>{var s=j(),n=I(s),i=e=>{const s=f(()=>m()[g(t).id]||!1),n=f(()=>function(e){return v()&&e&&v().id===e.id}(g(t))),i=f(()=>g(a)===h().length-1);oe(e,{get item(){return g(t)},get level(){return p()},get isExpanded(){return g(s)},get isSelected(){return g(n)},get isLastItem(){return g(i)},$$events:{toggle:x,select:k},children:(e,a)=>{var s=j(),n=I(s),i=e=>{var a=j(),s=I(a);const n=f(()=>p()+1);ce(s,{get items(){return g(t).children},get level(){return g(n)},get expandedState(){return m()},$$events:{select(e){S.call(this,c,e)}}}),r(e,a)};$(n,e=>{g(t).children&&g(t).children.length>0&&m()[g(t).id]&&e(i)}),r(e,s)},$$slots:{default:!0}})};$(n,e=>{g(t)&&e(i)}),r(e,s)}),l(L),r(a,L),n(),u()}var de=a('<button class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-300 dark:text-gray-400 hover:text-white"><i class="fas fa-times text-xs"></i></button>'),ue=a('<div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-300 dark:text-gray-400"><i class="fas fa-search text-xs"></i></div>'),ve=a("<span><i></i> </span>"),fe=a('<button class="px-3 py-1 text-sm bg-green-500 text-white rounded-full shadow-md flex items-center hover:bg-green-600 transition-colors"><i class="fas fa-sign-in-alt mr-1"></i> 登录</button>'),ge=a('<div class="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"></div>'),he=a('<div><div class="flex-1 flex flex-col bg-gradient-to-b from-blue-800 to-blue-900 dark:from-gray-800 dark:to-gray-900 shadow-lg overflow-hidden h-full"><div class="flex items-center justify-between p-4 border-b border-blue-700 dark:border-gray-700"><div class="flex items-center"><div class="text-xl font-bold text-white"> </div></div> <button class="p-2 rounded-full text-white hover:bg-blue-700 dark:hover:bg-gray-700 transition-colors"><i class="fas fa-arrow-left"></i></button></div> <div class="px-3 py-2 border-b border-blue-700 dark:border-gray-700"><div class="relative w-full"><input type="text" placeholder="搜索目录..." class="w-full py-1 px-3 pr-8 bg-blue-700 dark:bg-gray-700 text-white rounded-md placeholder-blue-300 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-400 dark:focus:ring-gray-400 text-sm"> <!></div></div> <div class="flex justify-between px-3 py-2 border-b border-blue-700 dark:border-gray-700"><button class="text-xs text-white opacity-80 hover:opacity-100 transition-opacity">展开全部</button> <button class="text-xs text-white opacity-80 hover:opacity-100 transition-opacity">折叠全部</button></div> <div class="flex-1 overflow-y-auto py-4 px-3 pb-0 drawer-content svelte-1xe7niy"><!> <div class="h-4"></div></div></div> <div class="bg-gradient-to-b from-blue-700 to-blue-900 dark:from-gray-700 dark:to-gray-900 py-2 border-t border-blue-700 dark:border-gray-700 shadow-inner w-full"><div class="flex justify-center"><!></div></div></div> <!>',1);function pe(a,s){e(s,!1);const[d,f]=i(),w=()=>o(Y,"$partnerName",d),x=()=>o(Z,"$menuItems",d),y=()=>o(P,"$isDrawerOpen",d),k=()=>o(_,"$user",d),L=()=>o(Q,"$isAdmin",d),j=N();let F=b(s,"title",8,"赶考小状学习机功能模块介绍"),G=N({}),H=N(""),K=N([]);function R(e,t){return e?e.map(e=>{const a=e.name&&e.name.toLowerCase().includes(t);let s=[];return e.children&&e.children.length>0&&(s=R(e.children,t)),a||s.length>0?(s.length>0&&""!==t&&J(G,g(G)[e.id]=!0),{...e,children:s.length>0?s:e.children}):null}).filter(Boolean):[]}function U(){B(H,"")}function X(){D(P,!1)}function T(){const e=window.location.href,t=`https://www.gankao.com/user/login?redirect=${encodeURIComponent(e)}`;window.location.href=t}function V(){try{localStorage.setItem("menu-expanded-state",JSON.stringify(g(G)))}catch(e){console.error("保存菜单状态失败:",e)}}C(()=>(function(){try{const e=localStorage.getItem("menu-expanded-state");e&&B(G,JSON.parse(e))}catch(e){console.error("加载菜单状态失败:",e)}}(),window.addEventListener("beforeunload",V),()=>{window.removeEventListener("beforeunload",V)})),M(()=>(w(),q(F())),()=>{B(j,w()?`[${w()}]功能模块介绍`:F())}),M(()=>(g(H),x()),()=>{""===g(H).trim()?B(K,x()):B(K,R(x(),g(H).toLowerCase()))}),z(),t();var W=he(),ee=I(W),te=c(ee),ae=c(te),se=c(ae),re=c(se),ne=c(re,!0);l(re),l(se);var ie=u(se,2);l(ae);var oe=u(ae,2),le=c(oe),pe=c(le);A(pe);var me=u(pe,2),we=e=>{var t=de();m("click",t,U),r(e,t)},xe=e=>{var t=ue();r(e,t)};$(me,e=>{g(H)?e(we):e(xe,!1)}),l(le),l(oe);var be=u(oe,2),ye=c(be),ke=u(ye,2);l(be);var $e=u(be,2);ce(c($e),{get items(){return g(K)},get expandedState(){return g(G)},set expandedState(e){B(G,e)},$$events:{select(e){S.call(this,s,e)}},$$legacy:!0}),O(2),l($e),l(te);var Le=u(te,2),je=c(Le),Ie=c(je),Se=e=>{var t=ve(),a=c(t),s=u(a);l(t),v(()=>{h(t,1,`px-3 py-1 text-sm ${(L()?"bg-red-500":"bg-blue-500")??""} text-white rounded-full shadow-md flex items-center`),h(a,1,`fas ${(L()?"fa-shield-alt":"fa-user")??""} mr-1`),p(s,` ${k().nickname||"用户"}`)}),r(e,t)},Ce=e=>{var t=fe();m("click",t,T),r(e,t)};$(Ie,e=>{k().uid?e(Se):e(Ce,!1)}),l(je),l(Le),l(ee);var Me=u(ee,2),ze=e=>{var t=ge();m("click",t,X),r(e,t)};$(Me,e=>{y()&&e(ze)}),v(()=>{h(ee,1,`fixed inset-y-0 left-0 w-4/5 max-w-xs transform ${(y()?"translate-x-0":"-translate-x-full")??""} transition-transform duration-300 ease-in-out z-50 flex flex-col`),p(ne,g(j))}),m("click",ie,X),E(pe,()=>g(H),e=>B(H,e)),m("click",ye,function(){const e=t=>{t.forEach(t=>{t.id&&J(G,g(G)[t.id]=!0),t.children&&t.children.length>0&&e(t.children)})};e(x()),B(G,{...g(G)})}),m("click",ke,function(){B(G,{})}),r(a,W),n(),f()}var me=a("<!> <!> <!>",1);function we(a,b){e(b,!1),C(async()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css",document.head.appendChild(e);try{const e=await W();console.log("当前用户信息:",e),e&&e.uid?(T(e),window.userInfo={...e,isAdmin:await ee()}):(V(),window.userInfo=null,console.info("用户未登录"))}catch(t){console.error("初始化用户信息失败:",t),V(),window.userInfo=null}}),t();var y=me();F(e=>{G.title="小状元学习机"});var k=I(y);H(k,{children:(e,t)=>{var a=j(),s=I(a);L(s,b,"default",{}),r(e,a)},$$slots:{default:!0}});var $=u(k,2);pe($,{$$events:{select:function(e){console.log("Layout接收到菜单选择事件:",e.detail)}}}),function(a,b){e(b,!1);const[y,k]=i();t();var $=ae();s($,5,()=>o(K,"$notifications",y),e=>e.id,(e,t)=>{var a=te(),s=c(a),n=c(s);d(n,()=>function(e){switch(e){case"success":return'<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\n          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />\n        </svg>';case"error":return'<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\n          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />\n        </svg>';case"warning":return'<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\n          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />\n        </svg>';default:return'<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\n          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />\n        </svg>'}}(g(t).type)),l(s);var i=u(s,2),o=c(i),b=c(o,!0);l(o),l(i);var y=u(i,2);l(a),v(e=>{h(a,1,`notification-item px-4 py-3 rounded-lg shadow-lg text-white flex items-start gap-3 ${e??""}`,"svelte-awgio6"),p(b,g(t).message)},[()=>function(e){switch(e){case"success":return"bg-green-500";case"error":return"bg-red-500";case"warning":return"bg-yellow-500";default:return"bg-blue-500"}}(g(t).type)],f),m("click",y,()=>R(g(t).id)),w(3,a,()=>x,()=>({y:-20,duration:300})),r(e,a)}),l($),r(a,$),n(),k()}(u($,2),{}),r(a,y),n()}export{we as component};
