import{p as e,o as t,i as a,t as o,K as s,G as r,f as n,v as l,x as d,L as i,g as c,m as g,s as v,h as u,e as x,r as b,A as m,I as p,j as y,k as h,z as f,n as w,u as T,M as $,N as j,O as k}from"../chunks/B1xmz3ZD.js";import{E as z}from"../chunks/Do6EixEe.js";const V=Object.freeze(Object.defineProperty({__proto__:null,load:async function(){return{title:"增强语音识别测试",description:"基于音素匹配的智能语音识别系统测试页面"}},prerender:!1,ssr:!1},Symbol.toStringTag,{value:"Module"}));var S=o('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"><strong>初始化失败:</strong> </div> <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">重新加载</button>',1),R=o('<div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">正在初始化语音识别系统，请稍候...</div>'),I=o('<div class="text-center mb-8"><!></div>'),A=o('<button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-6 rounded">清除结果</button>'),P=o('<div class="bg-white rounded-lg shadow-md p-6 mb-6"><div class="flex justify-between items-center mb-4"><h2 class="text-xl font-semibold text-gray-800"> </h2> <div class="text-sm text-gray-600"> </div></div> <div class="flex justify-center items-center space-x-4 mb-6"><button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded disabled:opacity-50">← 上一个</button> <div class="text-center"><div class="text-2xl font-bold text-blue-600 mb-1"> </div> <div class="text-sm text-gray-500">请朗读这个单词</div></div> <button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded disabled:opacity-50">下一个 →</button></div> <div class="flex justify-center"><!></div></div> <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"><div class="bg-blue-100 rounded-lg p-4 text-center"><div class="text-2xl font-bold text-blue-600"> </div> <div class="text-sm text-blue-800">总测试次数</div></div> <div class="bg-green-100 rounded-lg p-4 text-center"><div class="text-2xl font-bold text-green-600"> </div> <div class="text-sm text-green-800">匹配成功率</div></div> <div class="bg-purple-100 rounded-lg p-4 text-center"><div class="text-2xl font-bold text-purple-600"> </div> <div class="text-sm text-purple-800">平均置信度</div></div></div> <div class="flex justify-center space-x-4 mb-6"><button>查看详细结果</button> <!></div>',1),D=o('<p class="text-gray-500 text-center py-8">暂无测试结果</p>'),F=o('<div class="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded"><div> </div> <div> </div> <div> </div></div>'),M=o('<div><div class="flex justify-between items-start mb-2"><div><span class="font-semibold"> </span> <span class="text-sm text-gray-600 ml-2"> </span></div> <div class="text-sm text-gray-500"> </div></div> <div class="mb-2"><span class="text-sm text-gray-600">识别结果:</span> <span class="font-medium"> </span></div> <div class="flex justify-between items-center text-sm"><div><span> </span> <span class="text-gray-600 ml-2"> </span></div> <div class="text-gray-500"> </div></div> <!></div>'),O=o('<div class="space-y-3"></div>'),_=o('<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 svelte-tc24ut"><div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white"><div class="flex justify-between items-center mb-4"><h3 class="text-lg font-bold text-gray-900">测试结果详情</h3> <button class="text-gray-400 hover:text-gray-600"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div> <div class="max-h-96 overflow-y-auto"><!></div></div></div>'),L=o('<div class="container mx-auto px-4 py-8 max-w-4xl"><div class="text-center mb-8"><h1 class="text-3xl font-bold text-gray-800 mb-2">增强语音识别测试</h1> <p class="text-gray-600">基于音素匹配的智能语音识别系统</p></div> <!></div> <!>',1);function C(o,V){e(V,!1);const C=[{word:"hello",language:"en",category:"基础词汇"},{word:"world",language:"en",category:"基础词汇"},{word:"apple",language:"en",category:"水果"},{word:"banana",language:"en",category:"水果"},{word:"computer",language:"en",category:"科技"},{word:"phone",language:"en",category:"科技"},{word:"school",language:"en",category:"教育"},{word:"book",language:"en",category:"教育"},{word:"water",language:"en",category:"日常"},{word:"house",language:"en",category:"日常"},{word:"cat",language:"en",category:"动物"},{word:"dog",language:"en",category:"动物"},{word:"red",language:"en",category:"颜色"},{word:"blue",language:"en",category:"颜色"},{word:"green",language:"en",category:"颜色"},{word:"你好",language:"zh",category:"问候"},{word:"谢谢",language:"zh",category:"礼貌"},{word:"苹果",language:"zh",category:"水果"},{word:"香蕉",language:"zh",category:"水果"},{word:"电脑",language:"zh",category:"科技"}];let B=g(),E=g(0),G=g(C[0]),K=g([]),N=g(!1),W=g(!1),q=g(null),H=g(0),J=0,Q=0;function U(){c(E)<C.length-1&&($(E),v(G,C[c(E)]))}function X(){c(E)>0&&($(E,-1),v(G,C[c(E)]))}function Y(){console.log("[VoiceTest] 开始录音")}function Z(e){const{result:t,matchResult:a,analysisDetails:o}=e.detail;console.log("[VoiceTest] 录音停止:",{result:t,match:a,details:o}),t&&function(e,t,a){const o={word:c(G).word,language:c(G).language,category:c(G).category,asrResult:e,matched:t?.matched||!1,confidence:t?.confidence||0,method:t?.method||"unknown",details:a,timestamp:(new Date).toISOString()};v(K,[...c(K),o]),ae()}(t,a,o)}function ee(e){const{content:t}=e.detail}function te(e){const{matchResult:t,analysisDetails:a}=e.detail;console.log("[VoiceTest] 匹配完成:",{match:t,details:a})}function ae(){if(v(H,c(K).length),J=c(K).filter(e=>e.matched).length,c(H)>0){const e=c(K).reduce((e,t)=>e+t.confidence,0);Q=e/c(H)}}function oe(){v(N,!c(N))}function se(){v(K,[]),ae()}function re(e){const{error:t}=e.detail;console.error("[VoiceTest] ASR错误:",t),alert("语音识别错误: "+t.message)}function ne(){return c(H)>0?(J/c(H)*100).toFixed(1):0}function le(){return(100*Q).toFixed(1)}t(()=>{setTimeout(async()=>{try{console.log("[VoiceTest] 开始预初始化ASR组件"),console.log("[VoiceTest] enhancedASR组件引用:",c(B)),c(B)&&c(B).preInitialize?(console.log("[VoiceTest] 调用preInitialize方法"),c(B).preInitialize().then(e=>{console.log("[VoiceTest] preInitialize结果:",e),v(W,e),e?console.log("[VoiceTest] ASR组件初始化成功"):v(q,"初始化失败，请检查麦克风权限")}).catch(e=>{console.error("[VoiceTest] 预初始化失败:",e),v(q,e.message||"初始化失败"),v(W,!0)})):(console.error("[VoiceTest] enhancedASR组件引用为空或缺少preInitialize方法"),console.log("[VoiceTest] enhancedASR:",c(B)),console.log("[VoiceTest] enhancedASR.preInitialize:",c(B)?.preInitialize),v(q,"ASR组件未正确加载"),v(W,!0))}catch(e){console.error("[VoiceTest] 预初始化异常:",e),v(q,e.message||"初始化失败"),v(W,!0)}},1e3)}),a();var de=L();s(e=>{i.title="增强语音识别测试"});var ie=r(de),ce=u(x(ie),2),ge=e=>{var t=I(),a=x(t),o=e=>{var t=S(),a=r(t),o=u(x(a));b(a);var s=u(a,2);y(()=>f(o,` ${c(q)??""}`)),T("click",s,()=>window.location.reload()),l(e,t)},s=e=>{var t=R();l(e,t)};n(a,e=>{c(q)?e(o):e(s,!1)}),b(t),l(e,t)},ve=e=>{var t=P(),a=r(t),o=x(a),s=x(o),d=x(s);b(s);var i=u(s,2),g=x(i);b(i),b(o);var $=u(o,2),j=x($),k=u(j,2),V=x(k),S=x(V,!0);b(V),m(2),b(k);var R=u(k,2);b($);var I=u($,2),D=x(I);p(z(D,{get targetWord(){return c(G).word},get language(){return c(G).language},duration:10,direction:"top",showTextPanel:!1,$$events:{start:Y,stop:Z,textInput:ee,matchCompleted:te,error:re},$$legacy:!0}),e=>v(B,e),()=>c(B)),b(I),b(a);var F=u(a,2),M=x(F),O=x(M),_=x(O,!0);b(O),m(2),b(M);var L=u(M,2),W=x(L),q=x(W);b(W),m(2),b(L);var J=u(L,2),Q=x(J),ae=x(Q);b(Q),m(2),b(J),b(F);var de=u(F,2),ie=x(de);let ce;var ge=u(ie,2),ve=e=>{var t=A();T("click",t,se),l(e,t)};n(ge,e=>{c(K).length>0&&e(ve)}),b(de),y((e,t,a)=>{f(d,`当前测试 (${c(E)+1}/${C.length??""})`),f(g,`分类: ${c(G).category??""} | 语言: ${("en"===c(G).language?"英文":"中文")??""}`),j.disabled=0===c(E),f(S,c(G).word),R.disabled=c(E)===C.length-1,f(_,c(H)),f(q,`${e??""}%`),f(ae,`${t??""}%`),ce=w(ie,1,"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-6 rounded",null,ce,a),ie.disabled=c(N)},[ne,le,()=>({"opacity-50":c(N)})],h),T("click",j,X),T("click",R,U),T("click",ie,oe),l(e,t)};n(ce,e=>{c(W)?e(ve,!1):e(ge)}),b(ie);var ue=u(ie,2),xe=e=>{var t=_(),a=x(t),o=x(a),s=u(x(o),2);b(o);var r=u(o,2),d=x(r),i=e=>{var t=D();l(e,t)},g=e=>{var t=O();j(t,5,()=>c(K),k,(e,t)=>{var a=M();let o;var s=x(a),r=x(s),d=x(r),i=x(d);b(d);var g=u(d,2),v=x(g);b(g),b(r);var m=u(r,2),p=x(m,!0);b(m),b(s);var T=u(s,2),$=u(x(T),2),j=x($,!0);b($),b(T);var k=u(T,2),z=x(k),V=x(z);let S;var R=x(V,!0);b(V);var I=u(V,2),A=x(I);b(I),b(z);var P=u(z,2),D=x(P);b(P),b(k);var O=u(k,2),_=e=>{var a=F(),o=x(a),s=x(o);b(o);var r=u(o,2),n=x(r);b(r);var d=u(r,2),i=x(d);b(d),b(a),y((e,t,a)=>{f(s,`识别音素: [${e??""}]`),f(n,`目标音素: [${t??""}]`),f(i,`相似度: ${a??""}%`)},[()=>c(t).details.asrPhonemes.join(", "),()=>c(t).details.targetPhonemes?.join(", ")||"",()=>(100*(c(t).details.similarity||0)).toFixed(1)],h),l(e,a)};n(O,e=>{c(t).details&&c(t).details.asrPhonemes&&e(_)}),b(a),y((e,s,r,n)=>{o=w(a,1,"border rounded-lg p-4",null,o,e),f(i,`目标: ${c(t).word??""}`),f(v,`(${("en"===c(t).language?"英文":"中文")??""})`),f(p,s),f(j,c(t).asrResult),S=w(V,1,"font-medium",null,S,r),f(R,c(t).matched?"✓ 匹配成功":"✗ 匹配失败"),f(A,`置信度: ${n??""}%`),f(D,`方法: ${("direct"===c(t).method?"直接匹配":"phoneme"===c(t).method?"音素匹配":c(t).method)??""}`)},[()=>({"border-green-300":c(t).matched,"border-red-300":!c(t).matched,"bg-green-50":c(t).matched,"bg-red-50":!c(t).matched}),()=>new Date(c(t).timestamp).toLocaleTimeString(),()=>({"text-green-600":c(t).matched,"text-red-600":!c(t).matched}),()=>(100*c(t).confidence).toFixed(1)],h),l(e,a)}),b(t),l(e,t)};n(d,e=>{0===c(K).length?e(i):e(g,!1)}),b(r),b(a),b(t),T("click",s,oe),l(e,t)};n(ue,e=>{c(N)&&e(xe)}),l(o,de),d()}export{C as component,V as universal};
