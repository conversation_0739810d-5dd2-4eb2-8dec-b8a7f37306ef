import{p as e,a as t,m as s,c as n,o as i,i as o,F as l,G as a,f as r,g as c,v as d,x as v,s as g,t as u,e as h,r as m,h as f,A as p,j as w,k as y,y as $,z as q,q as x,l as C,a3 as k,d as S,N as b,n as T,J as j,b as E,I as R,a5 as M,M as I,u as P,V as L,K as A,L as D,O as W}from"../chunks/B1xmz3ZD.js";import{E as O,P as V}from"../chunks/Do6EixEe.js";import{E as B}from"../chunks/DuTLMzPI.js";const _=Object.freeze(Object.defineProperty({__proto__:null,load:async function(){return{title:"增强秒词测试",description:"基于音素匹配的智能秒词系统"}},prerender:!1,ssr:!1},Symbol.toStringTag,{value:"Module"}));var z=u('<div class="countdown-mask svelte-1mmrjfg"><div class="countdown-content svelte-1mmrjfg"><div class="countdown-number svelte-1mmrjfg"> </div> <div class="countdown-description svelte-1mmrjfg"> </div> <div class="countdown-ring svelte-1mmrjfg"><svg width="120" height="120" viewBox="0 0 120 120" class="svelte-1mmrjfg"><circle cx="60" cy="60" r="54" stroke="#e5e7eb" stroke-width="4" fill="none" class="svelte-1mmrjfg"></circle><circle cx="60" cy="60" r="54" stroke-width="4" fill="none" stroke-dasharray="339.3" stroke-linecap="round" class="progress-circle svelte-1mmrjfg"></circle></svg></div> <div class="ready-hint svelte-1mmrjfg"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="svelte-1mmrjfg"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2" class="svelte-1mmrjfg"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1mmrjfg"></path><path d="M12 18V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1mmrjfg"></path><path d="M8 22H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1mmrjfg"></path></svg> 准备好您的麦克风</div></div> <div class="countdown-bg svelte-1mmrjfg"><div class="bg-circle circle-1 svelte-1mmrjfg"></div> <div class="bg-circle circle-2 svelte-1mmrjfg"></div> <div class="bg-circle circle-3 svelte-1mmrjfg"></div></div></div>');function F(u,C){e(C,!1);let k,S=t(C,"duration",8,3),b=t(C,"description",8,"每个单词限时3秒，请大声朗读出英文单词"),T=s(S()),j=s(!0);const E=n(),R="https://img.qiaoxuesi.com/files/65_3-2-1-go.mp3";function M(){return c(T)>0?c(T).toString():"GO!"}function I(){return c(T)<=0?"#10b981":1===c(T)?"#f59e0b":"#3b82f6"}i(()=>(function(){try{new Audio(R).preload="auto"}catch(e){console.warn("音效预加载失败:",e)}}(),function(){try{new Audio(R).play().catch(e=>{console.warn("倒计时音效播放失败:",e)})}catch(e){console.warn("倒计时音效加载失败:",e)}k=setInterval(()=>{g(T,c(T)-1),2===c(T)&&(console.log("[CountdownMask] 倒计时剩余2秒，触发提前录音"),E("prestart")),c(T)<=0&&(clearInterval(k),setTimeout(()=>{g(j,!1),E("finish")},300))},1e3)}(),()=>{k&&clearInterval(k)})),o();var P=l(),L=a(P),A=e=>{var t=z(),s=h(t),n=h(s),i=h(n,!0);m(n);var o=f(n,2),l=h(o,!0);m(o);var a=f(o,2),r=h(a),v=f(h(r));m(r),m(a),p(2),m(s),p(2),m(t),w((e,t,s)=>{$(n,`color: ${e??""}`),q(i,t),q(l,b()),x(v,"stroke",s),x(v,"stroke-dashoffset",c(T)/S()*339.3)},[I,M,I],y),d(e,t)};r(L,e=>{c(j)&&e(A)}),d(u,P),v()}var U=u('<div class="repeat-badge svelte-1wpaoaf"> </div>'),H=j('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="svelte-1wpaoaf"><path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1wpaoaf"></path></svg>'),Z=j('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="svelte-1wpaoaf"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" class="svelte-1wpaoaf"></circle><path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1wpaoaf"></path></svg>'),N=u("<div><!></div>"),X=u('<div class="progress-ring svelte-1wpaoaf"><svg width="40" height="40" viewBox="0 0 40 40" class="svelte-1wpaoaf"><circle cx="20" cy="20" r="18" stroke="#e2e8f0" stroke-width="3" fill="none" class="svelte-1wpaoaf"></circle><circle cx="20" cy="20" r="18" stroke="#3b82f6" stroke-width="3" fill="none" stroke-dasharray="113" stroke-dashoffset="113" stroke-linecap="round" class="progress-circle svelte-1wpaoaf"></circle></svg> <div class="progress-text svelte-1wpaoaf"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="svelte-1wpaoaf"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2" class="svelte-1wpaoaf"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1wpaoaf"></path></svg></div></div>'),G=u('<div class="waiting-indicator svelte-1wpaoaf"><div class="ready-dot svelte-1wpaoaf"></div></div>'),Y=u('<div class="waiting-indicator svelte-1wpaoaf"><div class="waiting-dot svelte-1wpaoaf"></div></div>'),J=u('<div class="score-value svelte-1wpaoaf"> </div> <div class="confidence-text svelte-1wpaoaf"> </div>',1),K=u('<div class="score-value svelte-1wpaoaf">0</div>'),Q=u('<div><div class="wheel-item-content svelte-1wpaoaf"><div class="word-info svelte-1wpaoaf"><div class="english-word svelte-1wpaoaf"> </div> <div class="chinese-meaning svelte-1wpaoaf"> </div> <!></div> <div class="status-indicator svelte-1wpaoaf"><!></div> <div><!></div></div></div>'),ee=u('<div class="word-list svelte-1wpaoaf"><div class="wheel-wrapper svelte-1wpaoaf"><div class="scroll-list svelte-1wpaoaf"></div></div></div>');function te(n,i){e(i,!1);const l=s();let u=t(i,"wordList",24,()=>[]),j=t(i,"currentIndex",8,0),E=t(i,"results",24,()=>[]),R=t(i,"isRunning",8,!1),M=t(i,"maxTimePerWord",8,3500);C(()=>(k(u()),k(j()),k(E())),()=>{g(l,function(e,t,s){if(console.log("[WordList] getVisibleWords 调用:",{wordListLength:e?.length||0,currentIndex:t,resultsLength:s?.length||0}),!e||0===e.length)return console.log("[WordList] wordList 为空"),[];const n=Math.max(0,t-3),i=Math.min(e.length-1,t+3),o=[];for(let l=n;l<=i;l++){const n=e[l];if(!n)continue;const i=49*(l-t+2);o.push({...n,idx:l,isActive:l===t,isPast:l<t,isFuture:l>t,result:s?.[l],topOffset:i})}return console.log("[WordList] 可见单词:",o.map(e=>({idx:e.idx,english:e.english,topOffset:e.topOffset,isActive:e.isActive}))),o}(u(),j(),E()))}),C(()=>(k(u()),k(j()),k(E()),k(R()),c(l)),()=>{console.log("[WordList] Props 更新:",{wordList:u()?.length||0,currentIndex:j(),results:E()?.length||0,isRunning:R(),visibleWords:c(l)?.length||0,timestamp:(new Date).toISOString()})}),C(()=>k(j()),()=>{console.log("[WordList] currentIndex 变化:",j(),"时间:",(new Date).toISOString())}),S(),o();var I=ee(),P=h(I),L=h(P);b(L,5,()=>c(l),e=>e.key,(e,t)=>{var s=Q();let n;var i=h(s),o=h(i),l=h(o),v=h(l,!0);m(l);var g=f(l,2),u=h(g,!0);m(g);var C=f(g,2),k=e=>{var s=U(),n=h(s);m(s),w(()=>q(n,`重复 ${c(t).repeatCount??""}`)),d(e,s)};r(C,e=>{c(t).isRepeat&&e(k)}),m(o);var S=f(o,2),b=h(S),E=e=>{var s=N();let n;var i=h(s),o=e=>{var t=H();d(e,t)},l=e=>{var t=Z();d(e,t)};r(i,e=>{c(t).result.matched?e(o):e(l,!1)}),m(s),w(e=>n=T(s,1,"result-icon svelte-1wpaoaf",null,n,e),[()=>({matched:c(t).result.matched})],y),d(e,s)},I=(e,s)=>{var n=e=>{var t=X(),s=h(t),n=f(h(s));m(s),p(2),m(t),w(()=>{x(t,"key",`progress-${j()??""}`),$(n,`animation-duration: ${M()??""}ms`)}),d(e,t)},i=(e,s)=>{var n=e=>{var t=G();d(e,t)},i=e=>{var t=Y();d(e,t)};r(e,e=>{c(t).isActive&&!R()?e(n):e(i,!1)},s)};r(e,e=>{c(t).isActive&&R()?e(n):e(i,!1)},s)};r(b,e=>{c(t).result?e(E):e(I,!1)}),m(S);var P=f(S,2);let L;var A=h(P),D=e=>{var s=J(),n=a(s),i=h(n,!0);m(n);var o=f(n,2),l=h(o);m(o),w(e=>{q(i,c(t).result.score),q(l,`${e??""}%`)},[()=>Math.round(100*(c(t).result.confidence||0))],y),d(e,s)},W=(e,s)=>{var n=e=>{var t=K();d(e,t)};r(e,e=>{c(t).isPast&&e(n)},s)};r(A,e=>{c(t).result?e(D):e(W,!1)}),m(P),m(i),m(s),w((e,i)=>{n=T(s,1,"wheel-item svelte-1wpaoaf",null,n,e),$(s,`top: ${c(t).topOffset??""}px`),q(v,c(t).english),q(u,c(t).chinese),L=T(P,1,"score-section svelte-1wpaoaf",null,L,i)},[()=>({active:c(t).isActive,past:c(t).isPast,future:c(t).isFuture}),()=>({correct:c(t).result?.matched,wrong:c(t).result&&!c(t).result.matched})],y),d(e,s)}),m(L),m(P),m(I),d(n,I),v()}var se=u('<div class="current-word-hint svelte-163jitm"><div class="hint-label svelte-163jitm">当前单词:</div> <div class="hint-word svelte-163jitm"><span class="english svelte-163jitm"> </span> <span class="chinese svelte-163jitm"> </span></div></div>'),ne=u('<button class="start-btn svelte-163jitm"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12 18V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8 22H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg> 开始秒词</button>'),ie=u('<button class="quit-btn svelte-163jitm"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg> 退出测试</button>'),oe=u('<div class="enhanced-speed-card svelte-163jitm"><div class="progress-indicator svelte-163jitm"> </div>  <!> <div class="word-list-container svelte-163jitm"><!></div> <div class="controls svelte-163jitm"><!> <div class="instruction svelte-163jitm"><!></div></div> <div class="hidden-asr svelte-163jitm"><!></div> <!></div>');function le(l,a){e(a,!1);let u=t(a,"dataSource",24,()=>[]);t(a,"repeatTimes",8,2);const p=3500,$=500,x=3e3;let b=s(),T=s([]),j=s(0),A=s([]),D=[],W=s(!1),_=s(!1),z=null,U=null,H=!1,Z=0,N=[],X="",G=new Map,Y=null,J=null,K=null,Q=[];const ee=n();function le(){console.log("[EnhancedSpeedCard] 开始初始化单词列表, dataSource:",u()),g(T,u().map((e,t)=>({...e,key:`word_${t}_${Date.now()}`,index:t,offset:49*t,isRepeat:!1,originalIndex:t}))),Z=c(T).length,g(A,new Array(c(T).length).fill(null)),console.log("[EnhancedSpeedCard] 初始化完成:",{wordList:c(T),totalWords:Z,resultsLength:c(A).length})}async function ae(){try{return console.log("[EnhancedSpeedCard] 启动连续ASR录音"),await c(b).startRecording(),await async function(){try{K||(K=new B,console.log("[EnhancedSpeedCard] 环境检测器已创建"));const e=await c(b).getAudioStream();if(!e)return void console.warn("[EnhancedSpeedCard] 无法获取音频流，跳过环境检测");if(await K.initialize(e,de)){const e=ve();K.startDetection(e),console.log("[EnhancedSpeedCard] 环境检测已启动，当前单词数:",e)}else console.warn("[EnhancedSpeedCard] 环境检测初始化失败")}catch(e){console.error("[EnhancedSpeedCard] 环境检测启动失败:",e)}}(),!0}catch(e){return console.error("[EnhancedSpeedCard] ASR启动失败:",e),!1}}async function re(){try{console.log("[EnhancedSpeedCard] 停止连续ASR录音")}catch(e){console.error("[EnhancedSpeedCard] ASR停止失败:",e)}}function ce(){if(c(j)>=c(T).length)return void ye();const e=c(T)[c(j)];if(console.log(`[单词开始] 开始识别单词: ${e.english} (${c(j)+1}/${c(T).length})`),console.log(`[基底状态] 单词开始时基底内容: "${X}"`),K){const e=ve();K.startDetection(e),console.log(`[环境检测] 更新目标单词数: ${e}`)}const t={word:e,index:c(j),startTime:Date.now(),status:"waiting"};N.push(t),console.log("[等待队列] 单词添加到等待队列:",t),z=setTimeout(()=>{console.log(`[EnhancedSpeedCard] 单词 ${e.english} 超时`),function(){if(!c(W)||c(j)>=c(T).length||H)return void console.log(`[EnhancedSpeedCard] 超时处理被跳过，isRunning: ${c(W)}, currentIndex: ${c(j)}, wordList.length: ${c(T).length}, isTransitioning: ${H}`);const e=c(T)[c(j)],t=c(j)===c(T).length-1;console.log(`[EnhancedSpeedCard] 处理单词超时: ${e.english} (索引: ${c(j)}, 是否最后一个: ${t})`);const s=N.find(e=>e.index===c(j)&&"waiting"===e.status);s&&(s.status="timeout",console.log("[EnhancedSpeedCard] 标记单词为超时:",s));if(c(A)[c(j)])return console.log(`[EnhancedSpeedCard] 单词 ${e.english} 已有结果，跳过超时处理`),void(t?ye():we(300));if(t)return console.log("[最后单词] 开始最后一个单词的特殊处理流程"),console.log("[最后单词] 进入额外录音阶段，继续录音 500ms"),void(Y=setTimeout(async()=>{console.log("[最后单词] 额外录音时间结束，停止ASR录音"),await re(),console.log("[最后单词] 开始等待最终结果，等待时间: 3000ms"),J=setTimeout(()=>{console.log("[最后单词] 等待超时，强制结束测试"),ye()},x)},$));!function(e){let t=[];try{t=(new V).englishToPhonemes(e.english)}catch(n){console.error("[EnhancedSpeedCard] 获取目标音素失败:",n)}const s={refText:e.english,asrResult:"超时未识别",matched:!1,confidence:0,method:"timeout",details:{timeout:!0,timeoutDuration:p,asrPhonemes:[],targetPhonemes:t,similarity:0,matchMethod:"timeout",reason:"识别超时"},score:0,audioUrl:"",timestamp:(new Date).toISOString()};M(A,c(A)[c(j)]=s),D.push({...e,resultIndex:c(j)}),console.log(`[EnhancedSpeedCard] 单词 ${e.english} 超时，结果已保存`),we(300)}(e)}()},p)}function de(e,t){if(console.log("[EnhancedSpeedCard] 环境警告:",e,"类型:",t),"undefined"!=typeof window){const t=document.createElement("div");if(t.className="environment-warning-message",t.innerHTML=`\n        <div class="warning-content">\n          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">\n            <path d="M12 2L1 21h22L12 2zm0 3.99L19.53 19H4.47L12 5.99zM11 16h2v2h-2v-2zm0-6h2v4h-2v-4z"/>\n          </svg>\n          <span>${e}</span>\n        </div>\n      `,t.style.cssText="\n        position: fixed;\n        top: 20px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: #fff7e6;\n        border: 1px solid #ffd591;\n        border-radius: 6px;\n        padding: 12px 16px;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        z-index: 9999;\n        font-size: 14px;\n        color: #d46b08;\n        max-width: 400px;\n        animation: slideInDown 0.3s ease-out;\n      ",!document.querySelector("#environment-warning-styles")){const e=document.createElement("style");e.id="environment-warning-styles",e.textContent="\n          @keyframes slideInDown {\n            from { opacity: 0; transform: translateX(-50%) translateY(-20px); }\n            to { opacity: 1; transform: translateX(-50%) translateY(0); }\n          }\n          .warning-content {\n            display: flex;\n            align-items: center;\n            gap: 8px;\n          }\n          .warning-content svg {\n            flex-shrink: 0;\n          }\n        ",document.head.appendChild(e)}document.body.appendChild(t),setTimeout(()=>{t.parentNode&&(t.style.animation="slideInDown 0.3s ease-out reverse",setTimeout(()=>{t.remove()},300))},3e3)}Q.push({message:e,type:t,timestamp:(new Date).toISOString()})}function ve(){if(c(j)>=c(T).length)return 1;const e=c(T)[c(j)];if(!e||!e.english)return 1;return e.english.trim().split(/\s+/).length}async function ge(e,t,s){try{console.log(`[单词匹配] 开始为单词 "${t}" 进行匹配，ASR内容: "${e}"`);const n=new V,i=await n.enhancedMatch(e,t,"en");console.log(`[单词匹配] 单词 "${t}" 匹配结果:`,{matched:i.matched,confidence:i.confidence,method:i.method,similarity:i.details?.similarity});return{refText:t,asrResult:e,matched:i.matched,confidence:i.confidence,method:i.method,details:{...i.details,asrPhonemes:i.details?.asrPhonemes||[],targetPhonemes:i.details?.targetPhonemes||[],similarity:i.details?.similarity||0,matchMethod:i.details?.matchMethod||i.method,queueMatched:!0,originalAnalysis:s},score:Math.round(100*(i.confidence||0)),audioUrl:"",timestamp:(new Date).toISOString()}}catch(n){return console.error(`[单词匹配] 单词 "${t}" 匹配失败:`,n),{refText:t,asrResult:e,matched:!1,confidence:0,method:"error",details:{error:n.message,queueMatched:!0,originalAnalysis:s},score:0,audioUrl:"",timestamp:(new Date).toISOString()}}}let ue=!1,he=null;function me(e){if(console.log(`[基底清理] 开始清理 - 输入内容: "${e}", 当前基底: "${X}"`),!X||!e)return console.log("[基底清理] 无需清理 - 基底或内容为空"),e;if(e.startsWith(X)){const t=e.substring(X.length).trim();return console.log(`[基底清理] ✅ 清理成功: "${X}" + "${t}" -> "${t}"`),t}return console.log("[基底清理] ❌ 无需清理 - 内容不以基底开头"),e}async function fe(e){if(!c(W))return;let{content:t,status:s}=e.detail;if(console.log("[ASR输入] 收到文本输入事件:",{content:t,status:s}),console.log(`[基底内容] 当前基底字符: "${X}"`),t=function(e){return e?e.replace(/[，。！？；：""''（）【】《》〈〉「」『』〔〕［］｛｝〖〗〘〙〚〛,.!?;:"'()\[\]{}<>]/g,"").replace(/\s+/g," ").trim():""}(t),!t)return void console.log("[ASR输入] 内容为空，跳过处理");if(J)return console.log("[最后单词] 在等待阶段收到ASR结果，处理最终结果"),void function(e,t,s){console.log("[最后单词] 处理最终结果:",{content:e,matchResult:t,analysisDetails:s}),J&&(clearTimeout(J),J=null);const n=c(T).length-1,i=c(T)[n],o=me(e||"");console.log(`[最后单词] 基底清理: "${e}" -> "${o}"`);const l={content:o,originalContent:e||"",matchResult:t,analysisDetails:s,timestamp:Date.now()},a={refText:i.english,asrResult:l.content,matched:!1,confidence:0,method:"unknown",details:{...l.analysisDetails,asrPhonemes:[],targetPhonemes:[],similarity:0,matchMethod:"",originalMatchResult:l.matchResult,processingTime:l.timestamp-(N.find(e=>e.index===n)?.startTime||0),baseContentUsed:X,originalBeforeClean:l.originalContent},score:Math.round(0),audioUrl:"",timestamp:(new Date).toISOString()};console.log("[最后单词] 最终匹配结果:",a),M(A,c(A)[n]=a);const r=N.find(e=>e.index===n);r&&(r.status=a.matched?"completed":"failed"),a.matched?(console.log("[最后单词] 🎉 最终匹配成功，立即结束测试"),setTimeout(()=>{ye()},300)):(D.push({...i,resultIndex:n}),console.log("[最后单词] 最终匹配失败，添加到错误列表"),setTimeout(()=>{console.log("[最后单词] 最终处理完成，结束测试"),ye()},500))}(t,null,null);!function(e){const t=N.filter(e=>"timeout"===e.status);0!==t.length&&(console.log(`[回调计数] 发现 ${t.length} 个超时单词，当前内容: "${e}"`),t.forEach(e=>{const t=`${e.index}_${e.word.english}`,s=(G.get(t)||0)+1;if(G.set(t,s),console.log(`[回调计数] 单词 "${e.word.english}" (索引${e.index}) 回调次数: ${s}/3`),s>=3){console.log(`[回调清理] 单词 "${e.word.english}" 达到清理条件，开始最终处理`);const n=c(A)[e.index];n&&n.matched?(console.log(`[回调清理] ✅ 单词 "${e.word.english}" 已匹配成功，标记为完成`),e.status="completed"):(console.log(`[回调清理] ❌ 单词 "${e.word.english}" 最终未匹配，标记为失败`),e.status="failed",n||M(A,c(A)[e.index]={refText:e.word.english,asrResult:"未识别",matched:!1,confidence:0,method:"final_timeout",details:{finalTimeout:!0,callbackCount:s,reason:"回调计数超时"},score:0,audioUrl:"",timestamp:(new Date).toISOString()}),D.push({...e.word,resultIndex:e.index})),G.delete(t),console.log(`[回调清理] 单词 "${e.word.english}" 最终处理完成，状态: ${e.status}`)}}))}(t);const n=me(t||"");console.log(`[基底清理] 原内容: "${t}" | 基底: "${X}" | 清理后: "${n}"`),console.log("[匹配策略] 开始统一匹配处理"),K&&K.processASRResult(t);const i=await async function(e,t){const s=N.filter(e=>"waiting"===e.status||"timeout"===e.status);if(0===s.length)return console.log("[等待队列匹配] 没有可处理的单词"),null;console.log(`[等待队列匹配] 找到 ${s.length} 个可处理的单词:`,s.map(e=>e.word.english));const n=[];for(const i of s){console.log(`[等待队列匹配] 尝试匹配单词: "${i.word.english}"`);const s=await ge(e,i.word.english,null);s.details.processingTime=Date.now()-i.startTime,s.details.baseContentUsed=X,s.details.originalBeforeClean=t,console.log(`[等待队列匹配] 单词 "${i.word.english}" 匹配结果:`,{matched:s.matched,confidence:s.confidence,method:s.method,score:s.score}),s.matched&&(console.log(`[等待队列匹配] ✅ 单词 "${i.word.english}" 匹配成功`),n.push({waitingWord:i,wordResult:s,cleanContent:e,originalContent:t}))}return 0===n.length?(console.log("[等待队列匹配] 所有等待单词都没有匹配成功"),null):1===n.length?(console.log(`[等待队列匹配] 找到1个匹配成功的单词: "${n[0].waitingWord.word.english}"`),{type:"single",result:n[0]}):(console.log(`[等待队列匹配] 找到${n.length}个匹配成功的单词:`,n.map(e=>`${e.waitingWord.word.english}(${e.wordResult.confidence.toFixed(2)})`)),n.sort((e,t)=>e.waitingWord.index-t.waitingWord.index),console.log("[等待队列匹配] 按索引排序后的匹配顺序:",n.map(e=>`${e.waitingWord.word.english}(索引${e.waitingWord.index})`)),{type:"multiple",results:n})}(n,t);var o,l;i?(console.log("[匹配策略] 等待队列匹配成功，处理结果"),l=t,"single"===(o=i).type?function(e,t,s){const{waitingWord:n,wordResult:i}=e;console.log(`[等待队列成功] 处理匹配成功的单词: "${n.word.english}"`),M(A,c(A)[n.index]=i),n.status="completed",console.log(`[基底更新] 匹配成功，更新基底内容: "${X}" -> "${s}"`),X=s.trim();const o=`${n.index}_${n.word.english}`;if(G.delete(o),n.index===c(T).length-1)return console.log("[等待队列成功] 🎉 最后一个单词匹配成功，立即结束测试"),Y&&(clearTimeout(Y),Y=null),J&&(clearTimeout(J),J=null),void setTimeout(()=>{ye()},300);n.index===c(j)?(console.log("[等待队列成功] 当前单词匹配完成，准备切换到下一个"),we(800)):console.log(`[等待队列成功] 延迟单词匹配完成，当前单词索引: ${c(j)}, 完成单词索引: ${n.index}`)}(o.result,0,l):"multiple"===o.type&&function(e,t,s){console.log(`[等待队列成功] 处理${e.length}个匹配成功的单词`);let n=!1,i=!1,o=c(j);if(e.forEach((e,t)=>{const{waitingWord:s,wordResult:l}=e;console.log(`[等待队列成功] 处理第${t+1}个匹配: "${s.word.english}" (索引: ${s.index})`),M(A,c(A)[s.index]=l),s.status="completed";const a=`${s.index}_${s.word.english}`;G.delete(a),s.index===c(T).length-1&&(n=!0),s.index===c(j)&&(i=!0),s.index>=o&&(o=s.index+1)}),console.log(`[基底更新] 多个单词匹配成功，更新基底内容: "${X}" -> "${s}"`),X=s.trim(),n)return console.log("[等待队列成功] 🎉 最后一个单词在多个匹配中，立即结束测试"),Y&&(clearTimeout(Y),Y=null),J&&(clearTimeout(J),J=null),void setTimeout(()=>{ye()},300);i?(console.log("[等待队列成功] 当前单词在多个匹配中，准备切换到下一个"),we(800)):console.log(`[等待队列成功] 多个延迟单词匹配完成，当前单词索引: ${c(j)}`)}(o.results,0,l)):console.log("[匹配策略] 等待队列无匹配，继续等待后续识别结果")}function pe(e){if(console.log("[ASR完成] 收到textCompleted事件:",e.detail),ue){console.log("[最终结果] 收到最后一个单词的textCompleted事件"),he&&(clearTimeout(he),he=null);const{content:t}=e.detail;t&&(console.log(`[最终结果] 最终识别内容: "${t}"`),fe({detail:{content:t,status:"completed",matchResult:null,analysisDetails:null}})),setTimeout(()=>{console.log("[最终结果] 最终处理完成，结束测试"),ue=!1,ye()},1e3)}}function we(e=0){z&&(clearTimeout(z),z=null),U&&(clearTimeout(U),U=null),H?console.log("[EnhancedSpeedCard] 正在切换中，跳过重复调用"):U=setTimeout(()=>{!function(){if(H)return void console.log("[EnhancedSpeedCard] 切换锁已激活，跳过重复调用");H=!0,console.log(`[EnhancedSpeedCard] 进入下一个单词，当前索引: ${c(j)} -> ${c(j)+1}`);const e=N.find(e=>e.index===c(j));e&&"waiting"===e.status&&(e.status="abandoned",console.log("[EnhancedSpeedCard] 标记当前单词为已放弃:",e));if(I(j),c(j)>=u().length)return console.log("[EnhancedSpeedCard] 已完成所有单词，等待最后一个单词的最终结果"),H=!1,console.log("[最终结果] 开始等待最后一个单词的最终结果"),ue=!0,he=setTimeout(()=>{console.log("[最终结果] 等待超时，强制结束测试"),ue=!1,ye()},5e3),void console.log("[最终结果] 设置最终等待定时器: 5000ms");console.log(`[EnhancedSpeedCard] 开始下一个单词: ${c(T)[c(j)]?.english}`),c(W)&&ce();H=!1}()},e)}async function ye(){console.log("[EnhancedSpeedCard] 测试完成"),g(W,!1),H=!1,await re(),console.log("[测试结束] 开始处理等待队列中的剩余单词");const e=N.filter(e=>"waiting"===e.status||"timeout"===e.status);console.log("[测试结束] 发现 "+e.length+" 个剩余单词:",e.map(e=>`${e.word.english}(${e.status})`)),e.forEach(e=>{console.log(`[测试结束] 处理剩余单词: "${e.word.english}" (索引: ${e.index}, 状态: ${e.status})`);const t=c(A)[e.index];if(t&&t.matched)console.log(`[测试结束] 单词 "${e.word.english}" 已匹配成功，跳过处理`);else{console.log(`[测试结束] 单词 "${e.word.english}" 未匹配成功，标记为最终失败`);let t=[];try{t=(new V).englishToPhonemes(e.word.english)}catch(s){console.error("[测试结束] 获取目标音素失败:",s)}const n={refText:e.word.english,asrResult:"测试结束时未匹配",matched:!1,confidence:0,method:"test_ended",details:{reason:"测试结束时仍在等待队列中",finalStatus:e.status,testEndTime:(new Date).toISOString(),asrPhonemes:[],targetPhonemes:t,similarity:0,matchMethod:"test_ended"},score:0,audioUrl:"",timestamp:(new Date).toISOString()};M(A,c(A)[e.index]=n),D.push({...e.word,resultIndex:e.index}),console.log(`[测试结束] 单词 "${e.word.english}" 已标记为失败并添加到错误列表`)}}),z&&(clearTimeout(z),z=null),U&&(clearTimeout(U),U=null),Y&&(clearTimeout(Y),Y=null),J&&(clearTimeout(J),J=null),K&&(K.cleanup(),K=null,console.log("[EnhancedSpeedCard] 环境检测器已清理")),N=[],X="",G.clear(),Q=[],console.log("[测试结束] 最终结果统计:");for(let s=0;s<c(T).length;s++)c(A)[s]?console.log(`[测试结束] 单词 ${s}: "${c(T)[s].english}" - ${c(A)[s].matched?"成功":"失败"} (${c(A)[s].method})`):console.log(`[测试结束] 单词 ${s}: "${c(T)[s].english}" - 缺失结果`);const t=c(A).filter(e=>null!==e);console.log(`[测试结束] 最终结果数量: ${t.length}/${c(T).length}`),ee("finish",t)}function $e(){console.log("[EnhancedSpeedCard] 开始测试"),g(_,!0)}async function qe(){console.log("[EnhancedSpeedCard] 倒计时剩余2秒，提前开始录音");await ae()?(console.log("[EnhancedSpeedCard] 提前录音启动成功，等待倒计时结束开始计时"),window.prestartRecordingFailed=!1):(console.error("[EnhancedSpeedCard] 提前ASR启动失败，将在倒计时结束后重试"),window.prestartRecordingFailed=!0)}async function xe(){if(g(_,!1),g(W,!0),g(j,0),window.prestartRecordingFailed){console.log("[EnhancedSpeedCard] 提前录音失败，现在重新启动录音");if(!(await ae()))return console.error("[EnhancedSpeedCard] 录音启动失败，无法开始测试"),void g(W,!1);console.log("[EnhancedSpeedCard] 录音重新启动成功")}else console.log("[EnhancedSpeedCard] 倒计时结束，开始计时（录音已在提前阶段启动）");window.prestartRecordingFailed=!1,ce()}async function Ce(){console.log("[EnhancedSpeedCard] 退出测试"),g(W,!1),H=!1,await re(),z&&(clearTimeout(z),z=null),U&&(clearTimeout(U),U=null),Y&&(clearTimeout(Y),Y=null),J&&(clearTimeout(J),J=null),N=[],X="",G.clear(),ee("quit")}i(()=>{console.log("[EnhancedSpeedCard] 组件挂载, dataSource:",u()),u()&&u().length>0&&le()}),E(async()=>{z&&clearTimeout(z),U&&clearTimeout(U),Y&&clearTimeout(Y),J&&clearTimeout(J),c(W)&&await re(),N=[],X="",G.clear()}),C(()=>k(u()),()=>{u()&&u().length>0&&(console.log("[EnhancedSpeedCard] dataSource 变化，重新初始化:",u()),le())}),S(),o();var ke=oe(),Se=h(ke),be=h(Se);m(Se);var Te=f(Se,2),je=e=>{var t=se(),s=f(h(t),2),n=h(s),i=h(n,!0);m(n);var o=f(n,2),l=h(o);m(o),m(s),m(t),w(()=>{q(i,c(T)[c(j)].english),q(l,`(${c(T)[c(j)].chinese??""})`)}),d(e,t)};r(Te,e=>{c(W)&&c(T)[c(j)]&&e(je)});var Ee=f(Te,2);te(h(Ee),{get wordList(){return c(T)},get currentIndex(){return c(j)},get results(){return c(A)},get isRunning(){return c(W)},maxTimePerWord:p}),m(Ee);var Re=f(Ee,2),Me=h(Re),Ie=e=>{var t=ne();P("click",t,$e),d(e,t)},Pe=(e,t)=>{var s=e=>{var t=ie();P("click",t,Ce),d(e,t)};r(e,e=>{c(W)&&e(s)},t)};r(Me,e=>{c(W)||c(_)?e(Pe,!1):e(Ie)});var Le=f(Me,2),Ae=h(Le),De=e=>{var t=L("请大声朗读出英文单词");d(e,t)},We=(e,t)=>{var s=e=>{var t=L("正在进行中...");d(e,t)};r(e,e=>{c(W)&&e(s)},t)};r(Ae,e=>{c(W)||c(_)?e(We,!1):e(De)}),m(Le),m(Re);var Oe=f(Re,2),Ve=h(Oe);const Be=y(()=>c(T)[c(j)]?.english||"");R(O(Ve,{get targetWord(){return c(Be)},language:"en",duration:600,direction:"top",showTextPanel:!1,$$events:{textInput:fe,textCompleted:pe,stop:pe,matchCompleted:function(e){console.log("[ASR匹配] 收到匹配结果:",e.detail)}},$$legacy:!0}),e=>g(b,e),()=>c(b)),m(Oe);var _e=f(Oe,2),ze=e=>{F(e,{duration:3,description:"每个单词限时3.5秒，请大声朗读出英文单词",$$events:{prestart:qe,finish:xe}})};r(_e,e=>{c(_)&&e(ze)}),m(ke),w(()=>q(be,`${c(j)??""} / ${c(T).length??""}`)),d(l,ke),v()}var ae=u("<button> </button>"),re=u("<button> </button>"),ce=u('<div><span class="word-number svelte-mi828q"></span> <span class="english svelte-mi828q"> </span> <span class="chinese svelte-mi828q"> </span> <span class="difficulty svelte-mi828q"> </span></div>'),de=u('<div class="word-preview svelte-mi828q"></div>'),ve=u('<div class="no-words svelte-mi828q"><p class="svelte-mi828q">没有找到符合条件的单词，请调整难度等级</p></div>'),ge=u('<div class="settings-container svelte-mi828q"><div class="header svelte-mi828q"><h1 class="svelte-mi828q">增强秒词测试</h1> <p class="svelte-mi828q">基于音素匹配的智能语音识别系统</p></div> <div class="settings-panel svelte-mi828q"><div class="setting-group svelte-mi828q"><label class="svelte-mi828q">选择难度等级：</label> <div class="difficulty-buttons svelte-mi828q"></div> <div class="difficulty-desc svelte-mi828q"><!></div></div> <div class="setting-group svelte-mi828q"><label class="svelte-mi828q">选择单词数量：</label> <div class="count-buttons svelte-mi828q"></div></div> <div class="preview-section svelte-mi828q"><h3 class="svelte-mi828q"> </h3> <!></div> <div class="start-section svelte-mi828q"><button class="start-btn svelte-mi828q"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M8 5V19L19 12L8 5Z" fill="currentColor"></path></svg> 开始秒词测试</button> <div class="test-info svelte-mi828q"><p class="svelte-mi828q">• 每个单词限时 3.5 秒</p> <p class="svelte-mi828q">• 错误单词会自动重复</p> <p class="svelte-mi828q">• 支持音素级智能匹配</p></div></div></div></div>'),ue=u('<div class="method-item svelte-mi828q"><span class="method-label svelte-mi828q">未知</span> <span class="method-count svelte-mi828q"> </span></div>'),he=u('<div class="difficulty-item svelte-mi828q"><div class="difficulty-level svelte-mi828q"> </div> <div class="difficulty-score svelte-mi828q"> </div></div>'),me=u('<div class="phoneme-row svelte-mi828q"><strong>识别音素:</strong> </div>'),fe=u('<div class="phoneme-row svelte-mi828q"><strong>识别音素:</strong> [无音素数据]</div>'),pe=u('<div class="phoneme-row svelte-mi828q"><strong>目标音素:</strong> </div>'),we=u('<div class="phoneme-row svelte-mi828q"><strong>目标音素:</strong> [无音素数据]</div>'),ye=u('<div class="phoneme-section svelte-mi828q"><!> <!> <div class="phoneme-row svelte-mi828q"><strong>相似度:</strong> </div></div>'),$e=u('<div class="timeout-section svelte-mi828q"><div class="timeout-info svelte-mi828q"><strong>识别音素:</strong> [超时未获取]</div> <div class="timeout-info svelte-mi828q"><strong>目标音素:</strong> [超时未获取]</div> <div class="timeout-info svelte-mi828q"><strong>相似度:</strong> 0.0%</div></div>'),qe=u('<div class="direct-section svelte-mi828q"><div class="direct-info svelte-mi828q"><strong>识别音素:</strong> [直接匹配，无需音素]</div> <div class="direct-info svelte-mi828q"><strong>目标音素:</strong> [直接匹配，无需音素]</div> <div class="direct-info svelte-mi828q"><strong>相似度:</strong> </div></div>'),xe=u('<div class="unknown-section svelte-mi828q"><div class="unknown-info svelte-mi828q"><strong>识别音素:</strong> [数据缺失]</div> <div class="unknown-info svelte-mi828q"><strong>目标音素:</strong> [数据缺失]</div> <div class="unknown-info svelte-mi828q"><strong>相似度:</strong> </div></div>'),Ce=u('<div class="debug-info svelte-mi828q"><strong>处理延迟:</strong> </div>'),ke=u('<div class="debug-info svelte-mi828q"><strong>基底清理:</strong> </div>'),Se=u('<div class="debug-info svelte-mi828q"><strong>清理前:</strong> </div>'),be=u('<div class="debug-details svelte-mi828q"><!> <!> <!></div>'),Te=u('<div><div class="result-header svelte-mi828q"><div class="word-info svelte-mi828q"><span class="word-number svelte-mi828q"></span> <span class="target-word svelte-mi828q"> </span> <span class="chinese-meaning svelte-mi828q"> </span> <span class="difficulty-badge svelte-mi828q"> </span></div> <div class="result-score svelte-mi828q"><span class="score-value svelte-mi828q"> </span></div></div> <div class="result-details svelte-mi828q"><div class="recognition-result svelte-mi828q"><strong>识别结果:</strong> </div> <div> </div> <div class="method-info svelte-mi828q"><strong>方法:</strong> </div> <div class="test-details svelte-mi828q"><!></div> <!></div></div>'),je=u('<div class="stats-summary svelte-mi828q"><div class="stat-item svelte-mi828q"><div class="stat-value svelte-mi828q"> </div> <div class="stat-label svelte-mi828q">正确数量</div></div> <div class="stat-item svelte-mi828q"><div class="stat-value svelte-mi828q"> </div> <div class="stat-label svelte-mi828q">准确率</div></div> <div class="stat-item svelte-mi828q"><div class="stat-value svelte-mi828q"> </div> <div class="stat-label svelte-mi828q">平均评分</div></div> <div class="stat-item svelte-mi828q"><div class="stat-value svelte-mi828q"> </div> <div class="stat-label svelte-mi828q">平均置信度</div></div></div> <div class="method-stats svelte-mi828q"><h3 class="svelte-mi828q">识别方法分析</h3> <div class="method-grid svelte-mi828q"><div class="method-item svelte-mi828q"><span class="method-label svelte-mi828q">直接匹配</span> <span class="method-count svelte-mi828q"> </span></div> <div class="method-item svelte-mi828q"><span class="method-label svelte-mi828q">音素匹配</span> <span class="method-count svelte-mi828q"> </span></div> <div class="method-item svelte-mi828q"><span class="method-label svelte-mi828q">超时</span> <span class="method-count svelte-mi828q"> </span></div> <!></div></div> <div class="difficulty-analysis svelte-mi828q"><h3 class="svelte-mi828q">难度表现分析</h3> <div class="difficulty-grid svelte-mi828q"></div></div> <div class="detailed-results-section svelte-mi828q"><h3 class="svelte-mi828q">📝 详细识别结果</h3> <div class="detailed-results-list svelte-mi828q"></div></div>',1),Ee=u('<div class="results-container svelte-mi828q"><div class="results-header svelte-mi828q"><h2 class="svelte-mi828q">🎉 测试完成！</h2> <!></div> <div class="results-actions svelte-mi828q"><button class="secondary-btn svelte-mi828q">重新设置</button> <button class="primary-btn svelte-mi828q">再次测试</button></div></div>'),Re=u('<div class="enhanced-speed-reading svelte-mi828q"><!></div>');function Me(t,n){e(n,!1);const l=[{english:"hello",chinese:"你好",category:"问候",difficulty:1},{english:"world",chinese:"世界",category:"基础",difficulty:1},{english:"apple",chinese:"苹果",category:"水果",difficulty:1},{english:"water",chinese:"水",category:"日常",difficulty:1},{english:"book",chinese:"书",category:"学习",difficulty:1},{english:"computer",chinese:"电脑",category:"科技",difficulty:2},{english:"school",chinese:"学校",category:"教育",difficulty:2},{english:"family",chinese:"家庭",category:"关系",difficulty:2},{english:"friend",chinese:"朋友",category:"关系",difficulty:2},{english:"house",chinese:"房子",category:"建筑",difficulty:2},{english:"beautiful",chinese:"美丽的",category:"形容词",difficulty:3},{english:"important",chinese:"重要的",category:"形容词",difficulty:3},{english:"different",chinese:"不同的",category:"形容词",difficulty:3},{english:"language",chinese:"语言",category:"学习",difficulty:3},{english:"business",chinese:"商业",category:"工作",difficulty:3},{english:"pronunciation",chinese:"发音",category:"语言",difficulty:4},{english:"environment",chinese:"环境",category:"自然",difficulty:4},{english:"technology",chinese:"技术",category:"科技",difficulty:4},{english:"opportunity",chinese:"机会",category:"抽象",difficulty:4},{english:"development",chinese:"发展",category:"抽象",difficulty:4},{english:"extraordinary",chinese:"非凡的",category:"高级形容词",difficulty:5},{english:"responsibility",chinese:"责任",category:"抽象概念",difficulty:5},{english:"communication",chinese:"交流",category:"社交",difficulty:5},{english:"understanding",chinese:"理解",category:"认知",difficulty:5},{english:"international",chinese:"国际的",category:"地理政治",difficulty:5}];let u=s(1),$=s(10),x=s([]),C=s(!1),k=s(null);function S(){const e=l;console.log(`[SpeedReading] 筛选后的单词数量: ${e.length}, 难度: ${c(u)}, 选择数量: ${c($)}`);const t=[...e].sort(()=>Math.random()-.5);g(x,t.slice(0,c($)).map((e,t)=>({...e,wid:`word_${t}`,index:t,offset:49*t}))),console.log(`[SpeedReading] 生成的单词列表 (${c(x).length}个):`,c(x).map(e=>e.english))}function j(){S(),g(C,!0)}function E(){g(C,!1),g(k,null)}function R(){g(C,!1),g(k,null)}function M(){if(!c(k))return null;const e=c(k).length,t=c(k).filter(e=>e.matched).length,s=e-t,n=e>0?(t/e*100).toFixed(1):0,i=e>0?(c(k).reduce((e,t)=>e+(t.confidence||0),0)/e*100).toFixed(1):0,o={direct:c(k).filter(e=>"direct"===e.method).length,phoneme:c(k).filter(e=>"phoneme"===e.method).length,timeout:c(k).filter(e=>"timeout"===e.method).length,unknown:c(k).filter(e=>"unknown"===e.method||!e.method).length},a=e>0?(c(k).reduce((e,t)=>e+(t.score||0),0)/e).toFixed(1):0,r=c(k).map(e=>{const t=l.find(t=>t.english===e.refText);return{word:e.refText,chinese:t?.chinese||"",difficulty:t?.difficulty||0,recognized:e.asrResult||"未识别",matched:e.matched,confidence:Math.round(100*(e.confidence||0)),score:e.score||0,method:e.method||"unknown",details:e.details,timestamp:e.timestamp}}),d=r.filter(e=>!e.matched),v={};return c(k).forEach(e=>{const t=l.find(t=>t.english===e.refText);if(t){const s=t.difficulty;v[s]||(v[s]={total:0,correct:0}),v[s].total++,e.matched&&v[s].correct++}}),{total:e,correct:t,wrong:s,accuracy:n,avgConfidence:i,avgScore:a,methodStats:o,wrongWords:d,difficultyStats:v,detailedResults:r}}i(()=>{S()}),o();var I=Re();A(e=>{D.title="增强秒词测试"});var O=h(I),V=e=>{var t=ge(),s=f(h(t),2),n=h(s),i=f(h(n),2);b(i,4,()=>[1,2,3,4,5],W,(e,t)=>{var s=ae();let n;var i=h(s);m(s),w(e=>{n=T(s,1,"difficulty-btn svelte-mi828q",null,n,e),q(i,`等级 ${t??""}`)},[()=>({active:c(u)===t})],y),P("click",s,()=>{g(u,t),S()}),d(e,s)}),m(i);var o=f(i,2),l=h(o),a=e=>{var t=L("基础词汇 - 日常生活常用词");d(e,t)},v=(e,t)=>{var s=e=>{var t=L("进阶词汇 - 学习工作常用词");d(e,t)},n=(e,t)=>{var s=e=>{var t=L("中级词汇 - 形容词和抽象概念");d(e,t)},n=(e,t)=>{var s=e=>{var t=L("高级词汇 - 专业术语和复杂概念");d(e,t)},n=e=>{var t=L("挑战词汇 - 高难度长单词");d(e,t)};r(e,e=>{4===c(u)?e(s):e(n,!1)},t)};r(e,e=>{3===c(u)?e(s):e(n,!1)},t)};r(e,e=>{2===c(u)?e(s):e(n,!1)},t)};r(l,e=>{1===c(u)?e(a):e(v,!1)}),m(o),m(n);var C=f(n,2),k=f(h(C),2);b(k,4,()=>[5,10,15,20,25],W,(e,t)=>{var s=re();let n;var i=h(s);m(s),w(e=>{n=T(s,1,"count-btn svelte-mi828q",null,n,e),q(i,`${t??""} 个`)},[()=>({active:c($)===t})],y),P("click",s,()=>{g($,t),S()}),d(e,s)}),m(k),m(C);var E=f(C,2),R=h(E),M=h(R);m(R);var I=f(R,2),A=e=>{var t=de();b(t,5,()=>c(x),W,(e,t,s)=>{var n=ce();T(n,1,"preview-word svelte-mi828q",null,{},{highlight:s<5});var i=h(n);i.textContent=`${s+1}.`;var o=f(i,2),l=h(o,!0);m(o);var a=f(o,2),r=h(a,!0);m(a);var v=f(a,2),g=h(v);m(v),m(n),w(()=>{q(l,c(t).english),q(r,c(t).chinese),q(g,`难度${c(t).difficulty??""}`)}),d(e,n)}),m(t),d(e,t)},D=e=>{var t=ve();d(e,t)};r(I,e=>{c(x).length>0?e(A):e(D,!1)}),m(E);var O=f(E,2),V=h(O);p(2),m(O),m(s),m(t),w(()=>q(M,`预览单词列表 (${c(x).length??""} 个)`)),P("click",V,j),d(e,t)},B=(e,t)=>{var s=e=>{le(e,{get dataSource(){return c(x)},repeatTimes:2,$$events:{finish:e=>{return t=e.detail,g(k,t),g(C,!1),void console.log("测试完成，结果:",t);var t},quit:R}})},n=(e,t)=>{var s=e=>{var t=Ee(),s=h(t),n=f(h(s),2),i=e=>{var t=je();const s=y(M);var n=a(t),i=h(n),o=h(i),l=h(o);m(o),p(2),m(i);var v=f(i,2),g=h(v),u=h(g);m(g),p(2),m(v);var $=f(v,2),x=h($),C=h(x,!0);m(x),p(2),m($);var k=f($,2),S=h(k),j=h(S);m(S),p(2),m(k),m(n);var E=f(n,2),R=f(h(E),2),I=h(R),P=f(h(I),2),L=h(P,!0);m(P),m(I);var A=f(I,2),D=f(h(A),2),O=h(D,!0);m(D),m(A);var V=f(A,2),B=f(h(V),2),_=h(B,!0);m(B),m(V);var z=f(V,2),F=e=>{var t=ue(),n=f(h(t),2),i=h(n,!0);m(n),m(t),w(()=>q(i,c(s).methodStats.unknown)),d(e,t)};r(z,e=>{c(s).methodStats.unknown>0&&e(F)}),m(R),m(E);var U=f(E,2),H=f(h(U),2);b(H,5,()=>Object.entries(c(s).difficultyStats),W,(e,t)=>{let s=()=>c(t)[1];var n=he(),i=h(n),o=h(i);m(i);var l=f(i,2),a=h(l);m(l),m(n),w(e=>{q(o,`难度 ${c(t)[0]??""}`),q(a,`${s().correct??""}/${s().total??""}\n                    (${e??""}%)`)},[()=>(s().correct/s().total*100).toFixed(1)],y),d(e,n)}),m(H),m(U);var Z=f(U,2),N=f(h(Z),2);b(N,5,()=>c(s).detailedResults,W,(e,t,s)=>{var n=Te();let i;var o=h(n),l=h(o),a=h(l);a.textContent=`${s+1}.`;var v=f(a,2),g=h(v,!0);m(v);var u=f(v,2),p=h(u);m(u);var $=f(u,2),x=h($);m($),m(l);var C=f(l,2),k=h(C),S=h(k,!0);m(k),m(C),m(o);var b=f(o,2),j=h(b),E=f(h(j));m(j);var R=f(j,2);let M;var I=h(R);m(R);var P=f(R,2),L=f(h(P));m(P);var A=f(P,2),D=h(A),W=e=>{var s=ye(),n=h(s),i=e=>{var s=me(),n=f(h(s));m(s),w(e=>q(n,` [${e??""}]`),[()=>c(t).details.asrPhonemes.join(", ")],y),d(e,s)},o=e=>{var t=fe();d(e,t)};r(n,e=>{c(t).details.asrPhonemes&&c(t).details.asrPhonemes.length>0?e(i):e(o,!1)});var l=f(n,2),a=e=>{var s=pe(),n=f(h(s));m(s),w(e=>q(n,` [${e??""}]`),[()=>c(t).details.targetPhonemes.join(", ")],y),d(e,s)},v=e=>{var t=we();d(e,t)};r(l,e=>{c(t).details.targetPhonemes&&c(t).details.targetPhonemes.length>0?e(a):e(v,!1)});var g=f(l,2),u=f(h(g));m(g),m(s),w(e=>q(u,` ${e??""}`),[()=>void 0!==c(t).details.similarity?(100*c(t).details.similarity).toFixed(1)+"%":"无数据"],y),d(e,s)},O=(e,s)=>{var n=e=>{var t=$e();d(e,t)},i=(e,s)=>{var n=e=>{var s=qe(),n=f(h(s),4),i=f(h(n));m(n),m(s),w(()=>q(i,` ${c(t).confidence??""}%`)),d(e,s)},i=e=>{var s=xe(),n=f(h(s),4),i=f(h(n));m(n),m(s),w(()=>q(i,` ${c(t).confidence??""}%`)),d(e,s)};r(e,e=>{"direct"===c(t).method?e(n):e(i,!1)},s)};r(e,e=>{"timeout"===c(t).method?e(n):e(i,!1)},s)};r(D,e=>{c(t).details&&(c(t).details.asrPhonemes||c(t).details.targetPhonemes)?e(W):e(O,!1)}),m(A);var V=f(A,2),B=e=>{var s=be(),n=h(s),i=e=>{var s=Ce(),n=f(h(s));m(s),w(()=>q(n,` ${c(t).details.processingTime??""}ms`)),d(e,s)};r(n,e=>{c(t).details.processingTime&&e(i)});var o=f(n,2),l=e=>{var s=ke(),n=f(h(s));m(s),w(()=>q(n,` "${c(t).details.baseContentUsed??""}" → "${c(t).recognized??""}"`)),d(e,s)};r(o,e=>{c(t).details.baseContentUsed&&e(l)});var a=f(o,2),v=e=>{var s=Se(),n=f(h(s));m(s),w(()=>q(n,` "${c(t).details.originalBeforeClean??""}"`)),d(e,s)};r(a,e=>{c(t).details.originalBeforeClean&&c(t).details.originalBeforeClean!==c(t).recognized&&e(v)}),m(s),d(e,s)};r(V,e=>{c(t).details&&(c(t).details.processingTime||c(t).details.baseContentUsed)&&e(B)}),m(b),m(n),w((e,s,o)=>{i=T(n,1,"result-item svelte-mi828q",null,i,e),q(g,c(t).word),q(p,`(${c(t).chinese??""})`),q(x,`难度${c(t).difficulty??""}`),q(S,c(t).score),q(E,` ${c(t).recognized||"未识别"}`),M=T(R,1,"match-status svelte-mi828q",null,M,s),q(I,`${(c(t).matched?"✓":"✗")??""} ${(c(t).matched?"匹配成功":"匹配失败")??""} 置信度: ${c(t).confidence??""}%`),q(L,` ${o??""}`)},[()=>({correct:c(t).matched,wrong:!c(t).matched}),()=>({success:c(t).matched,failure:!c(t).matched}),()=>function(e){switch(e){case"direct":return"直接文本匹配";case"phoneme":return"音素匹配";case"timeout":return"识别超时";case"final_timeout":return"最终超时";case"unknown":return"未知方法";default:return e||"未知"}}(c(t).method)],y),d(e,n)}),m(N),m(Z),w(()=>{q(l,`${c(s).correct??""}/${c(s).total??""}`),q(u,`${c(s).accuracy??""}%`),q(C,c(s).avgScore),q(j,`${c(s).avgConfidence??""}%`),q(L,c(s).methodStats.direct),q(O,c(s).methodStats.phoneme),q(_,c(s).methodStats.timeout)}),d(e,t)};r(n,e=>{M()&&e(i)}),m(s);var o=f(s,2),l=h(o),v=f(l,2);m(o),m(t),P("click",l,E),P("click",v,j),d(e,t)};r(e,e=>{c(k)&&e(s)},t)};r(e,e=>{c(C)?e(s):e(n,!1)},t)};r(O,e=>{c(C)||c(k)?e(B,!1):e(V)}),m(I),d(t,I),v()}export{Me as component,_ as universal};
