import{p as e,a as t,c as s,o as a,i as n,t as l,e as r,f as o,r as i,h as c,u as v,v as d,x as u,X as g,s as h,g as p,m,N as f,O as b,F as w,G as y,j as x,k as q,n as I,q as $,y as k,Y as C,b as M,$ as A,z as T,l as S,d as z,a5 as j,a3 as P,I as R,A as E,_ as D,a7 as L,a8 as B,a9 as W,a6 as _,aa as O,a2 as X,ab as H}from"../chunks/B1xmz3ZD.js";import{d as Y,g as N,h as U,u as V,i as F,m as G,p as J,l as Z,b as Q}from"../chunks/BoaXofz5.js";import{u as K,s as ee,a as te,d as se,b as ae,c as ne,e as le,f as re,h as oe,j as ie,l as ce,k as ve,I as de,m as ue}from"../chunks/CwJ_S9-I.js";import{A as ge}from"../chunks/DuTLMzPI.js";import{E as he}from"../chunks/DD5itpR6.js";import{j as pe}from"../chunks/-7hijANM.js";const me=Object.freeze(Object.defineProperty({__proto__:null,load:async function({url:e}){return{type:e.searchParams.get("type")||null}}},Symbol.toStringTag,{value:"Module"}));var fe=l('<div class="loading-container svelte-1borv3o"><div class="text-center svelte-1borv3o"><div class="loading-text svelte-1borv3o">接收图片中...</div> <div class="loading-animation svelte-1borv3o"></div></div></div>'),be=l('<div class="image-loading-indicator svelte-1borv3o"><div class="loading-spinner svelte-1borv3o"></div></div>'),we=l('<div><img class="transition-opacity duration-300 svelte-1borv3o"> <!></div>'),ye=l('<div class="carousel-empty svelte-1borv3o"><button class="scan-button svelte-1borv3o"><i class="fas fa-qrcode svelte-1borv3o"></i> <span class="svelte-1borv3o">点击右上角编辑添加图片</span></button></div>'),xe=l('<div class="carousel-empty svelte-1borv3o"><p class="svelte-1borv3o">暂无图片</p></div>'),qe=l("<span></span>"),Ie=l('<div class="carousel-nav svelte-1borv3o"><div class="carousel-indicators svelte-1borv3o"></div></div>'),$e=l('<div class="carousel svelte-1borv3o"><div class="carousel-container svelte-1borv3o"><!></div> <!></div>');function ke(l,M){e(M,!1);const[A,T]=g();let S=t(M,"items",24,()=>[]),z=t(M,"loadingImages",8,!1),j=t(M,"loadedImages",24,()=>new Set);const P=s();let R,E=m(0);let D=0,L=0,B=!1;function W(){clearInterval(R),R=setInterval(()=>{h(E,(p(E)+1)%S().length)},5e3)}a(()=>(S().length>1&&W(),()=>clearInterval(R))),n();var _=$e(),O=r(_),X=r(O),H=e=>{var t=fe();d(e,t)},N=(e,t)=>{var s=e=>{var t=w(),s=y(t);f(s,1,S,b,(e,t,s)=>{var a=we(),n=r(a),l=c(n,2),u=e=>{var t=be();d(e,t)};o(l,e=>{j().has(p(t).image)||e(u)}),i(a),x(e=>{I(a,1,`carousel-slide ${(s===p(E)?"active":"")??""}`,"svelte-1borv3o"),$(n,"src",p(t).image),$(n,"alt",p(t).alt),k(n,`opacity: ${e??""}`)},[()=>j().has(p(t).image)?"1":"0"],q),v("load",n,()=>{return e=p(t).image,void P("imageload",e);var e}),d(e,a)}),d(e,t)},a=e=>{var t=w(),s=y(t),a=e=>{var t=ye();d(e,t)},n=e=>{var t=xe();d(e,t)};o(s,e=>{C(Y,"$isAdmin",A)?e(a):e(n,!1)}),d(e,t)};o(e,e=>{S().length>0?e(s):e(a,!1)},t)};o(X,e=>{z()?e(H):e(N,!1)}),i(O);var U=c(O,2),V=e=>{var t=Ie(),s=r(t);f(s,5,S,b,(e,t,s)=>{var a=qe();$(a,"data-index",s),x(()=>I(a,1,`indicator ${(s===p(E)?"active":"")??""}`,"svelte-1borv3o")),v("click",a,()=>function(e){h(E,e)}(s)),d(e,a)}),i(s),i(t),d(e,t)};o(U,e=>{S().length>1&&e(V)}),i(_),v("touchstart",_,function(e){D=e.touches[0].clientX,B=!0,clearInterval(R)}),v("touchmove",_,function(e){B&&(L=e.touches[0].clientX)}),v("touchend",_,function(){if(!B)return;const e=L-D;e>50?h(E,(p(E)-1+S().length)%S().length):e<-50&&h(E,(p(E)+1)%S().length),B=!1,S().length>1&&W()}),v("touchcancel",_,function(){B=!1,S().length>1&&W()}),d(l,_),u(),T()}var Ce=l('<div class="absolute inset-0 flex items-center justify-center bg-white/80 svelte-1q5j82q"><div class="w-8 h-8 border-2 border-green-500 border-t-transparent rounded-full animate-spin svelte-1q5j82q"></div></div>'),Me=l('<div class="extra-image-badge svelte-1q5j82q"><i class="fas fa-exclamation-triangle svelte-1q5j82q"></i></div>'),Ae=l('<div><img class="transition-opacity duration-300 svelte-1q5j82q"> <!> <button class="delete-btn svelte-1q5j82q"><i class="fas fa-trash svelte-1q5j82q"></i></button> <!></div>'),Te=l('<div><div class="scanning-spinner svelte-1q5j82q"></div> <span class="svelte-1q5j82q"> </span></div>'),Se=l('<button><i class="fas fa-plus svelte-1q5j82q"></i></button>'),ze=l('<div class="empty-image-item svelte-1q5j82q"></div>'),je=l('<div class="max-images-warning svelte-1q5j82q"><i class="fas fa-exclamation-circle svelte-1q5j82q"></i> <span class="svelte-1q5j82q"> </span> <button class="close-warning svelte-1q5j82q"><i class="fas fa-times svelte-1q5j82q"></i></button></div>'),Pe=l('<div class="image-grid-editor svelte-1q5j82q"><div><!> <!> <!></div> <!></div>');function Re(a,l){e(l,!1);const g=s();let C=t(l,"images",24,()=>[]),S=t(l,"loadedImages",24,()=>new Set),z=t(l,"isScanning",8,!1),j=t(l,"showMaxImagesWarning",8,!1),P=t(l,"maxImages",8,9),R=m(null),E=m(null),D=m(!1),L=null,B={x:0,y:0},W={x:0,y:0},_=null,O=null,X=[],H=[],Y=[];function U(e,t){const s=setTimeout(e,t);return Y.push(s),s}function V(){g("scan")}function F(e,t){p(D)||(h(E,t),B={x:e.touches[0].clientX,y:e.touches[0].clientY},W={...B},L=U(()=>{null!==p(E)&&(window.navigator&&window.navigator.vibrate&&window.navigator.vibrate(50),function(){const e=document.querySelectorAll(".image-item");X=[],e.forEach((e,t)=>{const s=e.getBoundingClientRect();X.push({index:t,left:s.left,right:s.right,top:s.top,bottom:s.bottom,centerX:s.left+s.width/2,centerY:s.top+s.height/2,width:s.width,height:s.height})})}(),H=[...Array(C().length).keys()],function(e,t,s){h(D,!0),h(E,e),B={x:t,y:s},W={...B};const a=Math.floor(e/3),n=e%3,l=document.querySelectorAll(".image-item");l&&l[e]&&(_=l[e],_.classList.add("dragging"),_.dataset.startRow=a,_.dataset.startCol=n,document.body.style.overflow="hidden")}(t,B.x,B.y),N("开始拖拽排序，松开手指放置图片","info"))},500))}function G(e,t){if(!p(D)){const t=e.touches[0].clientX,s=e.touches[0].clientY;return void(Math.sqrt(Math.pow(t-B.x,2)+Math.pow(s-B.y,2))>10&&Z())}e.preventDefault();const s=e.touches[0].clientX,a=e.touches[0].clientY,n=s-B.x,l=a-B.y;W={x:s,y:a};const r=Math.abs(n)>Math.abs(l)?n>0?"right":"left":l>0?"down":"up";if(_){_.style.transform=`translate(${n}px, ${l}px)`;const e=_.getBoundingClientRect(),t=function(e,t){if(!X.length)return null;const s=X.filter((e,t)=>t!==p(E));if(0===s.length)return null;const a={x:e-B.x,y:t-B.y},n=Math.abs(a.x)>Math.abs(a.y),l=s.map(s=>{const l=s.centerX-e,r=s.centerY-t,o=s.index>p(E),i=Math.floor(s.index/3)>Math.floor(p(E)/3);if(n){if(a.x>0&&!o||a.x<0&&o)return{index:s.index,distance:1/0}}else if(a.y>0&&!i||a.y<0&&i)return{index:s.index,distance:1/0};return{index:s.index,distance:Math.sqrt(l*l+r*r)}});if(l.sort((e,t)=>e.distance-t.distance),l[0].distance===1/0)return null;return l[0].index}(e.left+e.width/2,e.top+e.height/2);if(null!==t&&t!==O){const e=Math.floor(t/3),s=t%3,a=Math.floor(p(E)/3),n=p(E)%3;("right"===r&&(e>a||e===a&&s>n)||"left"===r&&(e<a||e===a&&s<n)||"down"===r&&e>a||"up"===r&&e<a)&&(O=t,function(){if(null===O||O===p(E))return;if(!_)return;_.getBoundingClientRect();const e=document.querySelector(".image-grid").getBoundingClientRect(),t=W.x-B.x,s=W.y-B.y,a=Math.abs(t)>Math.abs(s),n=a?t>0?"right":"left":s>0?"down":"up",l=Math.floor(O/3),r=Math.floor(p(E)/3),o=O%3,i=p(E)%3,c="right"===n&&(l>r||l===r&&o>i)||"left"===n&&(l<r||l===r&&o<i)||"down"===n&&l>r||"up"===n&&l<r;if(!c)return;if(0===p(E)&&"left"===n){if(Math.abs(t)/e.width>.15)return}if(p(E)===C().length-1&&"right"===n){if(Math.abs(t)/e.width>.15)return}if(0===r&&"up"===n){if(Math.abs(s)/e.height>.15)return}const v=Math.floor((C().length-1)/3);if(r===v&&"down"===n){if(Math.abs(s)/e.height>.15)return}let d=[...Array(C().length).keys()];d.splice(p(E),1);let u=O;O>p(E)&&u--;u=Math.max(0,Math.min(u,d.length)),d.splice(u,0,p(E)),H=d,function(){const e=document.querySelectorAll(".image-item:not(.dragging)");e.forEach(e=>{e.classList.remove("preview-move"),e.style.transform=""});const t={};H.forEach((e,s)=>{t[e]=s}),e.forEach((e,s)=>{if(s===p(E))return;const a=s,n=t[s];if(a!==n&&void 0!==n)try{const t=function(e){return e>=X.length?null:{x:X[e].centerX,y:X[e].centerY}}(n);if(t){const s=t.x-X[a].centerX,n=t.y-X[a].centerY;e.classList.add("preview-move"),e.style.transform=`translate(${s}px, ${n}px)`}}catch(l){console.error("应用预览样式时出错:",l)}})}()}())}}}function J(e,t){Z(),p(D)&&_&&function(){if(!H.length||null===O)return;try{let e=[];if(H.forEach(t=>{t>=0&&t<C().length?e.push(C()[t]):console.warn(`预览顺序包含无效索引: ${t}, 图片数组长度: ${C().length}`)}),e.length!==C().length)for(console.warn(`更新后的图片数组长度(${e.length})与原始长度(${C().length})不一致`);e.length<C().length&&C().length>0;)e.push(C()[0]);g("change",e),N("图片顺序已更新","success"),window.navigator&&window.navigator.vibrate&&window.navigator.vibrate([30,50,30])}catch(e){console.error("完成拖拽操作时出错:",e),N("更新图片顺序时出错","error")}}(),Q()}function Z(){L&&(clearTimeout(L),L=null)}function Q(){if(document.querySelectorAll(".image-item").forEach(e=>{try{e.style.transition="none",e.classList.remove("preview-move"),e.style.transform=""}catch(t){console.error("重置元素样式时出错:",t)}U(()=>{try{e&&e.style&&(e.style.transition="")}catch(t){console.error("恢复元素过渡效果时出错:",t)}},50)}),_){try{_.style.transition="none",_.classList.remove("dragging"),_.style.transform=""}catch(e){console.error("重置拖拽元素样式时出错:",e)}U(()=>{try{_&&_.style&&(_.style.transition="")}catch(e){console.error("恢复拖拽元素过渡效果时出错:",e)}},50)}document.body.style.overflow="",h(D,!1),h(E,null),_=null,O=null,H=[],X=[]}function K(){Z(),Q(),Y.forEach(e=>clearTimeout(e)),Y=[]}M(K),n();var ee=Pe();v("beforeunload",A,K);var te=r(ee),se=r(te);f(se,1,C,b,(e,t,s)=>{var a=Ae(),n=r(a),l=c(n,2),u=e=>{var t=Ce();d(e,t)};o(l,e=>{S().has(p(t).image)||e(u)});var m=c(l,2),f=c(m,2),b=e=>{var t=Me();d(e,t)};o(f,e=>{s>=P()&&e(b)}),i(a),x(e=>{I(a,1,`image-item ${(s>=P()?"extra-image":"")??""} ${(p(R)===s?"deleting":"")??""} ${(p(E)===s&&p(D)?"dragging":"")??""}`,"svelte-1q5j82q"),$(n,"src",p(t).image),$(n,"alt",p(t).alt||`图片${s+1}`),k(n,`opacity: ${e??""}`)},[()=>S().has(p(t).image)?"1":"0"],q),v("load",n,()=>{return e=p(t).image,void g("imageload",e);var e}),v("click",m,()=>function(e){h(R,e),U(()=>{const t=C().filter((t,s)=>s!==e);g("change",t),t.length<=P()&&g("warningupdate",!1),h(R,null)},300)}(s)),v("touchstart",a,e=>F(e,s)),v("touchmove",a,e=>G(e)),v("touchend",a,e=>J()),v("touchcancel",a,e=>J()),d(e,a)});var ae=c(se,2),ne=e=>{var t=Te(),s=c(r(t),2),a=r(s,!0);i(s),i(t),x(()=>{I(t,1,`scanning-image-btn ${(C().length>=P()?"compact-btn":"")??""}`,"svelte-1q5j82q"),T(a,C().length>=P()?"分析中":"正在分析，请稍候...")}),d(e,t)},le=e=>{var t=Se();x(()=>I(t,1,`add-image-btn ${(C().length>=P()?"compact-btn":"")??""}`,"svelte-1q5j82q")),v("click",t,V),d(e,t)};o(ae,e=>{z()?e(ne):e(le,!1)});var re=c(ae,2),oe=e=>{var t=w(),s=y(t);f(s,1,()=>Array(8-C().length),b,(e,t)=>{var s=ze();d(e,s)}),d(e,t)};o(re,e=>{C().length<8&&e(oe)}),i(te);var ie=c(te,2),ce=e=>{var t=je(),s=c(r(t),2),a=r(s);i(s);var n=c(s,2);i(t),x(()=>T(a,`最多只能保存${P()??""}张图片！`)),v("click",n,()=>g("warningupdate",!1)),d(e,t)};o(ie,e=>{j()&&e(ce)}),i(ee),x(()=>I(te,1,`image-grid ${(C().length>=P()?"expanded-grid":"")??""}`,"svelte-1q5j82q")),d(a,ee),u()}var Ee=l('<div class="qr-scanner-loading svelte-1w2zr59"><div class="spinner svelte-1w2zr59"></div> <p class="svelte-1w2zr59">正在加载扫描组件...</p> <button class="retry-button svelte-1w2zr59">刷新页面</button></div>'),De=l('<div class="camera-loading svelte-1w2zr59"><div class="spinner svelte-1w2zr59"></div> <p class="svelte-1w2zr59">正在请求相机权限...</p></div>'),Le=l('<div class="video-container svelte-1w2zr59"><video id="qr-video" playsinline="" autoplay class="svelte-1w2zr59"></video> <div class="scan-region-highlight svelte-1w2zr59"><div class="corner top-left svelte-1w2zr59"></div> <div class="corner top-right svelte-1w2zr59"></div> <div class="corner bottom-left svelte-1w2zr59"></div> <div class="corner bottom-right svelte-1w2zr59"></div> <div class="scan-line svelte-1w2zr59"></div></div> <canvas id="qr-canvas" style="display:none;" class="svelte-1w2zr59"></canvas> <!></div> <div class="scan-instructions svelte-1w2zr59"><p class="svelte-1w2zr59">将二维码对准扫描框，支持多种格式</p> <div class="supported-formats svelte-1w2zr59"><span class="format-badge svelte-1w2zr59">URL链接</span> <span class="format-badge svelte-1w2zr59">JSON</span> <span class="format-badge svelte-1w2zr59">自定义文本</span></div></div>',3),Be=l('<div class="qr-scanner-container svelte-1w2zr59"><div class="qr-scanner-header svelte-1w2zr59"><button class="qr-scanner-close svelte-1w2zr59"><i class="fas fa-arrow-left svelte-1w2zr59"></i></button> <h3 class="qr-scanner-title svelte-1w2zr59">扫描二维码</h3> <div class="qr-scanner-light svelte-1w2zr59"><button class="flash-toggle svelte-1w2zr59"><i class="fas fa-bolt svelte-1w2zr59"></i></button></div></div> <div class="qr-scanner-body svelte-1w2zr59"><!></div></div>');function We(l,g){e(g,!1);const f=s();let b,x=t(g,"showScanner",8,!1),q=m(),I=m(),$=m(!1),k=m(!1),C=null,A=0;function T(){try{if(h($,!1),p(q)){if(p(q).srcObject){const e=p(q).srcObject.getTracks();e&&e.length&&e.forEach(e=>{try{"live"===e.readyState&&e.stop()}catch(t){console.error("停止视频轨道出错:",t)}}),j(q,p(q).srcObject=null)}j(q,p(q).onloadedmetadata=null),j(q,p(q).onplay=null),j(q,p(q).onerror=null)}b&&(clearInterval(b),b=null),A=0}catch(e){console.error("安全停止视频流出错:",e)}}async function D(){if(p(q)&&p(I))try{if(!p(k))return console.error("jsQR库未准备就绪"),N("扫描组件未准备就绪，请刷新页面重试","error"),void L();C=p(I).getContext("2d",{willReadFrequently:!0});const t={video:{facingMode:"environment",width:{ideal:1280},height:{ideal:720},frameRate:{ideal:15}}},s=await navigator.mediaDevices.getUserMedia(t);if(!x())return s.getTracks().forEach(e=>e.stop()),void h($,!1);j(q,p(q).srcObject=s);try{await p(q).play(),h($,!1)}catch(e){if(console.log("视频播放被中断:",e),h($,!1),!x())return void T()}if(!x())return T(),void h($,!1);b=setInterval(()=>{if(!x())return T(),void h($,!1);if(p(q).readyState===p(q).HAVE_ENOUGH_DATA){j(I,p(I).width=p(q).videoWidth),j(I,p(I).height=p(q).videoHeight),C.drawImage(p(q),0,0,p(I).width,p(I).height);const e=C.getImageData(0,0,p(I).width,p(I).height),t=pe(e.data,e.width,e.height,{inversionAttempts:"dontInvert"});t&&(console.log("找到二维码!",t),T(),L(),function(e){try{console.log("扫描结果:",e);let s=null;try{s=JSON.parse(e)}catch(t){}if(s&&s.uuid&&s.username)return void f("scan",{uuid:s.uuid,username:s.username});if(e.startsWith("scan:")){const t=e.split(":");if(t.length>=3)return void f("scan",{uuid:t[1],username:t[2]})}if(e.startsWith("http"))try{const t=new URL(e),s=t.searchParams.get("uuid"),a=t.searchParams.get("name")||t.searchParams.get("username");if(s)return void f("scan",{uuid:s,username:a||"未知用户"})}catch(t){}if(e&&e.length>5)return void f("scan",{uuid:e.trim(),username:"扫描用户"});A++,console.log(`二维码识别失败，重试次数: ${A}/3`),A>=3?(N("无法识别的二维码内容，请确认二维码格式正确","error"),A=0,L()):(console.log("重新尝试扫描二维码..."),!b&&p(q)&&p(I)&&D())}catch(s){console.error("处理扫描结果失败:",s),A++,console.log(`扫描处理异常，重试次数: ${A}/3`),A>=3?(N("处理二维码失败: "+s.message,"error"),L(),A=0):(console.log("重新尝试扫描二维码..."),!b&&p(q)&&p(I)&&D())}}(t.data))}},100)}catch(t){console.error("相机初始化失败:",t),N("无法访问相机，请检查权限设置","error"),L()}}function L(){f("close")}a(()=>{h(k,!0),x()&&setTimeout(()=>{D()},100)}),M(()=>{T()}),S(()=>P(x()),()=>{x()&&setTimeout(()=>{D()},100)}),z(),n();var B=w(),W=y(B),_=e=>{var t=Be(),s=r(t),a=r(s),n=c(a,4),l=r(n);i(n),i(s);var u=c(s,2),g=r(u),m=e=>{var t=Ee(),s=c(r(t),4);i(t),v("click",s,()=>window.location.reload()),d(e,t)},f=e=>{var t=Le(),s=y(t),a=r(s);a.muted=!0,R(a,e=>h(q,e),()=>p(q));var n=c(a,4);R(n,e=>h(I,e),()=>p(I));var l=c(n,2),v=e=>{var t=De();d(e,t)};o(l,e=>{p($)&&e(v)}),i(s),E(2),d(e,t)};o(g,e=>{p(k)?e(f,!1):e(m)}),i(u),i(t),v("click",a,L),v("click",l,()=>N("闪光灯功能暂未实现","info")),d(e,t)};o(W,e=>{x()&&e(_)}),d(l,B),u()}var _e=l('<button class="nav-button prev-page svelte-30ryhq"><i class="fas fa-arrow-left svelte-30ryhq"></i> <div class="nav-button-content svelte-30ryhq"><span class="nav-title svelte-30ryhq"> </span></div></button>'),Oe=l('<button class="nav-button prev-page disabled svelte-30ryhq"><i class="fas fa-arrow-left svelte-30ryhq"></i> <div class="nav-button-content svelte-30ryhq"><span class="nav-title svelte-30ryhq"> </span></div></button>'),Xe=l('<button class="nav-button next-page svelte-30ryhq"><div class="nav-button-content svelte-30ryhq"><span class="nav-title svelte-30ryhq"> </span></div> <i class="fas fa-arrow-right svelte-30ryhq"></i></button>'),He=l('<button class="nav-button next-page disabled svelte-30ryhq"><div class="nav-button-content svelte-30ryhq"><span class="nav-title svelte-30ryhq"> </span></div> <i class="fas fa-arrow-right svelte-30ryhq"></i></button>'),Ye=l("<div><!> <!></div>");var Ne=l('<button class="close-button svelte-9qo7tg"><i class="fas fa-times"></i></button>'),Ue=l('<p class="error-text svelte-9qo7tg"> </p>'),Ve=l('<p class="svelte-9qo7tg"> </p> <p class="svelte-9qo7tg"> </p> <!>',1),Fe=l('<p class="error-text svelte-9qo7tg"> </p>'),Ge=l('<p class="svelte-9qo7tg">所有图片已上传处理完成</p> <p class="svelte-9qo7tg"> </p> <!> <p class="saving-text svelte-9qo7tg">正在上传数据并保存更改...</p> <div class="saving-indicator svelte-9qo7tg"><i class="fas fa-spinner fa-spin"></i></div>',1),Je=l('<div class="success-icon svelte-9qo7tg"><i class="fas fa-check-circle"></i></div>'),Ze=l('<div class="warning-icon svelte-9qo7tg"><i class="fas fa-exclamation-triangle"></i></div>'),Qe=l('<div class="error-icon svelte-9qo7tg"><i class="fas fa-times-circle"></i></div>'),Ke=l('<p class="result-text svelte-9qo7tg"> </p> <!>',1),et=l('<div class="modal-footer svelte-9qo7tg"><button class="btn-primary svelte-9qo7tg">确定</button></div>'),tt=l('<div class="modal-backdrop svelte-9qo7tg"><div class="modal-container svelte-9qo7tg"><div class="modal-content svelte-9qo7tg"><div class="modal-header svelte-9qo7tg"><h3 class="svelte-9qo7tg"> </h3> <!></div> <div class="modal-body svelte-9qo7tg"><div class="progress-container svelte-9qo7tg"><div class="progress-bar svelte-9qo7tg"></div></div> <div class="progress-text svelte-9qo7tg"><!></div></div> <!></div></div></div>');var st=l('<button class="dialog-btn cancel-btn svelte-f2wiz1"> </button>'),at=l('<div class="dialog-backdrop svelte-f2wiz1"><div class="dialog-container svelte-f2wiz1"><div><h3 class="dialog-title svelte-f2wiz1"> </h3></div> <div class="dialog-content svelte-f2wiz1"><p class="dialog-message svelte-f2wiz1"> </p></div> <div class="dialog-footer svelte-f2wiz1"><!> <button> </button></div></div></div>');var nt=l('<div class="countdown-display svelte-3pt5hs"><span class="countdown-text svelte-3pt5hs"> </span></div>'),lt=l('<div class="recording-float svelte-3pt5hs"><div class="volume-indicator svelte-3pt5hs"><div class="recording-status-text svelte-3pt5hs"><span class="svelte-3pt5hs">正在录音，松开结束</span></div> <div class="volume-bars svelte-3pt5hs"><span class="volume-bar bar1 svelte-3pt5hs"></span> <span class="volume-bar bar2 svelte-3pt5hs"></span> <span class="volume-bar bar3 svelte-3pt5hs"></span> <span class="volume-bar bar4 svelte-3pt5hs"></span> <span class="volume-bar bar5 svelte-3pt5hs"></span> <span class="volume-bar bar6 svelte-3pt5hs"></span> <span class="volume-bar bar7 svelte-3pt5hs"></span></div></div></div>'),rt=l('<div class="ai-generating-float svelte-3pt5hs"><div class="generating-indicator svelte-3pt5hs"><div class="generating-status-text svelte-3pt5hs"><span class="svelte-3pt5hs">AI正在生成...</span></div> <div class="generating-animation svelte-3pt5hs"><span class="dot dot1 svelte-3pt5hs"></span> <span class="dot dot2 svelte-3pt5hs"></span> <span class="dot dot3 svelte-3pt5hs"></span></div></div></div>'),ot=l('<div class="ai-generating-float small svelte-3pt5hs"><div class="generating-indicator small svelte-3pt5hs"><div class="generating-status-text small svelte-3pt5hs"><span class="svelte-3pt5hs">AI生成中...</span></div></div></div>'),it=l('<div class="menu-toggle-container svelte-3pt5hs"><button id="menu-toggle" class="menu-toggle-float svelte-3pt5hs" aria-label="打开菜单"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-3pt5hs"><path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path></svg></button> <a href="/enhanced-voice-test" class="test-entry-float svelte-3pt5hs" aria-label="语音识别测试"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-3pt5hs"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2" class="svelte-3pt5hs"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path><path d="M12 18V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path><path d="M8 22H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path></svg></a> <a href="/enhanced-speed-reading" class="speed-test-entry-float svelte-3pt5hs" aria-label="增强秒词测试"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-3pt5hs"><path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path></svg></a></div>'),ct=l('<div class="loading-overlay svelte-3pt5hs"><div class="loading-spinner svelte-3pt5hs"><i class="fas fa-spinner fa-spin svelte-3pt5hs"></i></div></div>'),vt=l('<button class="action-icon-btn cancel-btn svelte-3pt5hs" aria-label="取消"><i class="fas fa-times svelte-3pt5hs"></i></button>'),dt=l('<button class="edit-icon-btn svelte-3pt5hs" aria-label="编辑"><i class="fas fa-edit svelte-3pt5hs"></i></button>'),ut=l('<button class="action-icon-btn save-btn svelte-3pt5hs" aria-label="保存"><i class="fas fa-check svelte-3pt5hs"></i></button>'),gt=l('<div class="progress-container svelte-3pt5hs"><svg class="progress-circle svelte-3pt5hs" viewBox="0 0 100 100"><circle class="progress-background svelte-3pt5hs" cx="50" cy="50" r="45"></circle><circle class="progress-bar svelte-3pt5hs" cx="50" cy="50" r="45" stroke-dasharray="283"></circle></svg></div>'),ht=l('<span class="append-text svelte-3pt5hs">追加</span>'),pt=l('<button aria-label="语音输入"><i></i> <!> <!></button>'),mt=l('<p class="svelte-3pt5hs"> </p>'),ft=l('<br class="svelte-3pt5hs">'),bt=l('<p class="placeholder svelte-3pt5hs">等待语音输入...</p>'),wt=l('<textarea class="asr-text-edit svelte-3pt5hs" placeholder="等待语音输入..." rows="6"></textarea>'),yt=l('<i class="fas fa-spinner fa-spin svelte-3pt5hs"></i> 生成中',1),xt=l('<i class="fas fa-eye svelte-3pt5hs"></i> 预览',1),qt=l('<p class="svelte-3pt5hs"> </p>'),It=l('<br class="svelte-3pt5hs">'),$t=l('<p class="placeholder svelte-3pt5hs">AI正在生成内容...</p>'),kt=l('<div class="preview-result-container svelte-3pt5hs"><div class="preview-content svelte-3pt5hs"><!></div></div>'),Ct=l('<div class="asr-control-buttons svelte-3pt5hs"><button class="asr-control-btn reset-btn svelte-3pt5hs"><i class="fas fa-redo svelte-3pt5hs"></i> 重录</button> <div class="button-spacer svelte-3pt5hs"></div> <button class="asr-control-btn preview-btn svelte-3pt5hs"><!></button></div> <!>',1),Mt=l('<div class="card-image asr-preview-area svelte-3pt5hs"><div class="asr-preview-container svelte-3pt5hs"><div class="asr-preview-content svelte-3pt5hs"><!></div> <!></div></div>'),At=l('<div class="card-image fullpage-image editor-mode svelte-3pt5hs"><!></div>'),Tt=l('<div class="card-image fullpage-image editor-mode svelte-3pt5hs"><!></div>'),St=l('<div class="card-description svelte-3pt5hs"><div class="editor-container svelte-3pt5hs"><div class="input-area svelte-3pt5hs"><textarea placeholder="请输入内容描述..." rows="6"></textarea></div></div> <div><div class="voice-control-container svelte-3pt5hs"><!></div> <div class="hidden-asr svelte-3pt5hs"><!></div></div></div> <!>',1),zt=l('<p class="svelte-3pt5hs"> </p>'),jt=l('<br class="svelte-3pt5hs">'),Pt=l('<p class="placeholder svelte-3pt5hs">暂无内容描述</p>'),Rt=l('<div class="card-image fullpage-image svelte-3pt5hs"><!></div> <div class="card-description svelte-3pt5hs"><div class="text-display text-[0.9rem] svelte-3pt5hs"><!></div></div>',1),Et=l('<div class="dashboard-container svelte-3pt5hs"><!> <!> <!> <!> <div class="flex h-full svelte-3pt5hs"><div class="flex-1 flex flex-col svelte-3pt5hs"><!> <!> <main class="main-content full-height svelte-3pt5hs"><!> <div class="fullpage-card svelte-3pt5hs"><div class="card-header svelte-3pt5hs"><div class="header-left svelte-3pt5hs"><!></div> <h3 class="card-title svelte-3pt5hs"> </h3> <div class="header-right svelte-3pt5hs"><!></div></div> <!> <!></div></main></div></div> <!></div>');function Dt(l,A){e(A,!1);const[pe,me]=g(),fe=()=>C(ie,"$difyState",pe),be=()=>C(Z,"$leafNodes",pe),we=()=>C(Q,"$selectedMenuItem",pe),ye=()=>C(Y,"$isAdmin",pe);let xe=t(A,"type",8,null),qe=m(!1),Ie=m(!1),$e=m({title:"确认",message:"",type:"warning",confirmText:"确认",cancelText:"取消",showCancelButton:!0,onConfirm:()=>{},onCancel:()=>{}});function Ce(e){h($e,{...p($e),...e}),h(Ie,!0)}let Me,Ae,Te=m({title:"加载中...",description:"正在加载模块数据...",carouselItems:[]}),Se=m(new Set),ze=m({prev:{title:"",path:"",mcode:""},next:{title:"",path:"",mcode:""}}),je=m(""),Pe=m(!0),Ee=m(!1),De=m({description:"",carouselItems:[]}),Le=m(!1),Be=m(!1),Dt=m(!1),Lt=m(!1),Bt="",Wt=m(""),_t=m(),Ot=m(!0),Xt=m(60),Ht=0,Yt=0,Nt=m(null),Ut="",Vt="",Ft="",Gt=m(""),Jt="",Zt=0,Qt=null,Kt=!1,es=m(!1),ts=null,ss=m(!1),as=m(!1),ns=null,ls=null,rs=m(!1);function os(){ls&&(ls(),ls=null),p(De).description;const e=p(Gt);ns=ve(p(De).description,e,C(V,"$user",pe)?.uid||""),h(Gt,""),h(ss,!1);const t=ie.subscribe(e=>{e.streamingContent&&j(De,p(De).description=e.streamingContent),e.isComplete&&e.streamingContent&&(N("内容已自动优化","success"),e.isComplete&&e.streamingContent&&setTimeout(()=>{ie.update(e=>({...e,streamingContent:"",isProcessing:!1,isComplete:!1}))},1500),t(),ls=null)});ls=t}function is(){_(F,!C(F,"$isDrawerOpen",pe))}async function cs(){try{let e=null;if("undefined"!=typeof window){e=new URLSearchParams(window.location.search).get("partnerEName")||null}const t=await ne(xe(),e);_(G,t.menuItems),t.partnerName&&_(J,t.partnerName);const s=function(e){const t=[];return function e(s){if(s&&s.length)for(const a of s)a&&(a.children&&0!==a.children.length?e(a.children):t.push({...a}))}(e),t}(t.menuItems||[]);_(Z,s),console.log("提取的叶子节点:",s),s.length>0&&(_(Q,s[0]),console.log("默认选择第一个叶子节点:",s[0]))}catch(e){console.error("加载菜单数据失败:",e),N("加载菜单数据失败: "+e.message,"error")}}async function vs(){h(Pe,!0);try{if(p(je))try{const e=await le(p(je));if(e)return console.log("通过API获取到模块内容:",e),h(Te,{title:e.name,description:e.description,carouselItems:e.images?e.images.map(t=>({image:t,alt:e.name||""})):[],moduleId:e.moduleId}),void console.log("已更新recommendationData:",p(Te))}catch(e){console.error("通过API获取模块内容失败:",e)}}catch(t){console.error("加载模块数据失败:",t),N("加载模块数据失败: "+t.message,"error"),h(Te,{title:"模块加载失败",description:"无法加载请求的模块数据，请稍后重试。",carouselItems:[]})}finally{h(Pe,!1)}}function ds(e){const t=new Set,s=[];for(const a of e)if(t.has(a.image)){const e=s.findIndex(e=>e.image===a.image);e>=0&&!0===a.isTemporary&&(s[e].isTemporary=!0)}else t.add(a.image),s.push(a);return s}function us(e,t){if(t&&t!==p(je)&&!p(Pe)){if(h(je,t),be()&&be().length>0){const e=be().find(e=>e.mcode===t);e&&_(Q,e)}vs()}}function gs(){ye()&&!p(Pe)&&(h(De,{description:p(Te).description||"",carouselItems:[...p(Te).carouselItems||[]]}),h(Ee,!0),h(as,!1),setTimeout(()=>{ts=document.querySelector(".hidden-asr .asr-talk-btn"),console.log("已缓存ASR按钮:",ts?"成功":"失败"),p(_t)&&p(_t).preInitialize?(console.log("开始预初始化ASR组件"),p(_t).preInitialize().then(e=>{console.log("ASR组件预初始化",e?"成功":"失败"),e?h(as,!0):(h(as,!0),N("语音识别功能准备未完成，可能影响使用","warning"))}).catch(e=>{console.error("ASR组件预初始化错误:",e),h(as,!0),N("语音识别功能初始化失败","error")})):h(as,!0)},300))}function hs(){if(!p(De))return!1;const e=p(De).description!==(p(Te).description||""),t=p(Te).carouselItems?p(Te).carouselItems.length:0;if(t!==(p(De).carouselItems?p(De).carouselItems.length:0))return!0;if(t>0)for(let s=0;s<t;s++){const e=p(Te).carouselItems[s],t=p(De).carouselItems[s];if(e.image!==t.image)return!0}return e}async function ps(){!fe().isProcessing||fe().isComplete?async function(){let e=[...p(De).carouselItems];if(e.length>9)return void N("图片数量超过限制，最多支持保存9张图片","warning");try{de.resetProgress();const s=e.some(e=>e.image.startsWith("blob:")||e.image.startsWith("data:")||!0===e.isTemporary);if(s&&(h(qe,!0),e=await de.processImages(e)),console.log("保存模块数据:",{currentModuleCode:p(je),recommendationData:p(Te),moduleId:p(Te).moduleId}),!p(Te).moduleId&&p(je))try{console.log("尝试重新加载模块数据获取moduleId...");const e=await le(p(je));if(!e||!e.moduleId)return console.error("仍然无法获取moduleId"),N("保存失败：无法获取模块ID，请刷新页面重试","error"),void h(qe,!1);console.log("获取到moduleId:",e.moduleId),j(Te,p(Te).moduleId=e.moduleId)}catch(t){return console.error("重新获取moduleId失败:",t),N("保存失败：无法获取模块ID，请刷新页面重试","error"),void h(qe,!1)}const a={moduleId:p(Te).moduleId,description:p(De).description,carouselItems:e};if(!a.moduleId)return N("保存失败：无法获取模块ID，请刷新页面重试","error"),void h(qe,!1);s&&K.update(e=>({...e,status:"saving"})),await ue(p(je),a),h(Te,{...p(Te),description:p(De).description,carouselItems:e}),h(Ee,!1),h(Dt,!1),s?(K.update(e=>({...e,status:"completed"})),setTimeout(()=>{N("更新成功：所有图片已上传并保存","success")},3500)):N("更新成功","success")}catch(t){console.error("保存失败:",t),N(`保存失败: ${t.message}`,"error"),K.update(e=>({...e,status:"error"})),setTimeout(()=>{h(qe,!1)},3e3)}}():Ce({title:"提示",message:"AI生成还未完成，请等待生成完成后再保存",type:"warning",confirmText:"我知道了",showCancelButton:!1,onConfirm:()=>{console.log("用户已知晓生成未完成")}})}function ms(){!fe().isProcessing||fe().isComplete?hs()?Ce({title:"确认取消",message:"现在取消将不会保存之前编辑的结果，是否继续？",type:"error",confirmText:"继续取消",cancelText:"返回编辑",onConfirm:()=>{fs()},onCancel:()=>{console.log("用户返回继续编辑")}}):fs():Ce({title:"提示",message:"AI生成还未完成，现在退出将丢失生成的内容，是否继续？",type:"warning",confirmText:"退出编辑",cancelText:"继续等待",showCancelButton:!0,onConfirm:()=>{fs()},onCancel:()=>{console.log("用户选择继续等待AI生成")}})}function fs(){h(Ee,!1),h(Le,!1),h(Be,!1),ee(),h(Dt,!1),p(Lt)&&$s(),Os(),h(ss,!1),h(Gt,""),Jt="",ts=null,h(es,!1),h(as,!1)}function bs(e){console.log("轮播图编辑",e.detail),j(De,p(De).carouselItems=[...e.detail])}function ws(e){p(Se).add(e),h(Se,p(Se))}function ys(e){if(e&&"touchstart"===e.type&&e.preventDefault(),Vs=!1,Fs&&(clearTimeout(Fs),Fs=null),console.log("用户按下录音按钮"),document.activeElement===p(Nt)&&p(Nt)&&p(Nt).blur(),document.activeElement===p(Hs)&&p(Hs)&&p(Hs).blur(),h(es,!0),Zt=Date.now(),Kt=!1,Qt=setTimeout(()=>{Kt=!0,h(Ot,!0),xs()},200),p(Lt)&&!p(Ot))return clearTimeout(Qt),void Is(e)}function xs(){p(Ee)&&(h(Lt,!0),h(es,!1),document.activeElement===p(Nt)&&p(Nt)&&p(Nt).blur(),document.activeElement===p(Hs)&&p(Hs)&&p(Hs).blur(),Bt=p(De).description||"",p(Nt)&&(Yt=p(Nt).selectionStart),h(Wt,""),Ut="",Vt="",Ft="",Ht=0,p(ss)?Jt=p(Gt):(h(Gt,""),Jt=""),h(Xt,60),zs=Ss(),Rs(),p(_t)&&p(_t).startRecording?(console.log("使用API接口启动ASR"),p(_t).startRecording().catch(e=>{console.error("API启动ASR失败:",e),ts?(console.log("回退到缓存按钮"),ts.click()):qs()})):ts?(console.log("使用缓存的ASR按钮"),ts.click()):qs())}function qs(){console.log("未找到缓存的ASR按钮，重新查询DOM");const e=document.querySelectorAll(".hidden-asr .asr-talk-btn");e&&e.length>0?(ts=e[0],ts.click()):(console.error("未找到ASR组件"),N("无法启动语音识别","error"),h(Lt,!1),Es(),zs&&(zs(),zs=null))}function Is(e){e&&"touchend"===e.type&&e.preventDefault(),Vs=!1,Fs&&(clearTimeout(Fs),Fs=null),console.log("用户释放录音按钮"),p(Lt)||h(es,!1),Qt&&(clearTimeout(Qt),Qt=null);Date.now()-Zt<200&&!Kt?p(Lt)?p(Ot)||($s(),h(es,!1)):(h(Ot,!1),xs()):Kt&&p(Lt)&&($s(),h(es,!1))}function $s(){if(p(Lt)&&p(Ee))if(p(_t)&&p(_t).stopRecording)console.log("使用API接口停止ASR"),p(_t).stopRecording().catch(e=>{if(console.error("API停止ASR失败:",e),ts)ts.click();else{const e=document.querySelectorAll(".hidden-asr .asr-talk-btn");e&&e.length>0&&(ts=e[0],ts.click())}});else if(ts)ts.click();else{const e=document.querySelectorAll(".hidden-asr .asr-talk-btn");e&&e.length>0&&(ts=e[0],ts.click())}}function ks(e){const{uuid:t,username:s}=e.detail;h(Be,!0),te({uuid:t,username:s}).then(e=>{if(e&&e.length>0&&p(Ee)){const t=e.map(e=>({...e,isTemporary:!0})),a=p(De).carouselItems.length+t.length;a>9?(h(Dt,!0),j(De,p(De).carouselItems=[...p(De).carouselItems,...t])):j(De,p(De).carouselItems=[...p(De).carouselItems,...t]),j(De,p(De).carouselItems=ds(p(De).carouselItems));const n=a-p(De).carouselItems.length;N(`已从"${s}"接收${e.length}张图片${n>0?`，去除重复图片${n}张`:""}`,"success")}h(Be,!1)}).catch(e=>{h(Be,!1)})}function Cs(){h(Le,!1),ee()}function Ms(){h(Le,!0)}function As(e){h(Dt,e.detail)}function Ts(e){const{path:t,mcode:s}=e.detail;!function(e,t){p(Ee)?hs()?Ce({title:"确认离开",message:"您有未保存的更改，确定要离开吗？",type:"warning",confirmText:"离开页面",cancelText:"继续编辑",onConfirm:()=>{ms(),us(0,t)},onCancel:()=>{console.log("用户取消了页面切换")}}):(ms(),us(0,t)):us(0,t)}(0,s)}function Ss(){let e;if(p(Lt))return e=setInterval(()=>{!function(e){const t=Math.min(100,Math.max(0,e)),s=document.querySelectorAll(".volume-bars .volume-bar");if(s&&s.length>0){const e=5,a=40;s.forEach((s,n)=>{const l=n*(Math.PI/3),r=Math.sin(.005*Date.now()+l),o=e+t/100*.7*(a-e)+.15*(r+1)*(a-e);s.style.height=`${o}px`})}}(30+50*Math.random())},50),()=>{e&&clearInterval(e)}}U(),a(()=>{void 0===xe()&&cs(),vs(),_(V,{id:"1",name:"Admin User",role:"admin",isLoggedIn:!0}),h(rs,/MicroMessenger/i.test(navigator.userAgent)),console.log("是否在微信环境中:",p(rs)),Me=se.subscribe(e=>{if(e&&e.length>0&&p(Ee)){const t=e.map(e=>({...e,isTemporary:!0})),s=p(De).carouselItems.length+t.length;s>9?(h(Dt,!0),j(De,p(De).carouselItems=[...p(De).carouselItems,...t])):j(De,p(De).carouselItems=[...p(De).carouselItems,...t]),j(De,p(De).carouselItems=ds(p(De).carouselItems));const a=s-p(De).carouselItems.length;N(`延迟接收到${e.length}张图片${a>0?`，去除重复图片${a}张`:""}`,"success")}}),Ae=ae.subscribe(e=>{e&&""!==e.trim()&&(N(e,"warning"),h(Be,!1))})}),M(()=>{ee(),Me&&Me(),Ae&&Ae(),Es(),zs&&zs(),Fs&&(clearTimeout(Fs),Fs=null),ns&&ns(),ls&&(ls(),ls=null)});let zs=null;function js(){""===Ut&&(Vt=Bt.substring(0,Yt),Ft=Bt.substring(Yt)),Ut=p(Wt),""===Bt||(!Vt.endsWith(" ")&&Vt.length,!Ft.startsWith(" ")&&Ft.length)}let Ps=null;function Rs(){Es(),Ps=setInterval(()=>{p(Xt)>0?h(Xt,p(Xt)-1):(Es(),p(Lt)&&$s())},1e3)}function Es(){Ps&&(clearInterval(Ps),Ps=null)}function Ds(e){console.log("ASR组件开始录音"),p(Ee)&&!p(Lt)&&(h(Lt,!0),document.activeElement===p(Nt)&&p(Nt)&&p(Nt).blur(),document.activeElement===p(Hs)&&p(Hs)&&p(Hs).blur(),Bt=p(De).description||"",h(ss,!0),p(Nt)&&(Yt=p(Nt).selectionStart),h(Wt,""),Ut="",Vt="",Ft="",p(Gt)||(Jt=""),h(Xt,60),zs=Ss(),Rs())}function Ls(e){const{content:t,status:s}=e.detail;if(p(Ee)&&t&&p(Lt)){if(Ht>0&&t.length<Ht/2){if(console.log("检测到语音识别重新累加，保存前一次结果并重新开始"),p(Wt).trim().length>0){const e=p(De).description||"";Bt=e,p(Nt)&&(Yt=Vt.length+(Vt.length>0&&!Vt.endsWith(" ")?1:0)+p(Wt).length),Ut="",Vt="",Ft="",Jt=p(Gt)}h(Wt,t),h(Gt,Jt+(Jt.length>0?"\n":"")+t)}else h(Wt,t),h(Gt,Jt+(Jt.length>0?"\n":"")+t);js(),Ht=t.length,console.log("语音识别结果更新:",t,"状态:",s)}}function Bs(e){console.log("ASR组件停止录制，等待结果返回"),p(Lt)&&p(Ee)&&(console.log("等待语音识别完成..."),p(Ot)&&document.querySelector(".recording-status-text span")&&(document.querySelector(".recording-status-text span").textContent="正在处理..."))}function Ws(e){const{content:t}=e.detail;console.log("语音识别完成:",t),p(Ee)&&(t?(h(Wt,t),js(),h(Gt,Jt+(Jt.length>0?"\n":"")+t),h(ss,!0),N("已添加语音输入结果","success")):N("未识别到有效内容","warning"),Os())}function _s(e){if(console.log("ASR WebSocket连接关闭"),p(Lt)){if(""===p(Wt)||0===p(Wt).trim().length){const e=60-p(Xt);e<2?N("未检测到语音内容，录音时间太短","warning"):e>=2&&N("未检测到语音内容，请靠近麦克风或检查麦克风设置","warning")}Os(),p(Wt)&&p(Wt).trim().length>0&&N("语音识别连接已断开","info")}}function Os(){h(Lt,!1),Ht=0,Ut="",Vt="",Ft="",Es(),zs&&(zs(),zs=null)}function Xs(e){const{error:t}=e.detail;console.error("语音识别错误:",t),Os(),N("语音识别出错: "+(t.message||"未知错误"),"error")}let Hs=m(null);function Ys(e){h(Gt,e.target.value)}let Ns=0,Us=0,Vs=!1;let Fs=null;function Gs(e){p(Lt)&&p(Ot)&&e&&e.touches&&e.touches[0]&&(e.preventDefault(),Fs&&clearTimeout(Fs),Fs=setTimeout(()=>{if(!Vs)return Ns=e.touches[0].clientX,Us=e.touches[0].clientY,void(Vs=!0);const t=e.touches[0].clientX,s=e.touches[0].clientY,a=Math.abs(t-Ns),n=Math.abs(s-Us),l=Math.sqrt(a*a+n*n);if(l>30){const a=e.currentTarget.getBoundingClientRect(),n=a.left-10,r=a.right+10,o=a.top-10,i=a.bottom+10;(t<n||t>r||s<o||s>i)&&(console.log("触摸移出按钮区域，停止录音",{distance:l,position:{x:t,y:s},buttonRect:{left:a.left,right:a.right,top:a.top,bottom:a.bottom}}),Vs=!1,Kt&&p(Lt)&&($s(),h(es,!1)))}},150))}S(()=>(P(xe()),V),()=>{void 0!==xe()&&V&&cs()}),S(()=>(we(),p(je),p(Pe),p(Ee)),()=>{we()&&we().mcode&&(console.log("AppLayout监听到selectedMenuItem变化:",we()),_(F,!1),we().mcode===p(je)||p(Pe)||(p(Ee)?hs()?Ce({title:"确认切换",message:"您有未保存的更改，确定要切换到新模块吗？",type:"warning",confirmText:"切换模块",cancelText:"继续编辑",onConfirm:()=>{ms(),console.log("切换到新模块:",we().mcode),h(je,we().mcode),vs()},onCancel:()=>{console.log("用户取消了模块切换")}}):(ms(),console.log("切换到新模块:",we().mcode),h(je,we().mcode),vs()):(console.log("切换到新模块:",we().mcode),h(je,we().mcode),vs())))}),S(()=>(p(je),p(Pe)),()=>{p(je)&&!p(Pe)&&function(){try{if(be()&&be().length>0){const e=re(p(je),be()),t=e.currentIndex,s=0===t,a=t===be().length-1;if(e.prev&&!s)j(ze,p(ze).prev={title:e.prev.title,mcode:e.prev.mcode,path:`/${e.prev.mcode}`});else{const e=be()[t];j(ze,p(ze).prev={title:e.name,path:"",mcode:""})}if(e.next&&!a)j(ze,p(ze).next={title:e.next.title,mcode:e.next.mcode,path:`/${e.next.mcode}`});else{const e=be()[t];j(ze,p(ze).next={title:e.name,path:"",mcode:""})}}else h(ze,oe())}catch(e){console.error("更新导航数据失败:",e),N("更新导航数据失败: "+e.message,"error"),h(ze,oe())}}()}),z(),n(),he(l,{children:(a,l)=>{var A=Et(),_=r(A),Y=e=>{var t=nt(),s=r(t),a=r(s,!0);i(s),i(t),x(()=>T(a,p(Xt))),d(e,t)};o(_,e=>{!p(Ot)&&p(Lt)&&p(Xt)<=10&&e(Y)});var N=c(_,2),U=e=>{var t=lt();d(e,t)};o(N,e=>{p(Lt)&&p(Ot)&&e(U)});var V=c(N,2),F=e=>{var t=rt();d(e,t)},G=(e,t)=>{var s=e=>{var t=ot();d(e,t)};o(e,e=>{fe().isProcessing&&fe().streamingContent&&e(s)},t)};o(V,e=>{fe().isProcessing&&!fe().streamingContent?e(F):e(G,!1)});var J=c(V,2);!function(s,a){e(a,!1);const[l,f]=g(),b=()=>C(K,"$uploadProgress",l),q=m(),I=m(),$=m(),A=m();let j=t(a,"visible",8,!1),R=t(a,"onClose",8,()=>{});function _(){p(q)&&R()()}let O=m();M(()=>{p(O)&&clearTimeout(p(O))}),S(()=>b(),()=>{h(q,"completed"===b().status||"error"===b().status)}),S(()=>b(),()=>{h(I,"saving"===b().status?100:b().total>0?Math.floor(b().current/b().total*100):0)}),S(()=>b(),()=>{var e;h($,"uploading"===(e=b()).status?`正在上传图片 (${e.current}/${e.total})`:"saving"===e.status?"正在保存数据":"completed"===e.status?"上传完成":"completed_with_errors"===e.status?"上传部分完成":"error"===e.status?"上传出现问题":"准备中...")}),S(()=>b(),()=>{var e;h(A,"completed"===(e=b()).status?e.failed>0?`成功: ${e.success}张，失败: ${e.failed}张`:`全部 ${e.success} 张图片上传成功`:"completed_with_errors"===e.status?e.errorMessage||`成功: ${e.success}张，失败: ${e.failed}张`:"error"===e.status?e.errorMessage||"上传过程中发生错误":"saving"===e.status?"正在保存更新内容...":"")}),S(()=>(p(q),P(j()),P(R())),()=>{p(q)&&j()&&h(O,setTimeout(()=>{R()()},3e3))}),z(),n();var X=w(),H=y(X),Y=e=>{var t=tt(),s=r(t),a=r(s),n=r(a),l=r(n),u=r(l,!0);i(l);var g=c(l,2),h=e=>{var t=Ne();v("click",t,_),d(e,t)};o(g,e=>{p(q)&&e(h)}),i(n);var m=c(n,2),f=r(m),w=r(f);i(f);var C=c(f,2),M=r(C),S=e=>{var t=Ve(),s=y(t),a=r(s);i(s);var n=c(s,2),l=r(n);i(n);var v=c(n,2),u=e=>{var t=Ue(),s=r(t);i(t),x(()=>T(s,`失败: ${b().failed??""} 张`)),d(e,t)};o(v,e=>{b().failed>0&&e(u)}),x(()=>{T(a,`正在处理第 ${b().current??""} 张，共 ${b().total??""} 张图片`),T(l,`已成功: ${b().success??""} 张`)}),d(e,t)},z=(e,t)=>{var s=e=>{var t=Ge(),s=c(y(t),2),a=r(s);i(s);var n=c(s,2),l=e=>{var t=Fe(),s=r(t);i(t),x(()=>T(s,`失败: ${b().failed??""} 张`)),d(e,t)};o(n,e=>{b().failed>0&&e(l)}),E(4),x(()=>T(a,`成功: ${b().success??""} 张`)),d(e,t)},a=(e,t)=>{var s=e=>{var t=Ke(),s=y(t),a=r(s,!0);i(s);var n=c(s,2),l=e=>{var t=Je();d(e,t)},v=(e,t)=>{var s=e=>{var t=Ze();d(e,t)},a=(e,t)=>{var s=e=>{var t=Qe();d(e,t)};o(e,e=>{"error"===b().status&&e(s)},t)};o(e,e=>{"completed_with_errors"===b().status||"completed"===b().status&&b().failed>0?e(s):e(a,!1)},t)};o(n,e=>{"completed"===b().status&&0===b().failed?e(l):e(v,!1)}),x(()=>T(a,p(A))),d(e,t)};o(e,e=>{p(q)&&e(s)},t)};o(e,e=>{"saving"===b().status?e(s):e(a,!1)},t)};o(M,e=>{"uploading"===b().status?e(S):e(z,!1)}),i(C),i(m);var j=c(m,2),P=e=>{var t=et(),s=r(t);i(t),v("click",s,_),d(e,t)};o(j,e=>{p(q)&&e(P)}),i(a),i(s),i(t),x(()=>{T(u,p($)),k(w,`width: ${p(I)??""}%;`)}),D(3,s,()=>L,()=>({duration:200,start:.8})),v("click",s,B(()=>{})),D(3,t,()=>W,()=>({duration:200})),v("click",t,_),d(e,t)};o(H,e=>{j()&&e(Y)}),d(s,X),u(),f()}(J,{get visible(){return p(qe)},onClose:()=>h(qe,!1)});var Z=c(J,2),Q=r(Z),ee=r(Q),te=e=>{var t=it(),s=r(t);E(4),i(t),v("click",s,is),d(e,t)};o(ee,e=>{p(Ee)||e(te)});var se=c(ee,2);We(se,{get showScanner(){return p(Le)},$$events:{scan:ks,close:Cs}});var ae=c(se,2),ne=r(ae),le=e=>{var t=ct();d(e,t)};o(ne,e=>{p(Pe)&&e(le)});var re=c(ne,2),oe=r(re),ie=r(oe),ve=r(ie),de=e=>{var t=vt();v("click",t,ms),d(e,t)};o(ve,e=>{p(Ee)&&e(de)}),i(ie);var ue=c(ie,2),he=r(ue,!0);i(ue);var me=c(ue,2),be=r(me),we=e=>{var t=dt();v("click",t,gs),d(e,t)},xe=(e,t)=>{var s=e=>{var t=ut();v("click",t,ps),d(e,t)};o(e,e=>{p(Ee)&&e(s)},t)};o(be,e=>{!ye()||p(Pe)||p(Ee)?e(xe,!1):e(we)}),i(me),i(oe);var Ce=c(oe,2),Me=e=>{var t=St(),s=y(t),a=r(s),n=r(a),l=r(n);let u;O(l),R(l,e=>h(Nt,e),()=>p(Nt)),i(n),i(a);var g=c(a,2);let m;var k=r(g),C=r(k),M=e=>{var t=pt();let s;var a=r(t),n=c(a,2),l=e=>{var t=gt(),s=r(t),a=c(r(s));i(s),i(t),x(()=>$(a,"stroke-dashoffset",283-p(Xt)/60*283)),d(e,t)};o(n,e=>{!p(Ot)&&p(Lt)&&e(l)});var u=c(n,2),g=e=>{var t=ht();d(e,t)};o(u,e=>{p(ss)&&!p(Lt)&&p(Gt)&&e(g)}),i(t),x(e=>{s=I(t,1,"voice-record-btn svelte-3pt5hs",null,s,e),t.disabled=!p(as),I(a,1,`fas ${(p(Lt)?"fa-stop":p(ss)&&p(Gt)?"fa-plus":"fa-microphone")??""}`,"svelte-3pt5hs")},[()=>({recording:p(Lt),preparing:p(es)&&!p(Lt),"append-btn":p(ss)&&!p(Lt),disabled:!p(as)})],q),v("mousedown",t,ys),v("mouseup",t,Is),v("mouseleave",t,Is),v("touchstart",t,H(ys)),v("touchmove",t,H(Gs)),v("touchend",t,H(Is)),v("touchcancel",t,H(Is)),d(e,t)};o(C,e=>{(!fe().isProcessing||fe().isComplete&&!fe().isProcessing)&&e(M)}),i(k);var A=c(k,2),S=r(A);R(ge(S,{direction:"left",duration:60,showTextPanel:!1,$$events:{start:Ds,textInput:Ls,textCompleted:Ws,stop:Bs,error:Xs,close:_s},$$legacy:!0}),e=>h(_t,e),()=>p(_t)),i(A),i(g),i(s);var z=c(s,2),P=e=>{var t=Mt(),s=r(t),a=r(s),n=r(a),l=e=>{var t=w(),s=y(t),a=e=>{var t=w(),s=y(t);f(s,1,()=>p(Gt).split("\n"),b,(e,t)=>{var s=w(),a=y(s),n=e=>{var s=mt(),a=r(s,!0);i(s),x(()=>T(a,p(t))),d(e,s)},l=e=>{var t=ft();d(e,t)};o(a,e=>{p(t).trim()?e(n):e(l,!1)}),d(e,s)}),d(e,t)},n=e=>{var t=bt();d(e,t)};o(s,e=>{p(Gt).trim().length>0?e(a):e(n,!1)}),d(e,t)},u=e=>{var t=wt();O(t),R(t,e=>h(Hs,e),()=>p(Hs)),X(t,()=>p(Gt),e=>h(Gt,e)),v("input",t,Ys),d(e,t)};o(n,e=>{p(Lt)?e(l):e(u,!1)}),i(a);var g=c(a,2),m=e=>{var t=Ct(),s=y(t),a=r(s),n=c(a,4),l=r(n),u=e=>{var t=yt();E(),d(e,t)},g=e=>{var t=xt();E(),d(e,t)};o(l,e=>{fe().isProcessing?e(u):e(g,!1)}),i(n),i(s);var m=c(s,2),q=e=>{var t=kt(),s=r(t),a=r(s),n=e=>{var t=w(),s=y(t);f(s,1,()=>fe().streamingContent.split("\n"),b,(e,t)=>{var s=w(),a=y(s),n=e=>{var s=qt(),a=r(s,!0);i(s),x(()=>T(a,p(t))),d(e,s)},l=e=>{var t=It();d(e,t)};o(a,e=>{p(t).trim()?e(n):e(l,!1)}),d(e,s)}),d(e,t)},l=e=>{var t=$t();d(e,t)};o(a,e=>{fe().streamingContent?e(n):e(l,!1)}),i(s),i(t),d(e,t)};o(m,e=>{(fe().isProcessing||fe().streamingContent)&&e(q)}),x(()=>n.disabled=fe().isProcessing||!p(Gt)),v("click",a,()=>{h(Wt,""),h(Gt,"")}),v("click",n,os),d(e,t)};o(g,e=>{!p(Lt)&&p(ss)&&e(m)}),i(s),i(t),d(e,t)},D=(e,t)=>{var s=e=>{var t=At();Re(r(t),{get images(){return p(De).carouselItems},get loadedImages(){return p(Se)},get isScanning(){return p(Be)},get showMaxImagesWarning(){return p(Dt)},maxImages:9,$$events:{scan:Ms,change:bs,imageload:e=>ws(e.detail),warningupdate:As}}),i(t),d(e,t)},a=e=>{var t=Tt();Re(r(t),{get images(){return p(De).carouselItems},get loadedImages(){return p(Se)},get isScanning(){return p(Be)},get showMaxImagesWarning(){return p(Dt)},maxImages:9,$$events:{scan:Ms,change:bs,imageload:e=>ws(e.detail),warningupdate:As}}),i(t),d(e,t)};o(e,e=>{fe().isProcessing?e(s):e(a,!1)},t)};o(z,e=>{p(Lt)||p(ss)&&!fe().isProcessing&&!fe().streamingContent?e(P):e(D,!1)}),x((e,t)=>{u=I(l,1,"text-input svelte-3pt5hs",null,u,e),l.disabled=fe().isProcessing,m=I(g,1,"voice-input-container svelte-3pt5hs",null,m,t)},[()=>({streaming:fe().isProcessing&&fe().streamingContent}),()=>({"with-asr-preview":p(ss)})],q),X(l,()=>p(De).description,e=>j(De,p(De).description=e)),d(e,t)},Ae=e=>{var t=Rt(),s=y(t);ke(r(s),{get items(){return p(Te).carouselItems},get loadingImages(){return C(ce,"$loadingImages",pe)},get loadedImages(){return p(Se)},$$events:{scan:Ms,imageload:e=>ws(e.detail)}}),i(s);var a=c(s,2),n=r(a),l=r(n),v=e=>{var t=w(),s=y(t);f(s,1,()=>p(Te).description.split("\n"),b,(e,t)=>{var s=w(),a=y(s),n=e=>{var s=zt(),a=r(s,!0);i(s),x(()=>T(a,p(t))),d(e,s)},l=e=>{var t=jt();d(e,t)};o(a,e=>{p(t).trim()?e(n):e(l,!1)}),d(e,s)}),d(e,t)},u=e=>{var t=Pt();d(e,t)};o(l,e=>{p(Te).description?e(v):e(u,!1)}),i(n),i(a),x(()=>k(a,"padding-bottom: "+(p(rs)?"4rem":"0"))),d(e,t)};o(Ce,e=>{p(Ee)?e(Me):e(Ae,!1)});var je=c(Ce,2),Bt=a=>{!function(a,l){e(l,!1);const g=s();let h=t(l,"prevPage",24,()=>({title:"",path:"",mcode:""})),p=t(l,"nextPage",24,()=>({title:"",path:"",mcode:""})),m=t(l,"isLoading",8,!1),f=t(l,"isWechat",8,!1);function b(e,t){e&&t&&g("navigate",{path:e,mcode:t})}n();var w=Ye();let y;var $=r(w),k=e=>{var t=_e(),s=c(r(t),2),a=r(s),n=r(a,!0);i(a),i(s),i(t),x(()=>{t.disabled=m(),T(n,h().title)}),v("click",t,()=>b(h().path,h().mcode)),d(e,t)},C=e=>{var t=Oe();t.disabled=!0;var s=c(r(t),2),a=r(s),n=r(a,!0);i(a),i(s),i(t),x(()=>T(n,h().title)),d(e,t)};o($,e=>{h()&&h().mcode?e(k):e(C,!1)});var M=c($,2),A=e=>{var t=Xe(),s=r(t),a=r(s),n=r(a,!0);i(a),i(s),E(2),i(t),x(()=>{t.disabled=m(),T(n,p().title)}),v("click",t,()=>b(p().path,p().mcode)),d(e,t)},S=e=>{var t=He();t.disabled=!0;var s=r(t),a=r(s),n=r(a,!0);i(a),i(s),E(2),i(t),x(()=>T(n,p().title)),d(e,t)};o(M,e=>{p()&&p().mcode?e(A):e(S,!1)}),i(w),x(e=>y=I(w,1,"page-navigation svelte-30ryhq",null,y,e),[()=>({"wechat-fixed-nav":f()})],q),d(a,w),u()}(a,{get prevPage(){return p(ze).prev},get nextPage(){return p(ze).next},get isLoading(){return p(Pe)},get isWechat(){return p(rs)},$$events:{navigate:Ts}})};o(je,e=>{p(Ee)||e(Bt)}),i(re),i(ae),i(Q),i(Z),function(a,l){e(l,!1);let g=t(l,"visible",12,!1),h=t(l,"title",8,"确认"),p=t(l,"message",8,"是否确认此操作？"),m=t(l,"confirmText",8,"确认"),f=t(l,"cancelText",8,"取消"),b=t(l,"type",8,"warning"),q=t(l,"showCancelButton",8,!0);const $=s();function k(){$("confirm"),g(!1)}function C(){$("cancel"),g(!1)}function M(e){e.target===e.currentTarget&&C()}n();var A=w(),S=y(A),z=e=>{var t=at(),s=r(t),a=r(s),n=r(a),l=r(n,!0);i(n),i(a);var u=c(a,2),g=r(u),w=r(g,!0);i(g),i(u);var y=c(u,2),$=r(y),A=e=>{var t=st(),s=r(t,!0);i(t),x(()=>T(s,f())),v("click",t,C),d(e,t)};o($,e=>{q()&&e(A)});var S=c($,2),z=r(S,!0);i(S),i(y),i(s),i(t),x(()=>{I(a,1,`dialog-header ${b()??""}`,"svelte-f2wiz1"),T(l,h()),T(w,p()),I(S,1,`dialog-btn confirm-btn ${b()??""} ${(q()?"":"full-width")??""}`,"svelte-f2wiz1"),T(z,m())}),v("click",S,k),v("click",t,M),d(e,t)};o(S,e=>{g()&&e(z)}),d(a,A),u()}(c(Z,2),{get visible(){return p(Ie)},get title(){return p($e).title},get message(){return p($e).message},get confirmText(){return p($e).confirmText},get cancelText(){return p($e).cancelText},get type(){return p($e).type},get showCancelButton(){return p($e).showCancelButton},$$events:{confirm:()=>{h(Ie,!1),p($e).onConfirm()},cancel:()=>{h(Ie,!1),p($e).onCancel()}}}),i(A),x(()=>T(he,p(Te).title)),d(a,A)},$$slots:{default:!0}}),u(),me()}function Lt(s,a){e(a,!1);const l=m();let r=t(a,"data",8);S(()=>P(r()),()=>{h(l,r()?.type)}),z(),n(),Dt(s,{get type(){return p(l)}}),u()}export{Lt as component,me as universal};
