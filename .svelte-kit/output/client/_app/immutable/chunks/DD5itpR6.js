import{p as e,o as r,b as o,i as s,F as n,u as t,G as a,f as d,v as i,x as l,$ as v,g as c,m as u,s as w,t as f,h as m,e as h,r as p,j as x,z as b,H as g}from"./B1xmz3ZD.js";import{a as k}from"./BoaXofz5.js";var E=f('<div class="error-boundary p-6 bg-red-50 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-lg shadow-md"><div class="flex items-center mb-4"><div class="flex-shrink-0 text-red-600 dark:text-red-400"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg></div> <h3 class="ml-2 text-lg font-semibold text-red-800 dark:text-red-200">发生错误</h3></div> <div class="mb-4 text-red-700 dark:text-red-300"> </div> <button class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors">重试</button></div>');function j(f,j){e(j,!1);let L=u(null),y=u(!1);function D(e){w(y,!0),w(L,e.error||e.reason||new Error("发生未知错误")),k(c(L)),e.preventDefault(),console.error("全局错误:",c(L))}function z(e){w(y,!0),w(L,e.reason||new Error("发生未处理的Promise错误")),k(c(L)),e.preventDefault(),console.error("Promise错误:",c(L))}function P(){w(y,!1),w(L,null)}r(()=>{"undefined"!=typeof window&&(window.addEventListener("error",D),window.addEventListener("unhandledrejection",z))}),o(()=>{"undefined"!=typeof window&&(window.removeEventListener("error",D),window.removeEventListener("unhandledrejection",z))}),s();var B=n();t("error",v,function(e){w(y,!0),w(L,e.error||new Error("发生未知错误")),k(c(L)),e.preventDefault(),console.error("组件错误:",c(L))});var C=a(B),F=e=>{var r=E(),o=m(h(r),2),s=h(o,!0);p(o);var n=m(o,2);p(r),x(()=>b(s,c(L)?.message||"未知错误")),t("click",n,P),i(e,r)},G=e=>{var r=n(),o=a(r);g(o,j,"default",{}),i(e,r)};d(C,e=>{c(y)?e(F):e(G,!1)}),i(f,B),l()}export{j as E};
