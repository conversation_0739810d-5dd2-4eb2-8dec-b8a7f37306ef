function h(s){const l=function(n){n.length%4==0&&(n=n.replace(/==?$/,""));let t="",c=0,r=0;for(let o=0;o<n.length;o++)c<<=6,c|=A.indexOf(n[o]),r+=6,r===24&&(t+=String.fromCharCode((16711680&c)>>16),t+=String.fromCharCode((65280&c)>>8),t+=String.fromCharCode(255&c),c=r=0);return r===12?(c>>=4,t+=String.fromCharCode(c)):r===18&&(c>>=2,t+=String.fromCharCode((65280&c)>>8),t+=String.fromCharCode(255&c)),t}(s),g=new ArrayBuffer(l.length),e=new DataView(g);for(let n=0;n<g.byteLength;n++)e.setUint8(n,l.charCodeAt(n));return g}const A="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function w(s,l){if(typeof s=="number")return n(s,!0);if(!Array.isArray(s)||s.length===0)throw new Error("Invalid input");const g=s,e=Array(g.length);function n(t,c=!1){if(t===-1)return;if(t===-3)return NaN;if(t===-4)return 1/0;if(t===-5)return-1/0;if(t===-6)return-0;if(c)throw new Error("Invalid input");if(t in e)return e[t];const r=g[t];if(r&&typeof r=="object")if(Array.isArray(r))if(typeof r[0]=="string"){const o=r[0],i=l==null?void 0:l[o];if(i)return e[t]=i(n(r[1]));switch(o){case"Date":e[t]=new Date(r[1]);break;case"Set":const f=new Set;e[t]=f;for(let a=1;a<r.length;a+=1)f.add(n(r[a]));break;case"Map":const u=new Map;e[t]=u;for(let a=1;a<r.length;a+=2)u.set(n(r[a]),n(r[a+1]));break;case"RegExp":e[t]=new RegExp(r[1],r[2]);break;case"Object":e[t]=Object(r[1]);break;case"BigInt":e[t]=BigInt(r[1]);break;case"null":const y=Object.create(null);e[t]=y;for(let a=1;a<r.length;a+=2)y[r[a]]=n(r[a+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const a=new globalThis[o](h(r[1]));e[t]=a;break}case"ArrayBuffer":{const a=h(r[1]);e[t]=a;break}default:throw new Error(`Unknown type ${o}`)}}else{const o=new Array(r.length);e[t]=o;for(let i=0;i<r.length;i+=1){const f=r[i];f!==-2&&(o[i]=n(f))}}else{const o={};e[t]=o;for(const i in r){const f=r[i];o[i]=n(f)}}else e[t]=r;return e[t]}return n(0)}export{w as u};
