function r(r){const t=function(r){r.length%4==0&&(r=r.replace(/==?$/,""));let t="",n=0,a=0;for(let o=0;o<r.length;o++)n<<=6,n|=e.indexOf(r[o]),a+=6,24===a&&(t+=String.fromCharCode((16711680&n)>>16),t+=String.fromCharCode((65280&n)>>8),t+=String.fromCharCode(255&n),n=a=0);12===a?(n>>=4,t+=String.fromCharCode(n)):18===a&&(n>>=2,t+=String.fromCharCode((65280&n)>>8),t+=String.fromCharCode(255&n));return t}(r),n=new ArrayBuffer(t.length),a=new DataView(n);for(let e=0;e<n.byteLength;e++)a.setUint8(e,t.charCodeAt(e));return n}const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function t(e,t){if("number"==typeof e)return o(e,!0);if(!Array.isArray(e)||0===e.length)throw new Error("Invalid input");const n=e,a=Array(n.length);function o(e,c=!1){if(-1===e)return;if(-3===e)return NaN;if(-4===e)return 1/0;if(-5===e)return-1/0;if(-6===e)return-0;if(c)throw new Error("Invalid input");if(e in a)return a[e];const i=n[e];if(i&&"object"==typeof i)if(Array.isArray(i))if("string"==typeof i[0]){const n=i[0],c=t?.[n];if(c)return a[e]=c(o(i[1]));switch(n){case"Date":a[e]=new Date(i[1]);break;case"Set":const t=new Set;a[e]=t;for(let r=1;r<i.length;r+=1)t.add(o(i[r]));break;case"Map":const c=new Map;a[e]=c;for(let r=1;r<i.length;r+=2)c.set(o(i[r]),o(i[r+1]));break;case"RegExp":a[e]=new RegExp(i[1],i[2]);break;case"Object":a[e]=Object(i[1]);break;case"BigInt":a[e]=BigInt(i[1]);break;case"null":const s=Object.create(null);a[e]=s;for(let r=1;r<i.length;r+=2)s[i[r]]=o(i[r+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const t=new(0,globalThis[n])(r(i[1]));a[e]=t;break}case"ArrayBuffer":{const t=r(i[1]);a[e]=t;break}default:throw new Error(`Unknown type ${n}`)}}else{const r=new Array(i.length);a[e]=r;for(let e=0;e<i.length;e+=1){const t=i[e];-2!==t&&(r[e]=o(t))}}else{const r={};a[e]=r;for(const e in i){const t=i[e];r[e]=o(t)}}else a[e]=i;return a[e]}return o(0)}export{t as u};
