import{B as a,C as u,D as k}from"./CLjOhJ05.js";const w=a({id:null,name:null,avatar:null,isLoggedIn:!1}),d=a({theme:function(){if(typeof window!="undefined"){const e=localStorage.getItem("theme");if(e)return e;if(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches)return"dark"}return"light"}(),language:"zh-CN",notifications:!0,fontSize:"normal",reduceMotion:!1});function U(){const e=k(d);typeof window!="undefined"&&e.theme==="dark"&&document.documentElement.classList.add("dark")}const o=a([]);function c(e,n="info",t=3e3){const s=Date.now();return o.update(h=>[{id:s,message:e,type:n,timestamp:new Date},...h]),t&&setTimeout(()=>{m(s)},t),s}function m(e){o.update(n=>n.filter(t=>t.id!==e))}const y=a([]);function v(e){const n=Date.now(),t={id:n,message:e.message||"\u672A\u77E5\u9519\u8BEF",code:e.code||"UNKNOWN_ERROR",timestamp:new Date,details:e.details||null,stack:e.stack||null};return y.update(s=>[t,...s]),c(`\u9519\u8BEF: ${t.message}`,"error",5e3),n}u(w,e=>e.isLoggedIn),u(d,e=>e.theme==="dark");const D=a([]),_=a(""),r=a(null);function b(e){r.set(e)}const I=a([]),M=a(!1);u(r,e=>e?e.mcode:null);const l={adminUids:["********","1","9753360","921302","********","********","8454468","********","********","********"],defaultUid:null,uploadDomain:"https://gktempupload.qiaoxuesi.com",apiBaseUrl:"https://api.gankao.com/api-yunying",currentUser:"https://www.gankao.com/newaccount/currentUser",difyApiUrl:"https://aigate2.gankao.com/v1/chat-messages",apiProxyUrl:""},i=a({uid:null,username:null,nickname:null,avatar:null,role:"user"}),f=u(i,e=>!!e.uid&&l.adminUids.includes(e.uid));function p(e){i.update(n=>({...n,uid:e.uid||null,username:e.username||n.username,nickname:e.nickname||e.username||n.nickname,avatar:e.avatar||n.avatar,role:e.uid&&l.adminUids.includes(e.uid)?"admin":"user"}))}function g(){i.set({uid:null,username:null,nickname:null,avatar:null,role:"user"})}const N=Object.freeze(Object.defineProperty({__proto__:null,clearCurrentUser:g,isAdmin:f,setUserInfo:p,user:i},Symbol.toStringTag,{value:"Module"}));export{v as a,r as b,l as c,f as d,c as e,p as f,g,U as h,M as i,N as j,I as l,D as m,o as n,_ as p,m as r,b as s,i as u};
