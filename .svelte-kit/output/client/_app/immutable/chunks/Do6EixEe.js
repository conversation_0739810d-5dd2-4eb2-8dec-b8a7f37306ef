import{p as e,a as t,c as n,o,s as a,m as s,i,t as l,e as r,f as c,h,g as d,r as g,I as m,j as u,k as p,n as v,u as f,v as y,w as b,x as w,z as k,J as x,A as P}from"./B1xmz3ZD.js";import{A as C}from"./DuTLMzPI.js";import{p as S}from"./BDlA38tG.js";import{L as z}from"./8Cr1QNV0.js";const A={a:["æ","eɪ","ɑ","ə"],e:["e","i","ə"],i:["ɪ","aɪ","i"],o:["ɒ","oʊ","ɔ","ə"],u:["ʌ","u","ʊ","ju"],y:["aɪ","ɪ","j"],b:["b"],c:["k","s"],d:["d"],f:["f"],g:["g","dʒ"],h:["h"],j:["dʒ"],k:["k"],l:["l"],m:["m"],n:["n"],p:["p"],q:["kw"],r:["r"],s:["s","z"],t:["t"],v:["v"],w:["w"],x:["ks"],z:["z"]},R={hello:["h","ə","l","oʊ"],world:["w","ɜr","l","d"],apple:["æ","p","əl"],banana:["b","ə","n","æ","n","ə"],cat:["k","æ","t"],dog:["d","ɔ","g"],book:["b","ʊ","k"],water:["w","ɔ","t","ər"],house:["h","aʊ","s"],school:["s","k","u","l"],computer:["k","əm","p","ju","t","ər"],phone:["f","oʊ","n"],table:["t","eɪ","b","əl"],chair:["tʃ","ɛr"],window:["w","ɪ","n","d","oʊ"],door:["d","ɔr"],car:["k","ɑr"],tree:["t","r","i"],flower:["f","l","aʊ","ər"],sun:["s","ʌ","n"],moon:["m","u","n"],star:["s","t","ɑr"],good:["g","ʊ","d"],bad:["b","æ","d"],big:["b","ɪ","g"],small:["s","m","ɔ","l"],red:["r","ɛ","d"],blue:["b","l","u"],green:["g","r","i","n"],yellow:["j","ɛ","l","oʊ"],black:["b","l","æ","k"],white:["w","aɪ","t"]},j={b:"p",p:"pʰ",m:"m",f:"f",d:"t",t:"tʰ",n:"n",l:"l",g:"k",k:"kʰ",h:"x",j:"tɕ",q:"tɕʰ",x:"ɕ",zh:"ʈʂ",ch:"ʈʂʰ",sh:"ʂ",r:"ʐ",z:"ts",c:"tsʰ",s:"s",a:"a",o:"o",e:"ɤ",i:"i",u:"u","ü":"y",ai:"aɪ",ei:"eɪ",ao:"aʊ",ou:"oʊ",an:"an",en:"ən",ang:"aŋ",eng:"əŋ",er:"ər",ia:"ia",ie:"iɛ",iao:"iaʊ",iu:"ioʊ",ian:"ian",in:"in",iang:"iaŋ",ing:"iŋ",ua:"ua",uo:"uo",uai:"uaɪ",ui:"ueɪ",uan:"uan",un:"uən",uang:"uaŋ",ong:"uŋ","üe":"yɛ","üan":"yan","ün":"yn"},T={i:{type:"vowel",height:"high",backness:"front"},"ɪ":{type:"vowel",height:"high",backness:"front"},e:{type:"vowel",height:"mid",backness:"front"},"ɛ":{type:"vowel",height:"mid",backness:"front"},"æ":{type:"vowel",height:"low",backness:"front"},"ɑ":{type:"vowel",height:"low",backness:"back"},"ɔ":{type:"vowel",height:"mid",backness:"back"},o:{type:"vowel",height:"mid",backness:"back"},u:{type:"vowel",height:"high",backness:"back"},"ʊ":{type:"vowel",height:"high",backness:"back"},"ʌ":{type:"vowel",height:"mid",backness:"central"},"ə":{type:"vowel",height:"mid",backness:"central"},p:{type:"consonant",manner:"stop",place:"bilabial",voiced:!1},b:{type:"consonant",manner:"stop",place:"bilabial",voiced:!0},t:{type:"consonant",manner:"stop",place:"alveolar",voiced:!1},d:{type:"consonant",manner:"stop",place:"alveolar",voiced:!0},k:{type:"consonant",manner:"stop",place:"velar",voiced:!1},g:{type:"consonant",manner:"stop",place:"velar",voiced:!0},f:{type:"consonant",manner:"fricative",place:"labiodental",voiced:!1},v:{type:"consonant",manner:"fricative",place:"labiodental",voiced:!0},s:{type:"consonant",manner:"fricative",place:"alveolar",voiced:!1},z:{type:"consonant",manner:"fricative",place:"alveolar",voiced:!0},m:{type:"consonant",manner:"nasal",place:"bilabial",voiced:!0},n:{type:"consonant",manner:"nasal",place:"alveolar",voiced:!0},l:{type:"consonant",manner:"liquid",place:"alveolar",voiced:!0},r:{type:"consonant",manner:"liquid",place:"alveolar",voiced:!0},w:{type:"consonant",manner:"glide",place:"bilabial",voiced:!0},j:{type:"consonant",manner:"glide",place:"palatal",voiced:!0},h:{type:"consonant",manner:"fricative",place:"glottal",voiced:!1}};class M{constructor(){this.cache=new Map}detectLanguage(e){const t=/[\u4e00-\u9fff]/.test(e),n=/[a-zA-Z]/.test(e);return t&&n?"mixed":t?"zh":"en"}smartPhonemeConversion(e){console.log("[PhonemeConverter] 智能音素转换输入:",e);const t=this.detectLanguage(e);if(console.log("[PhonemeConverter] 检测到的语言:",t),"zh"===t){const t=this.chineseToPhonemes(e);return console.log("[PhonemeConverter] 中文音素转换结果:",t),t}if("en"===t){const t=this.englishToPhonemes(e);return console.log("[PhonemeConverter] 英文音素转换结果:",t),t}{const t=[],n=/[\u4e00-\u9fff]+/g,o=/[a-zA-Z]+/g;let a;for(;null!==(a=n.exec(e));){console.log("[PhonemeConverter] 处理中文部分:",a[0]);const e=this.chineseToPhonemes(a[0]);console.log("[PhonemeConverter] 中文音素:",e),t.push(...e)}for(n.lastIndex=0;null!==(a=o.exec(e));){console.log("[PhonemeConverter] 处理英文部分:",a[0]);const e=this.englishToPhonemes(a[0]);console.log("[PhonemeConverter] 英文音素:",e),t.push(...e)}return console.log("[PhonemeConverter] 混合语言音素转换结果:",t),t}}enhancedMatch(e,t,n="en",o=.6){if(console.log("[PhonemeConverter] 增强匹配输入:",e,t),this.directTextMatch(e,t))return{matched:!0,confidence:.95,method:"direct",details:{asrResult:e,targetWord:t}};const a=this.smartPhonemeConversion(e),s="zh"===n?this.chineseToPhonemes(t):this.englishToPhonemes(t),i=this.calculatePhonemeSimilarity(a,s),l=this.calculateContainsSimilarity(a,s),r=this.calculateFuzzyContainsSimilarity(a,s),c=Math.max(i,l,r);let h="exact";return c===l&&(h="contains"),c===r&&(h="fuzzy_contains"),{matched:c>=o,confidence:c,method:"phoneme",details:{asrResult:e,targetWord:t,asrPhonemes:a,targetPhonemes:s,similarity:c,exactSimilarity:i,containsSimilarity:l,fuzzyContainsSimilarity:r,matchMethod:h}}}directTextMatch(e,t){const n=e=>e.toLowerCase().trim().replace(/[^\w\u4e00-\u9fff]/g,"");return n(e)===n(t)}calculatePhonemeSimilarity(e,t){if(!e.length&&!t.length)return 1;if(!e.length||!t.length)return 0;const n=Array(e.length+1).fill().map(()=>Array(t.length+1).fill(0));for(let a=0;a<=e.length;a++)n[a][0]=a;for(let a=0;a<=t.length;a++)n[0][a]=a;for(let a=1;a<=e.length;a++)for(let o=1;o<=t.length;o++){const s=this.phonemeSimilarityCost(e[a-1],t[o-1]);n[a][o]=Math.min(n[a-1][o]+1,n[a][o-1]+1,n[a-1][o-1]+s)}const o=Math.max(e.length,t.length);return 1-n[e.length][t.length]/o}calculateContainsSimilarity(e,t){if(!e.length||!t.length)return 0;for(let n=0;n<=e.length-t.length;n++){let o=0;for(let a=0;a<t.length;a++)e[n+a]===t[a]&&o++;if(o===t.length)return Math.min(.95,t.length/e.length+.5)}return 0}calculateFuzzyContainsSimilarity(e,t){if(!e.length||!t.length)return 0;let n=0;for(let o=0;o<=e.length-t.length;o++){let a=0,s=0;for(let n=0;n<t.length;n++){const i=e[o+n],l=t[n];if(i===l)a++,s++;else{const e=this.phonemeSimilarityCost(i,l);e<.5&&(s+=1-e)}}const i=s/t.length;if(a>=.6*t.length){const o=Math.min(.2,t.length/e.length);n=Math.max(n,i+o)}else n=Math.max(n,i)}return Math.min(.9,n)}phonemeSimilarityCost(e,t){if(e===t)return 0;const n=T[e],o=T[t];if(!n||!o){return new z(e,t).distance/Math.max(e.length,t.length)}return 1-this.calculateFeatureSimilarity(n,o)}calculateFeatureSimilarity(e,t){let n=0,o=0;return e.type===t.type&&(n+=.4),o+=.4,"vowel"===e.type&&"vowel"===t.type?(e.height===t.height&&(n+=.3),e.backness===t.backness&&(n+=.3),o+=.6):"consonant"===e.type&&"consonant"===t.type&&(e.manner===t.manner&&(n+=.2),e.place===t.place&&(n+=.2),e.voiced===t.voiced&&(n+=.2),o+=.6),o>0?n/o:0}englishToPhonemes(e){const t=e.toLowerCase().trim();if(this.cache.has(t))return this.cache.get(t);let n=[];return n=R[t]?[...R[t]]:this.fallbackEnglishPhonemes(t),this.cache.set(t,n),n}chineseToPhonemes(e){const t=`zh_${e}`;if(this.cache.has(t))return this.cache.get(t);try{const n=S(e,{style:S.STYLE_TONE2,heteronym:!1}),o=[];for(const e of n)if(e&&e[0]){const t=this.pinyinToPhonemes(e[0]);o.push(...t)}return this.cache.set(t,o),o}catch(n){return console.error("中文转音素失败:",n),[]}}pinyinToPhonemes(e){const t=e.replace(/[0-9]/g,""),{initial:n,final:o}=this.separateInitialFinal(t),a=[];if(n&&j[n]&&a.push(j[n]),o&&j[o])a.push(j[o]);else if(o){const e=this.decomposeFinal(o);for(const t of e)j[t]&&a.push(j[t])}return a}fallbackEnglishPhonemes(e){const t=[];for(let n=0;n<e.length;n++){const o=e[n];A[o]?t.push(A[o][0]):/[a-z]/.test(o)&&t.push(o)}return t}separateInitialFinal(e){const t=["zh","ch","sh","b","p","m","f","d","t","n","l","g","k","h","j","q","x","r","z","c","s","y","w"];for(const n of t)if(e.startsWith(n))return{initial:n,final:e.substring(n.length)};return{initial:"",final:e}}decomposeFinal(e){const t={iao:["i","ao"],ian:["i","an"],iang:["i","ang"],uai:["u","ai"],uan:["u","an"],uang:["u","ang"]};return t[e]?t[e]:[e]}}var E=l('<div class="text-xs text-gray-500 mt-1 svelte-1g95opm"> </div>'),$=l('<div class="target-word-display mb-4 p-3 bg-blue-50 rounded-lg svelte-1g95opm"><div class="text-sm text-gray-600 mb-1 svelte-1g95opm">目标单词:</div> <div class="text-lg font-semibold text-blue-800 svelte-1g95opm"> </div> <!></div>'),I=x('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-1g95opm"><rect x="6" y="6" width="12" height="12" rx="2" fill="currentColor" class="svelte-1g95opm"></rect></svg> 停止录音',1),F=x('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-1g95opm"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2" class="svelte-1g95opm"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1g95opm"></path><path d="M12 18V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1g95opm"></path><path d="M8 22H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1g95opm"></path></svg> 开始录音',1),L=l('<div class="recording-status svelte-1g95opm"><div class="recording-indicator svelte-1g95opm"></div> <span class="svelte-1g95opm">正在录音...</span></div>'),q=l('<div class="svelte-1g95opm"> </div>'),V=l('<div class="svelte-1g95opm"> </div>'),W=l('<div class="analysis-details text-xs text-gray-600 mt-2 p-2 bg-gray-100 rounded svelte-1g95opm"><div class="mb-1 svelte-1g95opm"> </div> <!> <!></div>'),Z=l('<div><div class="mb-2 svelte-1g95opm"><div class="text-sm text-gray-600 svelte-1g95opm">识别结果:</div> <div class="text-lg font-medium svelte-1g95opm"> </div></div> <div class="mb-2 svelte-1g95opm"><div> </div></div> <!></div>'),_=l('<div class="enhanced-asr-container svelte-1g95opm"><!> <div class="recording-controls mb-4 svelte-1g95opm"><button><!></button> <!></div> <div class="hidden-asr svelte-1g95opm" style="display: none;"><!></div> <!></div>');function B(l,x){e(x,!1);let S=t(x,"targetWord",8,""),z=t(x,"language",8,"en"),A=t(x,"duration",8,30),R=t(x,"direction",8,"top");t(x,"showTextPanel",8,!0),t(x,"phonemeThreshold",8,.6),t(x,"directScoreThreshold",8,60),t(x,"phonemeScoreThreshold",8,40);let j=s(),T=s(),B=s(!1),H=s(""),D=s(null),J=s(null);const N=n();async function O(){d(j)&&(a(D,null),a(J,null),a(H,""),await d(j).startRecording())}async function Y(){d(j)&&await d(j).stopRecording()}async function G(){if(console.log("[EnhancedASR] preInitialize被调用"),console.log("[EnhancedASR] asrComponent引用:",d(j)),!d(j))return console.error("[EnhancedASR] asrComponent引用为空"),!1;console.log("[EnhancedASR] 调用底层ASR的preInitialize");try{const e=await d(j).preInitialize();return console.log("[EnhancedASR] 底层ASR preInitialize结果:",e),e}catch(e){return console.error("[EnhancedASR] 底层ASR preInitialize失败:",e),!1}}function K(){return d(D)?d(D).matched?`✓ 匹配成功 (${(100*d(D).confidence).toFixed(1)}%)`:`✗ 匹配失败 (${(100*d(D).confidence).toFixed(1)}%)`:""}async function Q(){return d(j)&&d(j).getAudioStream?await d(j).getAudioStream():null}function U(){return d(D)?d(D).matched?"text-green-600":"text-red-600":""}function X(){if(!d(J))return"";const{method:e,asrPhonemes:t,targetPhonemes:n,similarity:o}=d(J);return"direct"===e?"直接文本匹配":"phoneme"===e?`音素匹配: [${t?.join(", ")||""}] vs [${n?.join(", ")||""}] (${(100*o).toFixed(1)}%)`:""}o(()=>{console.log("[EnhancedASR] 组件开始挂载");try{a(T,new M),console.log("[EnhancedASR] 音素转换器初始化成功")}catch(e){console.error("[EnhancedASR] 音素转换器初始化失败:",e)}console.log("[EnhancedASR] 组件挂载完成")}),i();var ee=_(),te=r(ee),ne=e=>{var t=$(),n=h(r(t),2),o=r(n,!0);g(n);var a=h(n,2),s=e=>{var t=E(),n=r(t);g(t),u(e=>k(n,`音素: [${e??""}]`),[()=>d(T)?d(T).englishToPhonemes(S()).join(", "):""],p),y(e,t)};c(a,e=>{"en"===z()&&e(s)}),g(t),u(()=>k(o,S())),y(e,t)};c(te,e=>{S()&&e(ne)});var oe=h(te,2),ae=r(oe);let se;var ie=r(ae),le=e=>{var t=I();P(),y(e,t)},re=e=>{var t=F();P(),y(e,t)};c(ie,e=>{d(B)?e(le):e(re,!1)}),g(ae);var ce=h(ae,2),he=e=>{var t=L();y(e,t)};c(ce,e=>{d(B)&&e(he)}),g(oe);var de=h(oe,2),ge=r(de);m(C(ge,{get duration(){return A()},get direction(){return R()},showTextPanel:!1,$$events:{start:function(){a(B,!0),N("start")},stop:function(e){a(B,!1);const{content:t,status:n}=e.detail;N("stop",{content:t,status:n})},textInput:function(e){const{content:t,status:n}=e.detail;a(H,t),console.log("[EnhancedASR] 收到textInput事件:",{content:t,status:n}),N("textInput",{content:t,status:n,matchResult:null,analysisDetails:null})},textCompleted:async function(e){const{content:t,status:n}=e.detail;N("textCompleted",{content:t,status:n})},error:function(e){a(B,!1),N("error",e.detail)},close:function(e){a(B,!1),N("close",e.detail)}},$$legacy:!0}),e=>a(j,e),()=>d(j)),g(de);var me=h(de,2),ue=e=>{var t=Z();let n;var o=r(t),a=h(r(o),2),s=r(a,!0);g(a),g(o);var i=h(o,2),l=r(i),m=r(l,!0);g(l),g(i);var f=h(i,2),b=e=>{var t=W(),n=r(t),o=r(n);g(n);var a=h(n,2),s=e=>{var t=q(),n=r(t);g(t),u(e=>k(n,`识别音素: [${e??""}]`),[()=>d(J).asrPhonemes.join(", ")],p),y(e,t)};c(a,e=>{d(J).asrPhonemes&&e(s)});var i=h(a,2),l=e=>{var t=V(),n=r(t);g(t),u(e=>k(n,`目标音素: [${e??""}]`),[()=>d(J).targetPhonemes.join(", ")],p),y(e,t)};c(i,e=>{d(J).targetPhonemes&&e(l)}),g(t),u(e=>k(o,`分析方法: ${e??""}`),[X],p),y(e,t)};c(f,e=>{d(J)&&"phoneme"===d(J).method&&e(b)}),g(t),u((e,o,a)=>{n=v(t,1,"match-result-display mt-4 p-3 rounded-lg border-2 svelte-1g95opm",null,n,e),k(s,d(H)),v(l,1,`text-sm font-semibold ${o??""}`,"svelte-1g95opm"),k(m,a)},[()=>({"border-green-300":d(D).matched,"border-red-300":!d(D).matched,"bg-green-50":d(D).matched,"bg-red-50":!d(D).matched}),U,K],p),y(e,t)};return c(me,e=>{d(D)&&d(H)&&e(ue)}),g(ee),u(e=>{se=v(ae,1,"record-button svelte-1g95opm",null,se,e),ae.disabled=!d(j)},[()=>({recording:d(B)})],p),f("click",ae,function(...e){(d(B)?Y:O)?.apply(this,e)}),y(l,ee),b(x,"startRecording",O),b(x,"stopRecording",Y),b(x,"preInitialize",G),b(x,"getAudioStream",Q),w({startRecording:O,stopRecording:Y,preInitialize:G,getAudioStream:Q})}export{B as E,M as P};
