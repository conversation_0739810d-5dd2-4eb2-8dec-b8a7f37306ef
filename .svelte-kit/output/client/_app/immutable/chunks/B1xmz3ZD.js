import{D as t}from"./roc3oT9G.js";import{u as e}from"./t5uZ5HS4.js";var n=Array.isArray,r=Array.prototype.indexOf,a=Array.from,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyDescriptors,l=Object.prototype,u=Array.prototype,c=Object.getPrototypeOf,f=Object.isExtensible;const d=()=>{};function p(t){return t()}function v(t){for(var e=0;e<t.length;e++)t[e]()}const h=32,m=64,g=128,y=256,w=512,b=1024,_=2048,x=4096,k=8192,S=16384,E=65536,$=1<<19,R=1<<20,A=1<<21,P=Symbol("$state"),L=Symbol("legacy props"),O=Symbol("");function j(t){return t===this.v}function C(t,e){return t!=t?e==e:t!==e||null!==t&&"object"==typeof t||"function"==typeof t}function T(t){return!C(t,this.v)}let U=!1;const N="[",I="[!",q={},D=Symbol(),M="http://www.w3.org/1999/xhtml";function V(t){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}let W=null;function F(t){W=t}function B(t,e=!1,n){var r=W={p:W,c:null,d:!1,e:null,m:!1,s:t,x:null,l:null};U&&!e&&(W.l={s:null,u:null,r1:[],r2:X(!1)}),Ct(()=>{r.d=!0})}function z(t){const e=W;if(null!==e){void 0!==t&&(e.x=t);const s=e.e;if(null!==s){var n=he,r=de;e.e=null;try{for(var a=0;a<s.length;a++){var o=s[a];me(o.effect),ve(o.reaction),Nt(o.fn)}}finally{me(n),ve(r)}}W=e.p,e.m=!0}return t||{}}function J(){return!U||null!==W&&null===W.l}function K(t){if("object"!=typeof t||null===t||P in t)return t;const e=c(t);if(e!==l&&e!==u)return t;var r=new Map,a=n(t),o=Y(0),i=de,f=t=>{var e=de;ve(i);var n=t();return ve(e),n};return a&&r.set("length",Y(t.length)),new Proxy(t,{defineProperty(t,e,n){"value"in n&&!1!==n.configurable&&!1!==n.enumerable&&!1!==n.writable||function(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}();var a=r.get(e);return void 0===a?(a=f(()=>Y(n.value)),r.set(e,a)):tt(a,f(()=>K(n.value))),!0},deleteProperty(t,e){var n=r.get(e);if(void 0===n)e in t&&r.set(e,f(()=>Y(D)));else{if(a&&"string"==typeof e){var s=r.get("length"),i=Number(e);Number.isInteger(i)&&i<s.v&&tt(s,i)}tt(n,D),G(o)}return!0},get(e,n,a){if(n===P)return t;var o=r.get(n),i=n in e;if(void 0!==o||i&&!s(e,n)?.writable||(o=f(()=>Y(K(i?e[n]:D))),r.set(n,o)),void 0!==o){var l=Me(o);return l===D?void 0:l}return Reflect.get(e,n,a)},getOwnPropertyDescriptor(t,e){var n=Reflect.getOwnPropertyDescriptor(t,e);if(n&&"value"in n){var a=r.get(e);a&&(n.value=Me(a))}else if(void 0===n){var o=r.get(e),s=o?.v;if(void 0!==o&&s!==D)return{enumerable:!0,configurable:!0,value:s,writable:!0}}return n},has(t,e){if(e===P)return!0;var n=r.get(e),a=void 0!==n&&n.v!==D||Reflect.has(t,e);if((void 0!==n||null!==he&&(!a||s(t,e)?.writable))&&(void 0===n&&(n=f(()=>Y(a?K(t[e]):D)),r.set(e,n)),Me(n)===D))return!1;return a},set(t,e,n,i){var l=r.get(e),u=e in t;if(a&&"length"===e)for(var c=n;c<l.v;c+=1){var d=r.get(c+"");void 0!==d?tt(d,D):c in t&&(d=f(()=>Y(D)),r.set(c+"",d))}void 0===l?u&&!s(t,e)?.writable||(tt(l=f(()=>Y(void 0)),f(()=>K(n))),r.set(e,l)):(u=l.v!==D,tt(l,f(()=>K(n))));var p=Reflect.getOwnPropertyDescriptor(t,e);if(p?.set&&p.set.call(i,n),!u){if(a&&"string"==typeof e){var v=r.get("length"),h=Number(e);Number.isInteger(h)&&h>=v.v&&tt(v,h+1)}G(o)}return!0},ownKeys(t){Me(o);var e=Reflect.ownKeys(t).filter(t=>{var e=r.get(t);return void 0===e||e.v!==D});for(var[n,a]of r)a.v===D||n in t||e.push(n);return e},setPrototypeOf(){!function(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}()}})}function G(t,e=1){tt(t,t.v+e)}const H=new Map;function X(t,e){return{f:0,v:t,reactions:null,equals:j,rv:0,wv:0}}function Y(t,e){const n=X(t);return ye(n),n}function Q(t,e=!1){const n=X(t);return e||(n.equals=T),U&&null!==W&&null!==W.l&&(W.l.s??=[]).push(n),n}function Z(t,e){return tt(t,Ve(()=>Me(t))),e}function tt(t,e,n=!1){return null!==de&&!pe&&J()&&18&de.f&&!ge?.includes(t)&&function(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}(),et(t,n?K(e):e)}function et(t,e){if(!t.equals(e)){var n=t.v;ue?H.set(t,e):H.set(t,n),t.v=e,t.wv=Ee(),rt(t,_),!J()||null===he||0===(he.f&b)||96&he.f||(null===_e?function(t){_e=t}([t]):_e.push(t))}return e}function nt(t,e=1){var n=Me(t),r=1===e?n++:n--;return tt(t,n),r}function rt(t,e){var n=t.reactions;if(null!==n)for(var r=J(),a=n.length,o=0;o<a;o++){var s=n[o],i=s.f;0===(i&_)&&((r||s!==he)&&(Fe(s,e),1280&i&&(2&i?rt(s,x):Ne(s))))}}function at(t){console.warn("https://svelte.dev/e/hydration_mismatch")}let ot,st=!1;function it(t){st=t}function lt(t){if(null===t)throw at(),q;return ot=t}function ut(){return lt(_t(ot))}function ct(t){if(st){if(null!==_t(ot))throw at(),q;ot=t}}function ft(t=1){if(st){for(var e=t,n=ot;e--;)n=_t(n);ot=n}}function dt(){for(var t=0,e=ot;;){if(8===e.nodeType){var n=e.data;if("]"===n){if(0===t)return e;t-=1}else n!==N&&n!==I||(t+=1)}var r=_t(e);e.remove(),e=r}}var pt,vt,ht,mt,gt;function yt(){if(void 0===pt){pt=window,vt=document,ht=/Firefox/.test(navigator.userAgent);var t=Element.prototype,e=Node.prototype,n=Text.prototype;mt=s(e,"firstChild").get,gt=s(e,"nextSibling").get,f(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),f(n)&&(n.__t=void 0)}}function wt(t=""){return document.createTextNode(t)}function bt(t){return mt.call(t)}function _t(t){return gt.call(t)}function xt(t,e){if(!st)return bt(t);var n=bt(ot);if(null===n)n=ot.appendChild(wt());else if(e&&3!==n.nodeType){var r=wt();return n?.before(r),lt(r),r}return lt(n),n}function kt(t,e){if(!st){var n=bt(t);return n instanceof Comment&&""===n.data?_t(n):n}return ot}function St(t,e=1,n=!1){let r=st?ot:t;for(var a;e--;)a=r,r=_t(r);if(!st)return r;var o=r?.nodeType;if(n&&3!==o){var s=wt();return null===r?a?.after(s):r.before(s),lt(s),s}return lt(r),r}function Et(t){t.textContent=""}function $t(t){var e=2050,n=null!==de&&2&de.f?de:null;null===he||null!==n&&0!==(n.f&y)?e|=y:he.f|=R;return{ctx:W,deps:null,effects:null,equals:j,f:e,fn:t,reactions:null,rv:0,v:null,wv:0,parent:n??he}}function Rt(t){const e=$t(t);return ye(e),e}function At(t){const e=$t(t);return e.equals=T,e}function Pt(t){var e=t.effects;if(null!==e){t.effects=null;for(var n=0;n<e.length;n+=1)zt(e[n])}}function Lt(t){var e=function(t){var e,n=he;me(function(t){for(var e=t.parent;null!==e;){if(!(2&e.f))return e;e=e.parent}return null}(t));try{Pt(t),e=Pe(t)}finally{me(n)}return e}(t);Fe(t,!Se&&0===(t.f&y)||null===t.deps?b:x),t.equals(e)||(t.v=e,t.wv=Ee())}function Ot(t){null===he&&null===de&&function(){throw new Error("https://svelte.dev/e/effect_orphan")}(),null!==de&&0!==(de.f&y)&&null===he&&function(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}(),ue&&function(){throw new Error("https://svelte.dev/e/effect_in_teardown")}()}function jt(t,e,n,r=!0){var a=he,o={ctx:W,deps:null,nodes_start:null,nodes_end:null,f:t|_,first:null,fn:e,last:null,next:null,parent:a,prev:null,teardown:null,transitions:null,wv:0};if(n)try{je(o),o.f|=32768}catch(i){throw zt(o),i}else null!==e&&Ne(o);if(!(n&&null===o.deps&&null===o.first&&null===o.nodes_start&&null===o.teardown&&!(1048704&o.f))&&r&&(null!==a&&function(t,e){var n=e.last;null===n?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}(o,a),null!==de&&2&de.f)){var s=de;(s.effects??=[]).push(o)}return o}function Ct(t){const e=jt(8,null,!1);return Fe(e,b),e.teardown=t,e}function Tt(t){if(Ot(),!(null!==he&&0!==(he.f&h)&&null!==W&&!W.m))return Nt(t);var e=W;(e.e??=[]).push({fn:t,effect:he,reaction:de})}function Ut(t){return Ot(),Dt(t)}function Nt(t){return jt(4,t,!1)}function It(t,e){var n=W,r={effect:null,ran:!1};n.l.r1.push(r),r.effect=Dt(()=>{t(),r.ran||(r.ran=!0,tt(n.l.r2,!0),Ve(e))})}function qt(){var t=W;Dt(()=>{if(Me(t.l.r2)){for(var e of t.l.r1){var n=e.effect;0!==(n.f&b)&&Fe(n,x),$e(n)&&je(n),e.ran=!1}t.l.r2.v=!1}})}function Dt(t){return jt(8,t,!0)}function Mt(t,e=[],n=$t){const r=e.map(n);return Vt(()=>t(...r.map(Me)))}function Vt(t,e=0){return jt(24|e,t,!0)}function Wt(t,e=!0){return jt(40,t,!0,e)}function Ft(t){var e=t.teardown;if(null!==e){const t=ue,n=de;ce(!0),ve(null);try{e.call(null)}finally{ce(t),ve(n)}}}function Bt(t,e=!1){var n=t.first;for(t.first=t.last=null;null!==n;){var r=n.next;0!==(n.f&m)?n.parent=null:zt(n,e),n=r}}function zt(t,e=!0){var n=!1;if((e||0!==(t.f&$))&&null!==t.nodes_start){for(var r=t.nodes_start,a=t.nodes_end;null!==r;){var o=r===a?null:_t(r);r.remove(),r=o}n=!0}Bt(t,e&&!n),Oe(t,0),Fe(t,S);var s=t.transitions;if(null!==s)for(const l of s)l.stop();Ft(t);var i=t.parent;null!==i&&null!==i.first&&Jt(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=null}function Jt(t){var e=t.parent,n=t.prev,r=t.next;null!==n&&(n.next=r),null!==r&&(r.prev=n),null!==e&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function Kt(t,e){var n=[];Ht(t,n,!0),Gt(n,()=>{zt(t),e&&e()})}function Gt(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var a of t)a.out(r)}else e()}function Ht(t,e,n){if(0===(t.f&k)){if(t.f^=k,null!==t.transitions)for(const r of t.transitions)(r.is_global||n)&&e.push(r);for(var r=t.first;null!==r;){var a=r.next;Ht(r,e,!!(0!==(r.f&E)||0!==(r.f&h))&&n),r=a}}}function Xt(t){Yt(t,!0)}function Yt(t,e){if(0!==(t.f&k)){t.f^=k,0===(t.f&b)&&(t.f^=b),$e(t)&&(Fe(t,_),Ne(t));for(var n=t.first;null!==n;){var r=n.next;Yt(n,!!(0!==(n.f&E)||0!==(n.f&h))&&e),n=r}if(null!==t.transitions)for(const n of t.transitions)(n.is_global||e)&&n.in()}}const Qt="undefined"==typeof requestIdleCallback?t=>setTimeout(t,1):requestIdleCallback;let Zt=[],te=[];function ee(){var t=Zt;Zt=[],v(t)}function ne(){var t=te;te=[],v(t)}function re(t){0===Zt.length&&queueMicrotask(ee),Zt.push(t)}function ae(){Zt.length>0&&ee(),te.length>0&&ne()}let oe=!1,se=!1,ie=null,le=!1,ue=!1;function ce(t){ue=t}let fe=[],de=null,pe=!1;function ve(t){de=t}let he=null;function me(t){he=t}let ge=null;function ye(t){null!==de&&de.f&A&&(null===ge?ge=[t]:ge.push(t))}let we=null,be=0,_e=null;let xe=1,ke=0,Se=!1;function Ee(){return++xe}function $e(t){var e=t.f;if(0!==(e&_))return!0;if(0!==(e&x)){var n=t.deps,r=0!==(e&y);if(null!==n){var a,o,s=0!==(e&w),i=r&&null!==he&&!Se,l=n.length;if(s||i){var u=t,c=u.parent;for(a=0;a<l;a++)o=n[a],!s&&o?.reactions?.includes(u)||(o.reactions??=[]).push(u);s&&(u.f^=w),i&&null!==c&&0===(c.f&y)&&(u.f^=y)}for(a=0;a<l;a++)if($e(o=n[a])&&Lt(o),o.wv>t.wv)return!0}r&&(null===he||Se)||Fe(t,b)}return!1}function Re(t,e,n,r){if(oe){if(null===n&&(oe=!1),function(t){return 0===(t.f&S)&&(null===t.parent||0===(t.parent.f&g))}(e))throw t}else null!==n&&(oe=!0),function(t,e){for(var n=e;null!==n;){if(0!==(n.f&g))try{return void n.fn(t)}catch{n.f^=g}n=n.parent}throw oe=!1,t}(t,e)}function Ae(t,e,n=!0){var r=t.reactions;if(null!==r)for(var a=0;a<r.length;a++){var o=r[a];ge?.includes(t)||(2&o.f?Ae(o,e,!1):e===o&&(n?Fe(o,_):0!==(o.f&b)&&Fe(o,x),Ne(o)))}}function Pe(t){var e=we,n=be,r=_e,a=de,o=Se,s=ge,i=W,l=pe,u=t.f;we=null,be=0,_e=null,Se=0!==(u&y)&&(pe||!le||null===de),de=96&u?null:t,ge=null,F(t.ctx),pe=!1,ke++,t.f|=A;try{var c=(0,t.fn)(),f=t.deps;if(null!==we){var d;if(Oe(t,be),null!==f&&be>0)for(f.length=be+we.length,d=0;d<we.length;d++)f[be+d]=we[d];else t.deps=f=we;if(!Se)for(d=be;d<f.length;d++)(f[d].reactions??=[]).push(t)}else null!==f&&be<f.length&&(Oe(t,be),f.length=be);if(J()&&null!==_e&&!pe&&null!==f&&!(6146&t.f))for(d=0;d<_e.length;d++)Ae(_e[d],t);return a!==t&&(ke++,null!==_e&&(null===r?r=_e:r.push(..._e))),c}finally{we=e,be=n,_e=r,de=a,Se=o,ge=s,F(i),pe=l,t.f^=A}}function Le(t,e){let n=e.reactions;if(null!==n){var a=r.call(n,t);if(-1!==a){var o=n.length-1;0===o?n=e.reactions=null:(n[a]=n[o],n.pop())}}null===n&&2&e.f&&(null===we||!we.includes(e))&&(Fe(e,x),768&e.f||(e.f^=w),Pt(e),Oe(e,0))}function Oe(t,e){var n=t.deps;if(null!==n)for(var r=e;r<n.length;r++)Le(t,n[r])}function je(t){var e=t.f;if(0===(e&S)){Fe(t,b);var n=he,r=W,a=le;he=t,le=!0;try{16&e?function(t){for(var e=t.first;null!==e;){var n=e.next;0===(e.f&h)&&zt(e),e=n}}(t):Bt(t),Ft(t);var o=Pe(t);t.teardown="function"==typeof o?o:null,t.wv=xe;t.deps;0}catch(s){Re(s,t,n,r||t.ctx)}finally{le=a,he=n}}}function Ce(){try{!function(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}()}catch(t){if(null===ie)throw t;Re(t,ie,null)}}function Te(){var t=le;try{var e=0;for(le=!0;fe.length>0;){e++>1e3&&Ce();var n=fe,r=n.length;fe=[];for(var a=0;a<r;a++){Ue(Ie(n[a]))}H.clear()}}finally{se=!1,le=t,ie=null}}function Ue(t){var e=t.length;if(0!==e)for(var n=0;n<e;n++){var r=t[n];if(!(24576&r.f))try{$e(r)&&(je(r),null===r.deps&&null===r.first&&null===r.nodes_start&&(null===r.teardown?Jt(r):r.fn=null))}catch(a){Re(a,r,null,r.ctx)}}}function Ne(t){se||(se=!0,queueMicrotask(Te));for(var e=ie=t;null!==e.parent;){var n=(e=e.parent).f;if(96&n){if(0===(n&b))return;e.f^=b}}fe.push(e)}function Ie(t){for(var e=[],n=t;null!==n;){var r=n.f,a=!!(96&r);if(!(a&&0!==(r&b))&&0===(r&k)){if(4&r)e.push(n);else if(a)n.f^=b;else{var o=de;try{de=n,$e(n)&&je(n)}catch(l){Re(l,n,null,n.ctx)}finally{de=o}}var s=n.first;if(null!==s){n=s;continue}}var i=n.parent;for(n=n.next;null===n&&null!==i;)n=i.next,i=i.parent}return e}function qe(t){for(ae();fe.length>0;)se=!0,Te(),ae()}async function De(){await Promise.resolve(),qe()}function Me(t){var e=!!(2&t.f);if(null===de||pe){if(e&&null===t.deps&&null===t.effects){var n=t,r=n.parent;null!==r&&0===(r.f&y)&&(n.f^=y)}}else if(!ge?.includes(t)){var a=de.deps;t.rv<ke&&(t.rv=ke,null===we&&null!==a&&a[be]===t?be++:null===we?we=[t]:Se&&we.includes(t)||we.push(t))}return e&&$e(n=t)&&Lt(n),ue&&H.has(t)?H.get(t):t.v}function Ve(t){var e=pe;try{return pe=!0,t()}finally{pe=e}}const We=-7169;function Fe(t,e){t.f=t.f&We|e}function Be(t){if("object"==typeof t&&t&&!(t instanceof EventTarget))if(P in t)ze(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];"object"==typeof n&&n&&P in n&&ze(n)}}function ze(t,e=new Set){if(!("object"!=typeof t||null===t||t instanceof EventTarget||e.has(t))){e.add(t),t instanceof Date&&t.getTime();for(let a in t)try{ze(t[a],e)}catch(n){}const r=c(t);if(r!==Object.prototype&&r!==Array.prototype&&r!==Map.prototype&&r!==Set.prototype&&r!==Date.prototype){const e=i(r);for(let r in e){const a=e[r].get;if(a)try{a.call(t)}catch(n){}}}}}const Je=["touchstart","touchmove"];function Ke(t){return Je.includes(t)}function Ge(t){st&&null!==bt(t)&&Et(t)}let He=!1;function Xe(){He||(He=!0,document.addEventListener("reset",t=>{Promise.resolve().then(()=>{if(!t.defaultPrevented)for(const e of t.target.elements)e.__on_r?.()})},{capture:!0}))}function Ye(t){var e=de,n=he;ve(null),me(null);try{return t()}finally{ve(e),me(n)}}const Qe=new Set,Ze=new Set;function tn(t,e,n,r,a){var o={capture:r,passive:a},s=function(t,e,n,r={}){function a(t){if(r.capture||en.call(e,t),!t.cancelBubble)return Ye(()=>n?.call(this,t))}return t.startsWith("pointer")||t.startsWith("touch")||"wheel"===t?re(()=>{e.addEventListener(t,a,r)}):e.addEventListener(t,a,r),a}(t,e,n,o);e!==document.body&&e!==window&&e!==document||Ct(()=>{e.removeEventListener(t,s,o)})}function en(t){var e=this,r=e.ownerDocument,a=t.type,s=t.composedPath?.()||[],i=s[0]||t.target,l=0,u=t.__root;if(u){var c=s.indexOf(u);if(-1!==c&&(e===document||e===window))return void(t.__root=e);var f=s.indexOf(e);if(-1===f)return;c<=f&&(l=c)}if((i=s[l]||t.target)!==e){o(t,"currentTarget",{configurable:!0,get:()=>i||r});var d=de,p=he;ve(null),me(null);try{for(var v,h=[];null!==i;){var m=i.assignedSlot||i.parentNode||i.host||null;try{var g=i["__"+a];if(null!=g&&(!i.disabled||t.target===i))if(n(g)){var[y,...w]=g;y.apply(i,[t,...w])}else g.call(i,t)}catch(b){v?h.push(b):v=b}if(t.cancelBubble||m===e||null===m)break;i=m}if(v){for(let t of h)queueMicrotask(()=>{throw t});throw v}}finally{t.__root=e,delete t.currentTarget,ve(d),me(p)}}}let nn;function rn(t){let e=null,n=st;var r;if(st){for(e=ot,void 0===nn&&(nn=bt(document.head));null!==nn&&(8!==nn.nodeType||nn.data!==N);)nn=_t(nn);null===nn?it(!1):nn=lt(_t(nn))}st||(r=document.head.appendChild(wt()));try{Vt(()=>t(r),$)}finally{n&&(it(!0),nn=ot,lt(e))}}function an(t){var e=document.createElement("template");return e.innerHTML=t,e.content}function on(t,e){var n=he;null===n.nodes_start&&(n.nodes_start=t,n.nodes_end=e)}function sn(t,e){var n,r=!!(1&e),a=!!(2&e),o=!t.startsWith("<!>");return()=>{if(st)return on(ot,null),ot;void 0===n&&(n=an(o?t:"<!>"+t),r||(n=bt(n)));var e=a||ht?document.importNode(n,!0):n.cloneNode(!0);r?on(bt(e),e.lastChild):on(e,e);return e}}function ln(t,e,n="svg"){var r,a=!t.startsWith("<!>"),o=!!(1&e),s=`<${n}>${a?t:"<!>"+t}</${n}>`;return()=>{if(st)return on(ot,null),ot;if(!r){var t=bt(an(s));if(o)for(r=document.createDocumentFragment();bt(t);)r.appendChild(bt(t));else r=bt(t)}var e=r.cloneNode(!0);o?on(bt(e),e.lastChild):on(e,e);return e}}function un(t=""){if(!st){var e=wt(t+"");return on(e,e),e}var n=ot;return 3!==n.nodeType&&(n.before(n=wt()),lt(n)),on(n,n),n}function cn(){if(st)return on(ot,null),ot;var t=document.createDocumentFragment(),e=document.createComment(""),n=wt();return t.append(e,n),on(e,n),t}function fn(t,e){if(st)return he.nodes_end=ot,void ut();null!==t&&t.before(e)}let dn=!0;function pn(t,e){var n=null==e?"":"object"==typeof e?e+"":e;n!==(t.__t??=t.nodeValue)&&(t.__t=n,t.nodeValue=n+"")}function vn(t,e){return gn(t,e)}function hn(t,e){yt(),e.intro=e.intro??!1;const n=e.target,r=st,a=ot;try{for(var o=bt(n);o&&(8!==o.nodeType||o.data!==N);)o=_t(o);if(!o)throw q;it(!0),lt(o),ut();const r=gn(t,{...e,anchor:o});if(null===ot||8!==ot.nodeType||"]"!==ot.data)throw at(),q;return it(!1),r}catch(s){if(s===q)return!1===e.recover&&function(){throw new Error("https://svelte.dev/e/hydration_failed")}(),yt(),Et(n),it(!1),vn(t,e);throw s}finally{it(r),lt(a),nn=void 0}}const mn=new Map;function gn(t,{target:e,anchor:n,props:r={},events:o,context:s,intro:i=!0}){yt();var l=new Set,u=t=>{for(var n=0;n<t.length;n++){var r=t[n];if(!l.has(r)){l.add(r);var a=Ke(r);e.addEventListener(r,en,{passive:a});var o=mn.get(r);void 0===o?(document.addEventListener(r,en,{passive:a}),mn.set(r,1)):mn.set(r,o+1)}}};u(a(Qe)),Ze.add(u);var c=void 0,f=function(t){const e=jt(m,t,!0);return(t={})=>new Promise(n=>{t.outro?Kt(e,()=>{zt(e),n(void 0)}):(zt(e),n(void 0))})}(()=>{var a=n??e.appendChild(wt());return Wt(()=>{s&&(B({}),W.c=s);o&&(r.$$events=o),st&&on(a,null),dn=i,c=t(a,r)||{},dn=!0,st&&(he.nodes_end=ot),s&&z()}),()=>{for(var t of l){e.removeEventListener(t,en);var r=mn.get(t);0===--r?(document.removeEventListener(t,en),mn.delete(t)):mn.set(t,r)}Ze.delete(u),a!==n&&a.parentNode?.removeChild(a)}});return yn.set(c,f),c}let yn=new WeakMap;function wn(t,e,[n,r]=[0,0]){st&&0===n&&ut();var a=t,o=null,s=null,i=D,l=!1;const u=(t,e=!0)=>{l=!0,c(e,t)},c=(t,e)=>{if(i===(i=t))return;let l=!1;if(st&&-1!==r){if(0===n){const t=a.data;t===N?r=0:t===I?r=1/0:(r=parseInt(t.substring(1)))!=r&&(r=i?1/0:-1)}!!i===r>n&&(lt(a=dt()),it(!1),l=!0,r=-1)}i?(o?Xt(o):e&&(o=Wt(()=>e(a))),s&&Kt(s,()=>{s=null})):(s?Xt(s):e&&(s=Wt(()=>e(a,[n+1,r]))),o&&Kt(o,()=>{o=null})),l&&it(!0)};Vt(()=>{l=!1,e(u),l||c(null,null)},n>0?E:0),st&&(a=ot)}function bn(t,e){return e}function _n(t,e,r,o,s,i=null){var l=t,u={flags:e,items:new Map,first:null};if(!!(4&e)){var c=t;l=st?lt(bt(c)):c.appendChild(wt())}st&&ut();var f=null,d=!1,p=At(()=>{var t=r();return n(t)?t:null==t?[]:a(t)});Vt(()=>{var t=Me(p),n=t.length;if(d&&0===n)return;d=0===n;let c=!1;st&&(l.data===I!==(0===n)&&(lt(l=dt()),it(!1),c=!0));if(st){for(var v,h=null,m=0;m<n;m++){if(8===ot.nodeType&&"]"===ot.data){l=ot,c=!0,it(!1);break}var g=t[m],y=o(g,m);v=kn(ot,u,h,null,g,y,m,s,e,r),u.items.set(y,v),h=v}n>0&&lt(dt())}st||function(t,e,n,r,o,s,i){var l,u,c,f,d,p,v=!!(8&o),h=!!(3&o),m=t.length,g=e.items,y=e.first,w=y,b=null,_=[],x=[];if(v)for(p=0;p<m;p+=1)f=s(c=t[p],p),void 0!==(d=g.get(f))&&(d.a?.measure(),(u??=new Set).add(d));for(p=0;p<m;p+=1)if(f=s(c=t[p],p),void 0!==(d=g.get(f))){if(h&&xn(d,c,p,o),0!==(d.e.f&k)&&(Xt(d.e),v&&(d.a?.unfix(),(u??=new Set).delete(d))),d!==w){if(void 0!==l&&l.has(d)){if(_.length<x.length){var S,E=x[0];b=E.prev;var $=_[0],R=_[_.length-1];for(S=0;S<_.length;S+=1)Sn(_[S],E,n);for(S=0;S<x.length;S+=1)l.delete(x[S]);En(e,$.prev,R.next),En(e,b,$),En(e,R,E),w=E,b=R,p-=1,_=[],x=[]}else l.delete(d),Sn(d,w,n),En(e,d.prev,d.next),En(e,d,null===b?e.first:b.next),En(e,b,d),b=d;continue}for(_=[],x=[];null!==w&&w.k!==f;)0===(w.e.f&k)&&(l??=new Set).add(w),x.push(w),w=w.next;if(null===w)continue;d=w}_.push(d),b=d,w=d.next}else{b=kn(w?w.e.nodes_start:n,e,b,null===b?e.first:b.next,c,f,p,r,o,i),g.set(f,b),_=[],x=[],w=b.next}if(null!==w||void 0!==l){for(var A=void 0===l?[]:a(l);null!==w;)0===(w.e.f&k)&&A.push(w),w=w.next;var P=A.length;if(P>0){var L=4&o&&0===m?n:null;if(v){for(p=0;p<P;p+=1)A[p].a?.measure();for(p=0;p<P;p+=1)A[p].a?.fix()}!function(t,e,n,r){for(var a=[],o=e.length,s=0;s<o;s++)Ht(e[s].e,a,!0);var i=o>0&&0===a.length&&null!==n;if(i){var l=n.parentNode;Et(l),l.append(n),r.clear(),En(t,e[0].prev,e[o-1].next)}Gt(a,()=>{for(var n=0;n<o;n++){var a=e[n];i||(r.delete(a.k),En(t,a.prev,a.next)),zt(a.e,!i)}})}(e,A,L,g)}}v&&re(()=>{if(void 0!==u)for(d of u)d.a?.apply()});he.first=e.first&&e.first.e,he.last=b&&b.e}(t,u,l,s,e,o,r),null!==i&&(0===n?f?Xt(f):f=Wt(()=>i(l)):null!==f&&Kt(f,()=>{f=null})),c&&it(!0),Me(p)}),st&&(l=ot)}function xn(t,e,n,r){1&r&&et(t.v,e),2&r?et(t.i,n):t.i=n}function kn(t,e,n,r,a,o,s,i,l,u){var c=!!(1&l)?!(16&l)?Q(a):X(a):a,f=2&l?X(s):s,d={i:f,v:c,k:o,a:null,e:null,prev:n,next:r};try{return d.e=Wt(()=>i(t,c,f,u),st),d.e.prev=n&&n.e,d.e.next=r&&r.e,null===n?e.first=d:(n.next=d,n.e.next=d.e),null!==r&&(r.prev=d,r.e.prev=d.e),d}finally{}}function Sn(t,e,n){for(var r=t.next?t.next.e.nodes_start:n,a=e?e.e.nodes_start:n,o=t.e.nodes_start;o!==r;){var s=_t(o);a.before(o),o=s}}function En(t,e,n){null===e?t.first=n:(e.next=n,e.e.next=n&&n.e),null!==n&&(n.prev=e,n.e.prev=e&&e.e)}function $n(t,e,n,r,a){var o,s=t,i="";Vt(()=>{i!==(i=e()??"")?(void 0!==o&&(zt(o),o=void 0),""!==i&&(o=Wt(()=>{if(st){ot.data;for(var t=ut(),e=t;null!==t&&(8!==t.nodeType||""!==t.data);)e=t,t=_t(t);if(null===t)throw at(),q;return on(ot,e),void(s=lt(t))}var n=an(i+"");on(bt(n),n.lastChild),s.before(n)}))):st&&ut()})}function Rn(t,e,n,r,a){st&&ut();var o=e.$$slots?.[n],s=!1;!0===o&&(o=e.children,s=!0),void 0===o||o(t,s?()=>r:r)}function An(t,e,n){st&&ut();var r,a,o=t;Vt(()=>{r!==(r=e())&&(a&&(Kt(a),a=null),r&&(a=Wt(()=>n(o,r))))},E),st&&(o=ot)}const Pn=[..." \t\n\r\f \v\ufeff"];function Ln(t,e,n,r,a,o){var s=t.__className;if(st||s!==n||void 0===s){var i=function(t,e,n){var r=null==t?"":""+t;if(e&&(r=r?r+" "+e:e),n)for(var a in n)if(n[a])r=r?r+" "+a:a;else if(r.length)for(var o=a.length,s=0;(s=r.indexOf(a,s))>=0;){var i=s+o;0!==s&&!Pn.includes(r[s-1])||i!==r.length&&!Pn.includes(r[i])?s=i:r=(0===s?"":r.substring(0,s))+r.substring(i+1)}return""===r?null:r}(n,r,o);st&&i===t.getAttribute("class")||(null==i?t.removeAttribute("class"):t.className=i),t.__className=n}else if(o&&a!==o)for(var l in o){var u=!!o[l];null!=a&&u===!!a[l]||t.classList.toggle(l,u)}return o}function On(t,e,n,r){var a=t.__style;if(st||a!==e){var o=function(t){return null==t?null:String(t)}(e);st&&o===t.getAttribute("style")||(null==o?t.removeAttribute("style"):t.style.cssText=o),t.__style=e}return r}const jn=Symbol("is custom element"),Cn=Symbol("is html");function Tn(t){if(st){var e,n=!1,r=()=>{if(!n){if(n=!0,t.hasAttribute("value")){var e=t.value;Un(t,"value",null),t.value=e}if(t.hasAttribute("checked")){var r=t.checked;Un(t,"checked",null),t.checked=r}}};t.__on_r=r,e=r,0===te.length&&Qt(ne),te.push(e),Xe()}}function Un(t,e,n,r){var a=function(t){return t.__attributes??={[jn]:t.nodeName.includes("-"),[Cn]:t.namespaceURI===M}}(t);st&&(a[e]=t.getAttribute(e),"src"===e||"srcset"===e||"href"===e&&"LINK"===t.nodeName)||a[e]!==(a[e]=n)&&("loading"===e&&(t[O]=n),null==n?t.removeAttribute(e):"string"!=typeof n&&function(t){var e,n=Nn.get(t.nodeName);if(n)return n;Nn.set(t.nodeName,n=[]);var r=t,a=Element.prototype;for(;a!==r;){for(var o in e=i(r))e[o].set&&n.push(o);r=c(r)}return n}(t).includes(e)?t[e]=n:t.setAttribute(e,n))}var Nn=new Map;const In={tick:t=>requestAnimationFrame(t),now:()=>performance.now(),tasks:new Set};function qn(){const t=In.now();In.tasks.forEach(e=>{e.c(t)||(In.tasks.delete(e),e.f())}),0!==In.tasks.size&&In.tick(qn)}function Dn(t,e){Ye(()=>{t.dispatchEvent(new CustomEvent(e))})}function Mn(t){if("float"===t)return"cssFloat";if("offset"===t)return"cssOffset";if(t.startsWith("--"))return t;const e=t.split("-");return 1===e.length?e[0]:e[0]+e.slice(1).map(t=>t[0].toUpperCase()+t.slice(1)).join("")}function Vn(t){const e={},n=t.split(";");for(const r of n){const[t,n]=r.split(":");if(!t||void 0===n)break;e[Mn(t.trim())]=n.trim()}return e}const Wn=t=>t;function Fn(t,e,n,r){var a,o,s,i=!!(4&t),l=e.inert,u=e.style.overflow;function c(){var t=de,o=he;ve(null),me(null);try{return a??=n()(e,r?.()??{},{direction:"both"})}finally{ve(t),me(o)}}var f={is_global:i,in(){e.inert=l,Dn(e,"introstart"),o=Bn(e,c(),s,1,()=>{Dn(e,"introend"),o?.abort(),o=a=void 0,e.style.overflow=u})},out(t){e.inert=!0,Dn(e,"outrostart"),s=Bn(e,c(),o,0,()=>{Dn(e,"outroend"),t?.()})},stop:()=>{o?.abort(),s?.abort()}},d=he;if((d.transitions??=[]).push(f),dn){var p=i;if(!p){for(var v=d.parent;v&&0!==(v.f&E);)for(;(v=v.parent)&&!(16&v.f););p=!v||!!(32768&v.f)}p&&Nt(()=>{Ve(()=>f.in())})}}function Bn(t,e,n,r,a){var o=1===r;if("function"==typeof e){var s,i=!1;return re(()=>{if(!i){var l=e({direction:o?"in":"out"});s=Bn(t,l,n,r,a)}}),{abort:()=>{i=!0,s?.abort()},deactivate:()=>s.deactivate(),reset:()=>s.reset(),t:()=>s.t()}}if(n?.deactivate(),!e?.duration)return a(),{abort:d,deactivate:d,reset:d,t:()=>r};const{delay:l=0,css:u,tick:c,easing:f=Wn}=e;var p=[];if(o&&void 0===n&&(c&&c(0,1),u)){var v=Vn(u(0,1));p.push(v,v)}var h=()=>1-r,m=t.animate(p,{duration:l});return m.onfinish=()=>{var o=n?.t()??1-r;n?.abort();var s=r-o,i=e.duration*Math.abs(s),l=[];if(i>0){var d=!1;if(u)for(var p=Math.ceil(i/(1e3/60)),v=0;v<=p;v+=1){var g=o+s*f(v/p),y=Vn(u(g,1-g));l.push(y),d||="hidden"===y.overflow}d&&(t.style.overflow="hidden"),h=()=>{var t=m.currentTime;return o+s*f(t/i)},c&&function(t){let e;0===In.tasks.size&&In.tick(qn),new Promise(n=>{In.tasks.add(e={c:t,f:n})})}(()=>{if("running"!==m.playState)return!1;var t=h();return c(t,1-t),!0})}(m=t.animate(l,{duration:i,fill:"forwards"})).onfinish=()=>{h=()=>r,c?.(r,1-r),a()}},{abort:()=>{m&&(m.cancel(),m.effect=null,m.onfinish=d)},deactivate:()=>{a=d},reset:()=>{0===r&&c?.(1,0)},t:()=>h()}}function zn(t,e,n=e){var r=J();!function(t,e,n,r=n){t.addEventListener(e,()=>Ye(n));const a=t.__on_r;t.__on_r=a?()=>{a(),r(!0)}:()=>r(!0),Xe()}(t,"input",a=>{var o=a?t.defaultValue:t.value;if(o=Jn(t)?Kn(o):o,n(o),r&&o!==(o=e())){var s=t.selectionStart,i=t.selectionEnd;t.value=o??"",null!==i&&(t.selectionStart=s,t.selectionEnd=Math.min(i,t.value.length))}}),(st&&t.defaultValue!==t.value||null==Ve(e)&&t.value)&&n(Jn(t)?Kn(t.value):t.value),Dt(()=>{var n=e();Jn(t)&&n===Kn(t.value)||("date"!==t.type||n||t.value)&&n!==t.value&&(t.value=n??"")})}function Jn(t){var e=t.type;return"number"===e||"range"===e}function Kn(t){return""===t?null:+t}function Gn(t,e,n){var r=s(t,e);r&&r.set&&(t[e]=n,Ct(()=>{t[e]=null}))}function Hn(t,e){return t===e||t?.[P]===e}function Xn(t={},e,n,r){return Nt(()=>{var r,a;return Dt(()=>{r=a,a=[],Ve(()=>{t!==n(...a)&&(e(t,...a),r&&Hn(n(...r),t)&&e(null,...r))})}),()=>{re(()=>{a&&Hn(n(...a),t)&&e(null,...a)})}}),t}function Yn(t){return function(...e){return e[0].stopPropagation(),t?.apply(this,e)}}function Qn(t){return function(...e){return e[0].preventDefault(),t?.apply(this,e)}}function Zn(t=!1){const e=W,n=e.l.u;if(!n)return;let r=()=>Be(e.s);if(t){let t=0,n={};const a=$t(()=>{let r=!1;const a=e.s;for(const t in a)a[t]!==n[t]&&(n[t]=a[t],r=!0);return r&&t++,t});r=()=>Me(a)}n.b.length&&Ut(()=>{tr(e,r),v(n.b)}),Tt(()=>{const t=Ve(()=>n.m.map(p));return()=>{for(const e of t)"function"==typeof e&&e()}}),n.a.length&&Tt(()=>{tr(e,r),v(n.a)})}function tr(t,e){if(t.l.s)for(const n of t.l.s)Me(n);e()}function er(t,e){var r=t.$$events?.[e.type],a=n(r)?r.slice():null==r?[]:[r];for(var o of a)o.call(this,e)}function nr(t,e,n){if(null==t)return e(void 0),n&&n(void 0),d;const r=Ve(()=>t.subscribe(e,n));return r.unsubscribe?()=>r.unsubscribe():r}const rr=[];function ar(t,e=d){let n=null;const r=new Set;function a(e){if(C(t,e)&&(t=e,n)){const e=!rr.length;for(const n of r)n[1](),rr.push(n,t);if(e){for(let t=0;t<rr.length;t+=2)rr[t][0](rr[t+1]);rr.length=0}}}function o(e){a(e(t))}return{set:a,update:o,subscribe:function(s,i=d){const l=[s,i];return r.add(l),1===r.size&&(n=e(a,o)||d),s(t),()=>{r.delete(l),0===r.size&&n&&(n(),n=null)}}}}function or(t,e,n){const r=!Array.isArray(t),a=r?[t]:t;if(!a.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=e.length<2;return function(t,e){return{subscribe:ar(t,e).subscribe}}(n,(t,n)=>{let s=!1;const i=[];let l=0,u=d;const c=()=>{if(l)return;u();const a=e(r?i[0]:i,t,n);o?t(a):u="function"==typeof a?a:d},f=a.map((t,e)=>nr(t,t=>{i[e]=t,l&=~(1<<e),s&&c()},()=>{l|=1<<e}));return s=!0,c(),function(){v(f),u(),s=!1}})}function sr(t){let e;return nr(t,t=>e=t)(),e}let ir=!1,lr=Symbol();function ur(t,e,n){const r=n[e]??={store:null,source:Q(void 0),unsubscribe:d};if(r.store!==t&&!(lr in n))if(r.unsubscribe(),r.store=t??null,null==t)r.source.v=void 0,r.unsubscribe=d;else{var a=!0;r.unsubscribe=nr(t,t=>{a?r.source.v=t:tt(r.source,t)}),a=!1}return t&&lr in n?sr(t):Me(r.source)}function cr(t,e){return t.set(e),e}function fr(){const t={};return[t,function(){Ct(()=>{for(var e in t){t[e].unsubscribe()}o(t,lr,{enumerable:!1,value:!0})})}]}function dr(t){return t.ctx?.d??!1}function pr(t,e,n,r){var a,o=!!(1&n),i=!U||!!(2&n),l=!!(8&n),u=!!(16&n),c=!1;l?[a,c]=function(t){var e=ir;try{return ir=!1,[t(),ir]}finally{ir=e}}(()=>t[e]):a=t[e];var f,d=P in t||L in t,p=l&&(s(t,e)?.set??(d&&e in t&&(n=>t[e]=n)))||void 0,v=r,h=!0,m=!1,g=()=>(m=!0,h&&(h=!1,v=u?Ve(r):r),v);if(void 0===a&&void 0!==r&&(p&&i&&function(){throw new Error("https://svelte.dev/e/props_invalid_value")}(),a=g(),p&&p(a)),i)f=()=>{var n=t[e];return void 0===n?g():(h=!0,m=!1,n)};else{var y=(o?$t:At)(()=>t[e]);y.f|=131072,f=()=>{var t=Me(y);return void 0!==t&&(v=void 0),void 0===t?v:t}}if(!(4&n))return f;if(p){var w=t.$$legacy;return function(t,e){return arguments.length>0?(i&&e&&!w&&!c||p(e?f():t),t):f()}}var b=!1,_=Q(a),x=$t(()=>{var t=f(),e=Me(_);return b?(b=!1,e):_.v=t});return l&&Me(x),o||(x.equals=T),function(t,e){if(arguments.length>0){const n=e?Me(x):i&&l?K(t):t;if(!x.equals(n)){if(b=!0,tt(_,n),m&&void 0!==v&&(v=n),dr(x))return t;Ve(()=>Me(x))}return t}return dr(x)?x.v:Me(x)}}function vr(t){return class extends hr{constructor(e){super({component:t,...e})}}}class hr{#t;#e;constructor(t){var e=new Map,n=(t,n)=>{var r=Q(n);return e.set(t,r),r};const r=new Proxy({...t.props||{},$$events:{}},{get:(t,r)=>Me(e.get(r)??n(r,Reflect.get(t,r))),has:(t,r)=>r===L||(Me(e.get(r)??n(r,Reflect.get(t,r))),Reflect.has(t,r)),set:(t,r,a)=>(tt(e.get(r)??n(r,a),a),Reflect.set(t,r,a))});this.#e=(t.hydrate?hn:vn)(t.component,{target:t.target,anchor:t.anchor,props:r,context:t.context,intro:t.intro??!1,recover:t.recover}),t?.props?.$$host&&!1!==t.sync||qe(),this.#t=r.$$events;for(const a of Object.keys(this.#e))"$set"!==a&&"$destroy"!==a&&"$on"!==a&&o(this,a,{get(){return this.#e[a]},set(t){this.#e[a]=t},enumerable:!0});this.#e.$set=t=>{Object.assign(r,t)},this.#e.$destroy=()=>{!function(t,e){const n=yn.get(t);n?(yn.delete(t),n(e)):Promise.resolve()}(this.#e)}}$set(t){this.#e.$set(t)}$on(t,e){this.#t[t]=this.#t[t]||[];const n=(...t)=>e.call(this,...t);return this.#t[t].push(n),()=>{this.#t[t]=this.#t[t].filter(t=>t!==n)}}$destroy(){this.#e.$destroy()}}function mr(t){var e,n;null===W&&V(),U&&null!==W.l?(e=W,n=e.l,n.u??={a:[],b:[],m:[]}).m.push(t):Tt(()=>{const e=Ve(t);if("function"==typeof e)return e})}function gr(t){null===W&&V(),mr(()=>()=>Ve(t))}function yr(){const t=W;return null===t&&V(),(e,r,a)=>{const o=t.s.$$events?.[e];if(o){const s=n(o)?o.slice():[o],i=function(t,e,{bubbles:n=!1,cancelable:r=!1}={}){return new CustomEvent(t,{detail:e,bubbles:n,cancelable:r})}(e,r,a);for(const e of s)e.call(t.x,i);return!i.defaultPrevented}return!0}}function wr(t){for(const e in t)t[e]=decodeURIComponent(t[e]);return t}function br({href:t}){return t.split("#")[0]}function _r(t,e,n,r=!1){const a=new URL(t);Object.defineProperty(a,"searchParams",{value:new Proxy(a.searchParams,{get(t,r){if("get"===r||"getAll"===r||"has"===r)return e=>(n(e),t[r](e));e();const a=Reflect.get(t,r);return"function"==typeof a?a.bind(t):a}}),enumerable:!0,configurable:!0});const o=["href","pathname","search","toString","toJSON"];r&&o.push("hash");for(const s of o)Object.defineProperty(a,s,{get:()=>(e(),t[s]),enumerable:!0,configurable:!0});return a}new URL("sveltekit-internal://");const xr=window.fetch;window.fetch=(t,e)=>("GET"!==(t instanceof Request?t.method:e?.method||"GET")&&kr.delete(Er(t)),xr(t,e));const kr=new Map;function Sr(t,e){const n=Er(t,e),r=document.querySelector(n);if(r?.textContent){let{body:t,...e}=JSON.parse(r.textContent);const a=r.getAttribute("data-ttl");a&&kr.set(n,{body:t,init:e,ttl:1e3*Number(a)});return null!==r.getAttribute("data-b64")&&(t=function(t){const e=atob(t),n=new Uint8Array(e.length);for(let r=0;r<e.length;r++)n[r]=e.charCodeAt(r);return n.buffer}(t)),Promise.resolve(new Response(t,e))}return window.fetch(t,e)}function Er(t,e){let n=`script[data-sveltekit-fetched][data-url=${JSON.stringify(t instanceof Request?t.url:t)}]`;if(e?.headers||e?.body){const t=[];e.headers&&t.push([...new Headers(e.headers)].join(",")),e.body&&("string"==typeof e.body||ArrayBuffer.isView(e.body))&&t.push(e.body),n+=`[data-hash="${function(...t){let e=5381;for(const n of t)if("string"==typeof n){let t=n.length;for(;t;)e=33*e^n.charCodeAt(--t)}else{if(!ArrayBuffer.isView(n))throw new TypeError("value must be a string or TypedArray");{const t=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);let r=t.length;for(;r;)e=33*e^t[--r]}}return(e>>>0).toString(36)}(...t)}"]`}return n}const $r=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function Rr(t){const e=[];var n;return{pattern:"/"===t?/^\/$/:new RegExp(`^${(n=t,n.slice(1).split("/").filter(Ar)).map(t=>{const n=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(t);if(n)return e.push({name:n[1],matcher:n[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const r=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(t);if(r)return e.push({name:r[1],matcher:r[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!t)return;const a=t.split(/\[(.+?)\](?!\])/);return"/"+a.map((t,n)=>{if(n%2){if(t.startsWith("x+"))return Pr(String.fromCharCode(parseInt(t.slice(2),16)));if(t.startsWith("u+"))return Pr(String.fromCharCode(...t.slice(2).split("-").map(t=>parseInt(t,16))));const r=$r.exec(t),[,o,s,i,l]=r;return e.push({name:i,matcher:l,optional:!!o,rest:!!s,chained:!!s&&(1===n&&""===a[0])}),s?"(.*?)":o?"([^/]*)?":"([^/]+?)"}return Pr(t)}).join("")}).join("")}/?$`),params:e}}function Ar(t){return!/^\([^)]+\)$/.test(t)}function Pr(t){return t.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function Lr({nodes:t,server_loads:e,dictionary:n,matchers:r}){const a=new Set(e);return Object.entries(n).map(([e,[n,a,i]])=>{const{pattern:l,params:u}=Rr(e),c={id:e,exec:t=>{const e=l.exec(t);if(e)return function(t,e,n){const r={},a=t.slice(1),o=a.filter(t=>void 0!==t);let s=0;for(let i=0;i<e.length;i+=1){const t=e[i];let l=a[i-s];if(t.chained&&t.rest&&s&&(l=a.slice(i-s,i+1).filter(t=>t).join("/"),s=0),void 0!==l){if(!t.matcher||n[t.matcher](l)){r[t.name]=l;const n=e[i+1],u=a[i+1];n&&!n.rest&&n.optional&&u&&t.chained&&(s=0),n||u||Object.keys(r).length!==o.length||(s=0);continue}if(!t.optional||!t.chained)return;s++}else t.rest&&(r[t.name]="")}if(!s)return r}(e,u,r)},errors:[1,...i||[]].map(e=>t[e]),layouts:[0,...a||[]].map(s),leaf:o(n)};return c.errors.length=c.layouts.length=Math.max(c.errors.length,c.layouts.length),c});function o(e){const n=e<0;return n&&(e=~e),[n,t[e]]}function s(e){return void 0===e?e:[a.has(e),t[e]]}}function Or(t,e=JSON.parse){try{return e(sessionStorage[t])}catch{}}function jr(t,e,n=JSON.stringify){const r=n(e);try{sessionStorage[t]=r}catch{}}const Cr=globalThis.__sveltekit_1poeliz?.base??"/speed-reading",Tr=globalThis.__sveltekit_1poeliz?.assets??"https://webtoolhost.qiaoxuesi.com/speed-reading/latest",Ur="sveltekit:snapshot",Nr="sveltekit:scroll",Ir="sveltekit:states",qr="sveltekit:pageurl",Dr="sveltekit:history",Mr="sveltekit:navigation",Vr={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},Wr=location.origin;function Fr(t){if(t instanceof URL)return t;let e=document.baseURI;if(!e){const t=document.getElementsByTagName("base");e=t.length?t[0].href:document.URL}return new URL(t,e)}function Br(){return{x:pageXOffset,y:pageYOffset}}function zr(t,e){return t.getAttribute(`data-sveltekit-${e}`)}const Jr={...Vr,"":Vr.hover};function Kr(t){let e=t.assignedSlot??t.parentNode;return 11===e?.nodeType&&(e=e.host),e}function Gr(t,e){for(;t&&t!==e;){if("A"===t.nodeName.toUpperCase()&&t.hasAttribute("href"))return t;t=Kr(t)}}function Hr(t,e,n){let r;try{if(r=new URL(t instanceof SVGAElement?t.href.baseVal:t.href,document.baseURI),n&&r.hash.match(/^#[^/]/)){const t=location.hash.split("#")[1]||"/";r.hash=`#${t}${r.hash}`}}catch{}const a=t instanceof SVGAElement?t.target.baseVal:t.target;return{url:r,external:!r||!!a||ta(r,e,n)||(t.getAttribute("rel")||"").split(/\s+/).includes("external"),target:a,download:r?.origin===Wr&&t.hasAttribute("download")}}function Xr(t){let e=null,n=null,r=null,a=null,o=null,s=null,i=t;for(;i&&i!==document.documentElement;)null===r&&(r=zr(i,"preload-code")),null===a&&(a=zr(i,"preload-data")),null===e&&(e=zr(i,"keepfocus")),null===n&&(n=zr(i,"noscroll")),null===o&&(o=zr(i,"reload")),null===s&&(s=zr(i,"replacestate")),i=Kr(i);function l(t){switch(t){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:Jr[r??"off"],preload_data:Jr[a??"off"],keepfocus:l(e),noscroll:l(n),reload:l(o),replace_state:l(s)}}function Yr(t){const e=ar(t);let n=!0;return{notify:function(){n=!0,e.update(t=>t)},set:function(t){n=!1,e.set(t)},subscribe:function(t){let r;return e.subscribe(e=>{(void 0===r||n&&e!==r)&&t(r=e)})}}}const Qr={v:()=>{}};function Zr(){const{set:t,subscribe:e}=ar(!1);let n;return{subscribe:e,check:async function(){clearTimeout(n);try{const e=await fetch(`${Tr}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!e.ok)return!1;const r="1753428411163"!==(await e.json()).version;return r&&(t(!0),Qr.v(),clearTimeout(n)),r}catch{return!1}}}}function ta(t,e,n){return t.origin!==Wr||!t.pathname.startsWith(e)||!!n&&(t.pathname!==e+"/"&&t.pathname!==e+"/index.html"&&("file:"!==t.protocol||t.pathname.replace(/\/[^/]+\.html?$/,"")!==e))}function ea(t){}const na=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);new Set([...na,"entries"]);const ra=new Set([...na]);new Set([...ra,"actions","entries"]);class aa{constructor(t,e){this.status=t,this.body="string"==typeof e?{message:e}:e||{message:`Error: ${t}`}}toString(){return JSON.stringify(this.body)}}class oa{constructor(t,e){this.status=t,this.location=e}}class sa extends Error{constructor(t,e,n){super(n),this.status=t,this.text=e}}function ia(t){return t instanceof aa||t instanceof sa?t.status:500}let la,ua,ca;mr.toString().includes("$$")||/function \w+\(\) \{\}/.test(mr.toString())?(la={data:{},form:null,error:null,params:{},route:{id:null},state:{},status:-1,url:new URL("https://example.com")},ua={current:null},ca={current:!1}):(la=new class{#n=Y({});get data(){return Me(this.#n)}set data(t){tt(this.#n,t)}#r=Y(null);get form(){return Me(this.#r)}set form(t){tt(this.#r,t)}#a=Y(null);get error(){return Me(this.#a)}set error(t){tt(this.#a,t)}#o=Y({});get params(){return Me(this.#o)}set params(t){tt(this.#o,t)}#s=Y({id:null});get route(){return Me(this.#s)}set route(t){tt(this.#s,t)}#i=Y({});get state(){return Me(this.#i)}set state(t){tt(this.#i,t)}#l=Y(-1);get status(){return Me(this.#l)}set status(t){tt(this.#l,t)}#u=Y(new URL("https://example.com"));get url(){return Me(this.#u)}set url(t){tt(this.#u,t)}},ua=new class{#c=Y(null);get current(){return Me(this.#c)}set current(t){tt(this.#c,t)}},ca=new class{#c=Y(!1);get current(){return Me(this.#c)}set current(t){tt(this.#c,t)}},Qr.v=()=>ca.current=!0);const fa=new Set(["icon","shortcut icon","apple-touch-icon"]),da=Or(Nr)??{},pa=Or(Ur)??{},va={url:Yr({}),page:Yr({}),navigating:ar(null),updated:Zr()};function ha(t){da[t]=Br()}function ma(t){return location.href=t.href,new Promise(()=>{})}async function ga(){if("serviceWorker"in navigator){const t=await navigator.serviceWorker.getRegistration(Cr||"/");t&&await t.update()}}function ya(){}let wa,ba,_a,xa,ka,Sa;const Ea=[],$a=[];let Ra=null;const Aa=new Map,Pa=new Set,La=new Set,Oa=new Set;let ja,Ca,Ta,Ua,Na={branch:[],error:null,url:null},Ia=!1,qa=!1,Da=!0,Ma=!1,Va=!1,Wa=!1,Fa=!1;const Ba=new Set;async function za(t,e,n){document.URL!==location.href&&(location.href=location.href),Sa=t,await(t.hooks.init?.()),wa=Lr(t),xa=document.documentElement,ka=e,ba=t.nodes[0],_a=t.nodes[1],ba(),_a(),Ca=history.state?.[Dr],Ta=history.state?.[Mr],Ca||(Ca=Ta=Date.now(),history.replaceState({...history.state,[Dr]:Ca,[Mr]:Ta},""));const r=da[Ca];r&&(history.scrollRestoration="manual",scrollTo(r.x,r.y)),n?await async function(t,{status:e=200,error:n,node_ids:r,params:a,route:o,server_route:s,data:i,form:l}){Ia=!0;const u=new URL(location.href);let c,f;({params:a={},route:o={id:null}}=await io(u,!1)||{}),c=wa.find(({id:t})=>t===o.id);let d=!0;try{const t=r.map(async(e,n)=>{const r=i[n];return r?.uses&&(r.uses=mo(r.uses)),Za({loader:Sa.nodes[e],url:u,params:a,route:o,parent:async()=>{const e={};for(let r=0;r<n;r+=1)Object.assign(e,(await t[r]).data);return e},server_data_node:no(r)})}),s=await Promise.all(t);if(c){const t=c.layouts;for(let e=0;e<t.length;e++)t[e]||s.splice(e,0,void 0)}f=Qa({url:u,params:a,branch:s,status:e,error:n,form:l,route:c??null})}catch(p){if(p instanceof oa)return void(await ma(new URL(p.location,location.href)));f=await so({status:ia(p),error:await po(p,{url:u,params:a,route:o}),url:u,route:o}),t.textContent="",d=!1}f.props.page&&(f.props.page.state={});Ya(f,t,d)}(ka,n):await co({type:"enter",url:Fr(Sa.hash?wo(new URL(location.href)):location.href),replace_state:!0}),function(){history.scrollRestoration="manual",addEventListener("beforeunload",t=>{let e=!1;if(Ga(),!Ma){const t=go(Na,void 0,null,"leave"),n={...t.navigation,cancel:()=>{e=!0,t.reject(new Error("navigation cancelled"))}};Pa.forEach(t=>t(n))}e?(t.preventDefault(),t.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&Ga()}),navigator.connection?.saveData||function(){let t,e,n;function r(t){t.defaultPrevented||o(t.composedPath()[0],Vr.tap)}xa.addEventListener("mousemove",e=>{const n=e.target;clearTimeout(t),t=setTimeout(()=>{o(n,Vr.hover)},20)}),xa.addEventListener("mousedown",r),xa.addEventListener("touchstart",r,{passive:!0});const a=new IntersectionObserver(t=>{for(const e of t)e.isIntersecting&&(Xa(new URL(e.target.href)),a.unobserve(e.target))},{threshold:0});async function o(t,r){const a=Gr(t,xa);if(!a||a===e&&r>=n)return;const{url:o,external:s,download:i}=Hr(a,Cr,Sa.hash);if(s||i)return;const l=Xr(a),u=o&&lo(Na.url)===lo(o);if(!l.reload&&!u)if(r<=l.preload_data){e=a,n=Vr.tap;const t=await io(o,!1);if(!t)return;!async function(t){if(t.id!==Ra?.id){const e={};Ba.add(e),Ra={id:t.id,token:e,promise:ao({...t,preload:e}).then(t=>(Ba.delete(e),"loaded"===t.type&&t.state.error&&(Ra=null),t))}}Ra.promise}(t)}else r<=l.preload_code&&(e=a,n=r,Xa(o))}function s(){a.disconnect();for(const t of xa.querySelectorAll("a")){const{url:e,external:n,download:r}=Hr(t,Cr,Sa.hash);if(n||r)continue;const o=Xr(t);o.reload||(o.preload_code===Vr.viewport&&a.observe(t),o.preload_code===Vr.eager&&Xa(e))}}Oa.add(s),s()}();xa.addEventListener("click",async e=>{if(e.button||1!==e.which)return;if(e.metaKey||e.ctrlKey||e.shiftKey||e.altKey)return;if(e.defaultPrevented)return;const n=Gr(e.composedPath()[0],xa);if(!n)return;const{url:r,external:a,target:o,download:s}=Hr(n,Cr,Sa.hash);if(!r)return;if("_parent"===o||"_top"===o){if(window.parent!==window)return}else if(o&&"_self"!==o)return;const i=Xr(n);if(!(n instanceof SVGAElement)&&r.protocol!==location.protocol&&"https:"!==r.protocol&&"http:"!==r.protocol)return;if(s)return;const[l,u]=(Sa.hash?r.hash.replace(/^#/,""):r.href).split("#"),c=l===br(location);if(!a&&(!i.reload||c&&u)){if(void 0!==u&&c){const[,a]=Na.url.href.split("#");if(a===u){if(e.preventDefault(),""===u||"top"===u&&null===n.ownerDocument.getElementById("top"))window.scrollTo({top:0});else{const t=n.ownerDocument.getElementById(decodeURIComponent(u));t&&(t.scrollIntoView(),t.focus())}return}if(Va=!0,ha(Ca),t(r),!i.replace_state)return;Va=!1}e.preventDefault(),await new Promise(t=>{requestAnimationFrame(()=>{setTimeout(t,0)}),setTimeout(t,100)}),await co({type:"link",url:r,keepfocus:i.keepfocus,noscroll:i.noscroll,replace_state:i.replace_state??r.href===location.href})}else uo({url:r,type:"link"})?Ma=!0:e.preventDefault()}),xa.addEventListener("submit",t=>{if(t.defaultPrevented)return;const e=HTMLFormElement.prototype.cloneNode.call(t.target),n=t.submitter;if("_blank"===(n?.formTarget||e.target))return;if("get"!==(n?.formMethod||e.method))return;const r=new URL(n?.hasAttribute("formaction")&&n?.formAction||e.action);if(ta(r,Cr,!1))return;const a=t.target,o=Xr(a);if(o.reload)return;t.preventDefault(),t.stopPropagation();const s=new FormData(a),i=n?.getAttribute("name");i&&s.append(i,n?.getAttribute("value")??""),r.search=new URLSearchParams(s).toString(),co({type:"form",url:r,keepfocus:o.keepfocus,noscroll:o.noscroll,replace_state:o.replace_state??r.href===location.href})}),addEventListener("popstate",async e=>{if(e.state?.[Dr]){const n=e.state[Dr];if(Ua={},n===Ca)return;const r=da[n],a=e.state[Ir]??{},o=new URL(e.state[qr]??location.href),s=e.state[Mr],i=!!Na.url&&br(location)===br(Na.url);if(s===Ta&&(Wa||i))return a!==la.state&&(la.state=a),t(o),da[Ca]=Br(),r&&scrollTo(r.x,r.y),void(Ca=n);const l=n-Ca;await co({type:"popstate",url:o,popped:{state:a,scroll:r,delta:l},accept:()=>{Ca=n,Ta=s},block:()=>{history.go(-l)},nav_token:Ua})}else if(!Va){t(new URL(location.href)),Sa.hash&&location.reload()}}),addEventListener("hashchange",()=>{Va&&(Va=!1,history.replaceState({...history.state,[Dr]:++Ca,[Mr]:Ta},"",location.href))});for(const e of document.querySelectorAll("link"))fa.has(e.rel)&&(e.href=e.href);function t(t){Na.url=la.url=t,va.page.set(yo(la)),va.page.notify()}addEventListener("pageshow",t=>{t.persisted&&va.navigating.set(ua.current=null)})}()}function Ja(t){$a.some(t=>t?.snapshot)&&(pa[t]=$a.map(t=>t?.snapshot?.capture()))}function Ka(t){pa[t]?.forEach((t,e)=>{$a[e]?.snapshot?.restore(t)})}function Ga(){ha(Ca),jr(Nr,da),Ja(Ta),jr(Ur,pa)}async function Ha(t,e,n,r){return co({type:"goto",url:Fr(t),keepfocus:e.keepFocus,noscroll:e.noScroll,replace_state:e.replaceState,state:e.state,redirect_count:n,nav_token:r,accept:()=>{e.invalidateAll&&(Fa=!0),e.invalidate&&e.invalidate.forEach(vo)}})}async function Xa(t){const e=(await io(t,!1))?.route;e&&await Promise.all([...e.layouts,e.leaf].map(t=>t?.[1]()))}function Ya(t,e,n){Na=t.state;const r=document.querySelector("style[data-sveltekit]");if(r&&r.remove(),Object.assign(la,t.props.page),ja=new Sa.root({target:e,props:{...t.props,stores:va,components:$a},hydrate:n,sync:!1}),Ka(Ta),n){const t={from:null,to:{params:Na.params,route:{id:Na.route?.id??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};Oa.forEach(e=>e(t))}qa=!0}function Qa({url:t,params:e,branch:n,status:r,error:a,route:o,form:s}){let i="never";if(!Cr||t.pathname!==Cr&&t.pathname!==Cr+"/")for(const h of n)void 0!==h?.slash&&(i=h.slash);else i="always";var l,u;t.pathname=(l=t.pathname,u=i,"/"===l||"ignore"===u?l:"never"===u?l.endsWith("/")?l.slice(0,-1):l:"always"!==u||l.endsWith("/")?l:l+"/"),t.search=t.search;const c={type:"loaded",state:{url:t,params:e,branch:n,error:a,route:o},props:{constructors:(f=n,f.filter(t=>null!=t)).map(t=>t.node.component),page:yo(la)}};var f;void 0!==s&&(c.props.form=s);let d={},p=!la,v=0;for(let h=0;h<Math.max(n.length,Na.branch.length);h+=1){const t=n[h],e=Na.branch[h];t?.data!==e?.data&&(p=!0),t&&(d={...d,...t.data},p&&(c.props[`data_${v}`]=d),v+=1)}return(!Na.url||t.href!==Na.url.href||Na.error!==a||void 0!==s&&s!==la.form||p)&&(c.props.page={error:a,params:e,route:{id:o?.id??null},state:{},status:r,url:new URL(t),form:s??null,data:p?d:la.data}),c}async function Za({loader:t,parent:e,url:n,params:r,route:a,server_data_node:o}){let s=null,i=!0;const l={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},u=await t();if(u.universal?.load){let t=function(...t){for(const e of t){const{href:t}=new URL(e,n);l.dependencies.add(t)}};const c={route:new Proxy(a,{get:(t,e)=>(i&&(l.route=!0),t[e])}),params:new Proxy(r,{get:(t,e)=>(i&&l.params.add(e),t[e])}),data:o?.data??null,url:_r(n,()=>{i&&(l.url=!0)},t=>{i&&l.search_params.add(t)},Sa.hash),async fetch(e,r){e instanceof Request&&(r={body:"GET"===e.method||"HEAD"===e.method?void 0:await e.blob(),cache:e.cache,credentials:e.credentials,headers:[...e.headers].length?e.headers:void 0,integrity:e.integrity,keepalive:e.keepalive,method:e.method,mode:e.mode,redirect:e.redirect,referrer:e.referrer,referrerPolicy:e.referrerPolicy,signal:e.signal,...r});const{resolved:a,promise:o}=to(e,r,n);return i&&t(a.href),o},setHeaders:()=>{},depends:t,parent:()=>(i&&(l.parent=!0),e()),untrack(t){i=!1;try{return t()}finally{i=!0}}};s=await u.universal.load.call(null,c)??null}return{node:u,loader:t,server:o,universal:u.universal?.load?{type:"data",data:s,uses:l}:null,data:s??o?.data??null,slash:u.universal?.trailingSlash??o?.slash}}function to(t,e,n){let r=t instanceof Request?t.url:t;const a=new URL(r,n);a.origin===n.origin&&(r=a.href.slice(n.origin.length));const o=qa?function(t,e,n){if(kr.size>0){const e=Er(t,n),r=kr.get(e);if(r){if(performance.now()<r.ttl&&["default","force-cache","only-if-cached",void 0].includes(n?.cache))return new Response(r.body,r.init);kr.delete(e)}}return window.fetch(e,n)}(r,a.href,e):Sr(r,e);return{resolved:a,promise:o}}function eo(t,e,n,r,a,o){if(Fa)return!0;if(!a)return!1;if(a.parent&&t)return!0;if(a.route&&e)return!0;if(a.url&&n)return!0;for(const s of a.search_params)if(r.has(s))return!0;for(const s of a.params)if(o[s]!==Na.params[s])return!0;for(const s of a.dependencies)if(Ea.some(t=>t(new URL(s))))return!0;return!1}function no(t,e){return"data"===t?.type?t:"skip"===t?.type?e??null:null}function ro({error:t,url:e,route:n,params:r}){return{type:"loaded",state:{error:t,url:e,route:n,params:r,branch:[]},props:{page:yo(la),constructors:[]}}}async function ao({id:t,invalidating:e,url:n,params:r,route:a,preload:o}){if(Ra?.id===t)return Ba.delete(Ra.token),Ra.promise;const{errors:s,layouts:i,leaf:l}=a,u=[...i,l];s.forEach(t=>t?.().catch(()=>{})),u.forEach(t=>t?.[1]().catch(()=>{}));let c=null;const f=!!Na.url&&t!==lo(Na.url),d=!!Na.route&&a.id!==Na.route.id,p=function(t,e){if(!t)return new Set(e.searchParams.keys());const n=new Set([...t.searchParams.keys(),...e.searchParams.keys()]);for(const r of n){const a=t.searchParams.getAll(r),o=e.searchParams.getAll(r);a.every(t=>o.includes(t))&&o.every(t=>a.includes(t))&&n.delete(r)}return n}(Na.url,n);let v=!1;const h=u.map((t,e)=>{const n=Na.branch[e],a=!!t?.[0]&&(n?.loader!==t[1]||eo(v,d,f,p,n.server?.uses,r));return a&&(v=!0),a});if(h.some(Boolean)){try{c=await ho(n,h)}catch(b){const e=await po(b,{url:n,params:r,route:{id:t}});return Ba.has(o)?ro({error:e,url:n,params:r,route:a}):so({status:ia(b),error:e,url:n,route:a})}if("redirect"===c.type)return c}const m=c?.nodes;let g=!1;const y=u.map(async(t,e)=>{if(!t)return;const o=Na.branch[e],s=m?.[e];if(!(s&&"skip"!==s.type||t[1]!==o?.loader||eo(g,d,f,p,o.universal?.uses,r)))return o;if(g=!0,"error"===s?.type)throw s;return Za({loader:t[1],url:n,params:r,route:a,parent:async()=>{const t={};for(let n=0;n<e;n+=1)Object.assign(t,(await y[n])?.data);return t},server_data_node:no(void 0===s&&t[0]?{type:"skip"}:s??null,t[0]?o?.server:void 0)})});for(const x of y)x.catch(()=>{});const w=[];for(let x=0;x<u.length;x+=1)if(u[x])try{w.push(await y[x])}catch(_){if(_ instanceof oa)return{type:"redirect",location:_.location};if(Ba.has(o))return ro({error:await po(_,{params:r,url:n,route:{id:a.id}}),url:n,params:r,route:a});let t,e=ia(_);if(m?.includes(_))e=_.status??e,t=_.error;else if(_ instanceof aa)t=_.body;else{if(await va.updated.check())return await ga(),await ma(n);t=await po(_,{params:r,url:n,route:{id:a.id}})}const i=await oo(x,w,s);return i?Qa({url:n,params:r,branch:w.slice(0,i.idx).concat(i.node),status:e,error:t,route:a}):await fo(n,{id:a.id},t,e)}else w.push(void 0);return Qa({url:n,params:r,branch:w,status:200,error:null,route:a,form:e?void 0:null})}async function oo(t,e,n){for(;t--;)if(n[t]){let r=t;for(;!e[r];)r-=1;try{return{idx:r+1,node:{node:await n[t](),loader:n[t],data:{},server:null,universal:null}}}catch{continue}}}async function so({status:t,error:e,url:n,route:r}){const a={};let o=null;if(0===Sa.server_loads[0])try{const t=await ho(n,[!0]);if("data"!==t.type||t.nodes[0]&&"data"!==t.nodes[0].type)throw 0;o=t.nodes[0]??null}catch{(n.origin!==Wr||n.pathname!==location.pathname||Ia)&&await ma(n)}try{const s=await Za({loader:ba,url:n,params:a,route:r,parent:()=>Promise.resolve({}),server_data_node:no(o)});return Qa({url:n,params:a,branch:[s,{node:await _a(),loader:_a,universal:null,server:null,data:null}],status:t,error:e,route:null})}catch(s){if(s instanceof oa)return Ha(new URL(s.location,location.href),{},0);throw s}}async function io(t,e){if(t&&!ta(t,Cr,Sa.hash)){const n=await async function(t){const e=t.href;if(Aa.has(e))return Aa.get(e);let n;try{const r=(async()=>{let e=await Sa.hooks.reroute({url:new URL(t),fetch:async(e,n)=>to(e,n,t).promise})??t;if("string"==typeof e){const n=new URL(t);Sa.hash?n.hash=e:n.pathname=e,e=n}return e})();Aa.set(e,r),n=await r}catch(r){return void Aa.delete(e)}return n}(t);if(!n)return;const r=function(t){return e=Sa.hash?t.hash.replace(/^#/,"").replace(/[?#].+/,""):t.pathname.slice(Cr.length),e.split("%25").map(decodeURI).join("%25")||"/";var e}(n);for(const a of wa){const n=a.exec(r);if(n)return{id:lo(t),invalidating:e,route:a,params:wr(n),url:t}}}}function lo(t){return(Sa.hash?t.hash.replace(/^#/,""):t.pathname)+t.search}function uo({url:t,type:e,intent:n,delta:r}){let a=!1;const o=go(Na,n,t,e);void 0!==r&&(o.navigation.delta=r);const s={...o.navigation,cancel:()=>{a=!0,o.reject(new Error("navigation cancelled"))}};return Ma||Pa.forEach(t=>t(s)),a?null:o}async function co({type:t,url:e,popped:n,keepfocus:r,noscroll:a,replace_state:o,state:s={},redirect_count:i=0,nav_token:l={},accept:u=ya,block:c=ya}){const f=Ua;Ua=l;const d=await io(e,!1),p="enter"===t?go(Na,d,e,t):uo({url:e,type:t,delta:n?.delta,intent:d});if(!p)return c(),void(Ua===l&&(Ua=f));const v=Ca,h=Ta;u(),Ma=!0,qa&&"enter"!==p.navigation.type&&va.navigating.set(ua.current=p.navigation);let m=d&&await ao(d);if(!m){if(ta(e,Cr,Sa.hash))return await ma(e);m=await fo(e,{id:null},await po(new sa(404,"Not Found",`Not found: ${e.pathname}`),{url:e,params:{},route:{id:null}}),404)}if(e=d?.url||e,Ua!==l)return p.reject(new Error("navigation aborted")),!1;if("redirect"===m.type){if(!(i>=20))return await Ha(new URL(m.location,e).href,{},i+1,l),!1;m=await so({status:500,error:await po(new Error("Redirect loop"),{url:e,params:{},route:{id:null}}),url:e,route:{id:null}})}else if(m.props.page.status>=400){await va.updated.check()&&(await ga(),await ma(e))}if(Ea.length=0,Fa=!1,ha(v),Ja(h),m.props.page.url.pathname!==e.pathname&&(e.pathname=m.props.page.url.pathname),s=n?n.state:s,!n){const t=o?0:1,n={[Dr]:Ca+=t,[Mr]:Ta+=t,[Ir]:s};(o?history.replaceState:history.pushState).call(history,n,"",e),o||function(t,e){let n=t+1;for(;da[n];)delete da[n],n+=1;for(n=e+1;pa[n];)delete pa[n],n+=1}(Ca,Ta)}if(Ra=null,m.props.page.state=s,qa){Na=m.state,m.props.page&&(m.props.page.url=e);const t=(await Promise.all(Array.from(La,t=>t(p.navigation)))).filter(t=>"function"==typeof t);if(t.length>0){let e=function(){t.forEach(t=>{Oa.delete(t)})};t.push(e),t.forEach(t=>{Oa.add(t)})}ja.$set(m.props),g=m.props.page,Object.assign(la,g),Wa=!0}else Ya(m,ka,!1);var g;const{activeElement:y}=document;await De();const w=n?n.scroll:a?Br():null;if(Da){const t=e.hash&&document.getElementById(decodeURIComponent(Sa.hash?e.hash.split("#")[2]??"":e.hash.slice(1)));w?scrollTo(w.x,w.y):t?t.scrollIntoView():scrollTo(0,0)}const b=document.activeElement!==y&&document.activeElement!==document.body;r||b||function(){const t=document.querySelector("[autofocus]");if(t)t.focus();else{const t=document.body,e=t.getAttribute("tabindex");t.tabIndex=-1,t.focus({preventScroll:!0,focusVisible:!1}),null!==e?t.setAttribute("tabindex",e):t.removeAttribute("tabindex");const n=getSelection();if(n&&"None"!==n.type){const t=[];for(let e=0;e<n.rangeCount;e+=1)t.push(n.getRangeAt(e));setTimeout(()=>{if(n.rangeCount===t.length){for(let e=0;e<n.rangeCount;e+=1){const r=t[e],a=n.getRangeAt(e);if(r.commonAncestorContainer!==a.commonAncestorContainer||r.startContainer!==a.startContainer||r.endContainer!==a.endContainer||r.startOffset!==a.startOffset||r.endOffset!==a.endOffset)return}n.removeAllRanges()}})}}}(),Da=!0,m.props.page&&Object.assign(la,m.props.page),Ma=!1,"popstate"===t&&Ka(Ta),p.fulfil(void 0),Oa.forEach(t=>t(p.navigation)),va.navigating.set(ua.current=null)}async function fo(t,e,n,r){return t.origin!==Wr||t.pathname!==location.pathname||Ia?await ma(t):await so({status:r,error:n,url:t,route:e})}function po(t,e){if(t instanceof aa)return t.body;const n=ia(t),r=function(t){return t instanceof sa?t.text:"Internal Error"}(t);return Sa.hooks.handleError({error:t,event:e,status:n,message:r})??{message:r}}function vo(t){if("function"==typeof t)Ea.push(t);else{const{href:e}=new URL(t,location.href);Ea.push(t=>t.href===e)}}async function ho(t,n){const r=new URL(t);var a;r.pathname=(a=t.pathname).endsWith(".html")?a.replace(/\.html$/,".html__data.json"):a.replace(/\/$/,"")+"/__data.json",t.pathname.endsWith("/")&&r.searchParams.append("x-sveltekit-trailing-slash","1"),r.searchParams.append("x-sveltekit-invalidated",n.map(t=>t?"1":"0").join(""));const o=window.fetch,s=await o(r.href,{});if(!s.ok){let t;throw s.headers.get("content-type")?.includes("application/json")?t=await s.json():404===s.status?t="Not Found":500===s.status&&(t="Internal Error"),new aa(s.status,t)}return new Promise(async t=>{const n=new Map,r=s.body.getReader(),a=new TextDecoder;function o(t){return e(t,{...Sa.decoders,Promise:t=>new Promise((e,r)=>{n.set(t,{fulfil:e,reject:r})})})}let i="";for(;;){const{done:e,value:s}=await r.read();if(e&&!i)break;for(i+=!s&&i?"\n":a.decode(s,{stream:!0});;){const e=i.indexOf("\n");if(-1===e)break;const r=JSON.parse(i.slice(0,e));if(i=i.slice(e+1),"redirect"===r.type)return t(r);if("data"===r.type)r.nodes?.forEach(t=>{"data"===t?.type&&(t.uses=mo(t.uses),t.data=o(t.data))}),t(r);else if("chunk"===r.type){const{id:t,data:e,error:a}=r,s=n.get(t);n.delete(t),a?s.reject(o(a)):s.fulfil(o(e))}}}})}function mo(t){return{dependencies:new Set(t?.dependencies??[]),params:new Set(t?.params??[]),parent:!!t?.parent,route:!!t?.route,url:!!t?.url,search_params:new Set(t?.search_params??[])}}function go(t,e,n,r){let a,o;const s=new Promise((t,e)=>{a=t,o=e});s.catch(()=>{});return{navigation:{from:{params:t.params,route:{id:t.route?.id??null},url:t.url},to:n&&{params:e?.params??null,route:{id:e?.route?.id??null},url:n},willUnload:!e,type:r,complete:s},fulfil:a,reject:o}}function yo(t){return{data:t.data,error:t.error,form:t.form,params:t.params,route:t.route,state:t.state,status:t.status,url:t.url}}function wo(t){const e=new URL(t);return e.hash=decodeURIComponent(t.hash),e}"undefined"!=typeof window&&((window.__svelte??={}).v??=new Set).add("5"),U=!0;const bo=t=>t;function _o(t){const e=t-1;return e*e*e+1}function xo(t){const e="string"==typeof t&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return e?[parseFloat(e[1]),e[2]||"px"]:[t,"px"]}function ko(t,{delay:e=0,duration:n=400,easing:r=bo}={}){const a=+getComputedStyle(t).opacity;return{delay:e,duration:n,easing:r,css:t=>"opacity: "+t*a}}function So(t,{delay:e=0,duration:n=400,easing:r=_o,x:a=0,y:o=0,opacity:s=0}={}){const i=getComputedStyle(t),l=+i.opacity,u="none"===i.transform?"":i.transform,c=l*(1-s),[f,d]=xo(a),[p,v]=xo(o);return{delay:e,duration:n,easing:r,css:(t,e)=>`\n\t\t\ttransform: ${u} translate(${(1-t)*f}${d}, ${(1-t)*p}${v});\n\t\t\topacity: ${l-c*e}`}}function Eo(t,{delay:e=0,duration:n=400,easing:r=_o,start:a=0,opacity:o=0}={}){const s=getComputedStyle(t),i=+s.opacity,l="none"===s.transform?"":s.transform,u=1-a,c=i*(1-o);return{delay:e,duration:n,easing:r,css:(t,e)=>`\n\t\t\ttransform: ${l} scale(${1-u*e});\n\t\t\topacity: ${i-c*e}\n\t\t`}}const $o={get error(){return la.error},get status(){return la.status}};va.updated.check;const Ro=$o;var Ao=sn("<h1> </h1> <p> </p>",1);function Po(t,e){B(e,!1),Zn();var n=Ao(),r=kt(n),a=xt(r,!0);ct(r);var o=St(r,2),s=xt(o,!0);ct(o),Mt(()=>{pn(a,Ro.status),pn(s,Ro.error?.message)}),fn(t,n),z()}export{pt as $,ft as A,ar as B,or as C,sr as D,Po as E,cn as F,kt as G,Rn as H,Xn as I,ln as J,rn as K,vt as L,nt as M,_n as N,bn as O,Ut as P,Tt as Q,Y as R,De as S,An as T,Rt as U,un as V,vr as W,fr as X,ur as Y,$n as Z,Fn as _,pr as a,So as a0,er as a1,zn as a2,Be as a3,Tn as a4,Z as a5,cr as a6,Eo as a7,Yn as a8,ko as a9,Ge as aa,Qn as ab,za as ac,ea as ad,gr as b,yr as c,qt as d,xt as e,wn as f,Me as g,St as h,Zn as i,Mt as j,At as k,It as l,Q as m,Ln as n,mr as o,B as p,Un as q,ct as r,tt as s,sn as t,tn as u,fn as v,Gn as w,z as x,On as y,pn as z};
