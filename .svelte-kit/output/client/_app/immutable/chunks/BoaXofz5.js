import{B as e,C as a,D as n}from"./B1xmz3ZD.js";const t=e({id:null,name:null,avatar:null,isLoggedIn:!1}),s=e({theme:function(){if("undefined"!=typeof window){const e=localStorage.getItem("theme");if(e)return e;if(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches)return"dark"}return"light"}(),language:"zh-CN",notifications:!0,fontSize:"normal",reduceMotion:!1});function i(){const e=n(s);"undefined"!=typeof window&&"dark"===e.theme&&document.documentElement.classList.add("dark")}const o=e([]);function u(e,a="info",n=3e3){const t=Date.now();return o.update(n=>[{id:t,message:e,type:a,timestamp:new Date},...n]),n&&setTimeout(()=>{r(t)},n),t}function r(e){o.update(a=>a.filter(a=>a.id!==e))}const l=e([]);function d(e){const a=Date.now(),n={id:a,message:e.message||"未知错误",code:e.code||"UNKNOWN_ERROR",timestamp:new Date,details:e.details||null,stack:e.stack||null};return l.update(e=>[n,...e]),u(`错误: ${n.message}`,"error",5e3),a}a(t,e=>e.isLoggedIn),a(s,e=>"dark"===e.theme);const c=e([]),m=e(""),f=e(null);function p(e){console.log("设置选中菜单项:",e),f.set(e)}const g=e([]),h=e(!1);a(f,e=>e?e.mcode:null);const k={adminUids:["********","1","9753360","921302","********","********","8454468","********","********","********"],defaultUid:null,uploadDomain:"https://gktempupload.qiaoxuesi.com",apiBaseUrl:"https://api.gankao.com/api-yunying",currentUser:"https://www.gankao.com/newaccount/currentUser",difyApiUrl:"https://aigate2.gankao.com/v1/chat-messages",apiProxyUrl:""},w=e({uid:null,username:null,nickname:null,avatar:null,role:"user"}),U=a(w,e=>!!e.uid&&k.adminUids.includes(e.uid));function y(e){w.update(a=>({...a,uid:e.uid||null,username:e.username||a.username,nickname:e.nickname||e.username||a.nickname,avatar:e.avatar||a.avatar,role:e.uid&&k.adminUids.includes(e.uid)?"admin":"user"}))}function v(){w.set({uid:null,username:null,nickname:null,avatar:null,role:"user"})}const D=Object.freeze(Object.defineProperty({__proto__:null,clearCurrentUser:v,isAdmin:U,setUserInfo:y,user:w},Symbol.toStringTag,{value:"Module"}));export{d as a,f as b,k as c,U as d,y as e,v as f,u as g,i as h,h as i,D as j,g as l,c as m,o as n,m as p,r,p as s,w as u};
