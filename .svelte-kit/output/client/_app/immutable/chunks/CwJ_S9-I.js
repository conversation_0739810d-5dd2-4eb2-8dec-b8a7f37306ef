const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./BoaXofz5.js","./B1xmz3ZD.js","./roc3oT9G.js","./t5uZ5HS4.js"])))=>i.map(i=>d[i]);
import{_ as e}from"./DuTLMzPI.js";import{u as t,c as n,l as s}from"./BoaXofz5.js";import{D as o,B as a}from"./B1xmz3ZD.js";import{h as r}from"./gyA2PHTf.js";let D,P,k,h,S,g,p,c,f,u,E,M,T,w,$,_;let __tla=(async()=>{let i=null;c=async function(){const s=o(t);if(s&&s.uid)return s;if(i)return i;try{const t=await fetch(`${n.currentUser}`,{method:"GET",headers:{"Content-Type":"application/json",Cookie:document.cookie},credentials:"include"});if(!t.ok)return console.warn(`获取用户信息失败: ${t.status} - 可能是未登录状态`),null;const s=await t.json();return 200===s.status&&s.data?(i={uid:s.data.user_id||n.defaultUid,username:s.data.real_name||null,nickname:s.data.real_name||null,avatar:s.data.user_type_name||null},e(async()=>{const{setUserInfo:e}=await import("./BoaXofz5.js").then(e=>e.j);return{setUserInfo:e}},__vite__mapDeps([0,1,2,3]),import.meta.url).then(({setUserInfo:e})=>{e(i)}),i):(console.info("未获取到用户信息或用户未登录:",s),null)}catch(a){return console.error("获取用户信息出错:",a),null}};u=async function(){const e=o(t);if(e&&e.uid)return n.adminUids.includes(e.uid);const s=await async function(){const e=o(t);if(e&&e.uid)return e.uid;const n=await c();return n?n.uid:null}();return s&&n.adminUids.includes(s)};const l=n.apiBaseUrl;function d(e){const{menuTitle:t,menuList:n,partnerName:s}=e;return{menuTitle:t,partnerName:s,menuItems:n.map(e=>m(e))}}function m(e){const t={id:e.zoneCode,name:e.name,link:null,mcode:e.zoneCode,children:[]};return e.children&&e.children.length>0&&(t.children=e.children.map(e=>e.zoneCode?m(e):{id:e.id.toString(),name:e.name,link:e.link,mcode:e.mcode})),t}h=async function(e=null,t){const n=await async function(e=null,t=null){try{let n="mcodes";e&&(n=`${e}`);const s=await fetch(`${l}/GKZoneModules/getAppModuleListByPartner`,{method:"POST",headers:{"Content-Type":"application/json",Cookie:"student=11188475,a7e7f2ccac18db3ccf2ce39ca20a8947,nihao"},body:JSON.stringify({cp:n,partnerEName:t||void 0})});if(!s.ok)throw new Error(`获取菜单列表失败: ${s.status}`);const o=await s.json();return{menuList:[{name:"所有功能",zoneCode:"all",zone_id:0,children:o.result.data.reduce((e,t,n)=>(e.push({id:n+1,link:t.schema,mcode:t.mcode,name:t["名称"]}),e),[])}],partnerName:o.result.partnerName}}catch(n){throw console.error("获取菜单列表出错:",n),n}}(e,t);return d(n)};p=function(e,t){if(!t||!t.length)return{prev:null,next:null,currentIndex:-1};const n=t.findIndex(t=>t.mcode===e);if(-1===n)return{prev:null,next:null,currentIndex:-1};const s=n>0?n-1:t.length-1,o=(n+1)%t.length;return{prev:{mcode:t[s].mcode,title:t[s].name},next:{mcode:t[o].mcode,title:t[o].name},currentIndex:n}};f=function(){return{prev:{title:"",path:"",mcode:""},next:{title:"",path:"",mcode:""}}};g=async function(e){if(!e)return null;let t=null;if("undefined"!=typeof window){t=new URLSearchParams(window.location.search).get("partnerEName")||null}const n=await async function(e,t=null){try{const n=await fetch(`${l}/GKZoneModules/getBannerAndContent`,{method:"POST",headers:{"Content-Type":"application/json",Cookie:"student=11188475,a7e7f2ccac18db3ccf2ce39ca20a8947,nihao"},body:JSON.stringify({mcode:e,partnerEName:t||void 0})});if(!n.ok)throw new Error(`获取模块详情失败: ${n.status}`);return(await n.json()).result.data}catch(n){throw console.error("获取模块详情出错:",n),n}}(e,t);return n?function(e,t){const n=o(s),{prev:a,next:r}=p(t,n),i=e.app_module_banners.filter(e=>e.image).map(e=>e.image);return{mcode:t,description:e.content||"",images:i,moduleId:e.id,schema:e.schema,name:e.name,prevPage:a,nextPage:r}}(n,e):null};let y,I,C,b;w=async(e,t)=>{try{if(!t.moduleId)throw new Error("保存失败：缺少模块ID");const e=t.moduleId,n=t.description||"",s=t.carouselItems?t.carouselItems.map(e=>({type:1,image:e.image})):[],o=await async function(e,t,n){try{const s=await fetch(`${l}/GKZoneModules/editBannerAndContent`,{method:"POST",headers:{"Content-Type":"application/json",Cookie:"student=11188475,a7e7f2ccac18db3ccf2ce39ca20a8947,nihao"},body:JSON.stringify({module_id:e,content:t,images:n})});if(!s.ok)throw new Error(`编辑模块详情失败: ${s.status}`);return await s.json()}catch(s){throw console.error("编辑模块详情出错:",s),s}}(e,n,s);return{success:!0,message:"保存成功",data:t,result:o}}catch(n){throw console.error(`保存模块数据出错: ${n.message}`),n}};y=a(!1);T=a(!1);k=a("");I=a(new Set);S=a([]);C=a({scanStartTime:0,hasFoundImages:!1,scanFailCount:0,isRequestPending:!1,hasSuccessfulResponse:!1,scanTimer:null,scanUUID:"",scanImageName:"",isTimedOut:!1,extendedScanTimer:null});b=3e4;P=function(e){const{uuid:t,username:s}=e;return $(),x(),C.update(e=>({...e,scanUUID:t,scanImageName:s||"扫描用户",scanStartTime:Date.now(),hasFoundImages:!1,scanFailCount:0,isRequestPending:!1,hasSuccessfulResponse:!1,isTimedOut:!1})),y.set(!0),T.set(!0),new Promise((e,t)=>{const s=setInterval(()=>{(async function(){const e=o(C);if(e.isRequestPending)return console.log("已有请求正在进行中，等待请求完成..."),[];try{if(!e.scanUUID)return j(),[];if(e.hasFoundImages)return console.log("已找到图片，停止轮询"),j(),[];if(Date.now()-e.scanStartTime>b)return console.log("超过扩展扫描时间，停止扫描"),j(),x(),[];C.update(e=>({...e,isRequestPending:!0}));const t=Date.now();console.log(`发送请求：${n.uploadDomain}/${e.scanUUID}.json?_t=${t}`);const s=await fetch(`${n.uploadDomain}/${e.scanUUID}.json?_t=${t}`);if(C.update(e=>({...e,isRequestPending:!1})),s.ok){C.update(e=>({...e,hasSuccessfulResponse:!0,scanFailCount:0}));const e=await s.json(),t=r(),n=e.filter(e=>r(e.uploadTime).clone().add(30,"minutes").isAfter(t));if(n.length){const e=o(I),t=n.filter(t=>!e.has(t.url));if(t.length>0){I.update(e=>(t.forEach(t=>e.add(t.url)),e));const e=t.map(e=>({id:Date.now()+Math.random(),image:e.url,alt:`扫描图片 ${Date.now()}`}));C.update(e=>({...e,hasFoundImages:!0}));const n=o(C);return n.isTimedOut&&n.extendedScanTimer?(clearTimeout(n.extendedScanTimer),C.update(e=>({...e,extendedScanTimer:null})),k.set(""),S.set(e),[]):(T.set(!1),y.set(!1),e)}n.length>0&&(C.update(e=>({...e,hasFoundImages:!0})),j(),T.set(!1),y.set(!1))}return[]}throw new Error("请求失败，状态码: "+s.status)}catch(t){C.update(e=>({...e,isRequestPending:!1})),console.error("检查文件失败:",t),k.set("检查文件失败: "+t.message),C.update(e=>({...e,scanFailCount:e.scanFailCount+1}));const e=o(C);if(console.log(`扫描失败次数: ${e.scanFailCount}/3`),e.scanFailCount>=3){const t=e.hasSuccessfulResponse?"扫描失败，请检查网络连接后重试":"当前网络缓慢，您可以继续等待或者重新扫描！";throw y.set(!1),T.set(!1),new Error(t)}return[]}})().then(t=>{t&&t.length>0&&(j(),e(t))}).catch(e=>{o(C).scanFailCount>=3&&(j(),t(e))})},3e3);C.update(e=>({...e,scanTimer:s}));const a=setTimeout(()=>{const e=o(C);if(!e.hasFoundImages){C.update(e=>({...e,isTimedOut:!0}));const n=e.hasSuccessfulResponse?"未在指定时间内找到图片，请重试或检查上传设备":"当前网络缓慢，您可以继续等待或者重新扫描！";k.set(n);const s=setTimeout(()=>{o(C).hasFoundImages||(j(),x(),t(new Error(n)))},2e4);C.update(e=>({...e,extendedScanTimer:s}))}},1e4);o(C).scanTimer,C.update(e=>({...e,additionalTimers:[...e.additionalTimers||[],a]}))})};$=function(){j(),x()};function j(){const e=o(C);e.scanTimer&&clearInterval(e.scanTimer),e.extendedScanTimer&&clearTimeout(e.extendedScanTimer),e.additionalTimers&&e.additionalTimers.length>0&&e.additionalTimers.forEach(e=>{e&&clearTimeout(e)}),C.update(e=>({...e,scanTimer:null,extendedScanTimer:null,additionalTimers:[]}))}function x(){j(),y.set(!1),T.set(!1),k.set(""),I.set(new Set),S.set([]),C.set({scanStartTime:0,hasFoundImages:!1,scanFailCount:0,isRequestPending:!1,hasSuccessfulResponse:!1,isTimedOut:!1,scanUUID:"",scanImageName:"",scanTimer:null,extendedScanTimer:null,additionalTimers:[]})}let U;U=n.apiBaseUrl;_=a({total:0,current:0,success:0,failed:0,status:"idle",errorMessage:""});D={qiniuToken:"",async getQiniuToken(e=3){if(0===e)throw new Error("获取七牛云token失败");try{const e=await fetch(`${U}/GkCommonMindMap/getQiniuToken`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({bucket:"static"})}),t=await e.json();if(t&&t.result&&t.result.data)return this.qiniuToken=t.result.data,console.log("获取七牛token成功"),t.result.data;throw new Error("获取七牛token返回格式错误")}catch(t){return console.error("获取七牛token失败",t),this.getQiniuToken(e-1)}},async fetchImageAsBlob(e){try{const t=await fetch(e);return await t.blob()}catch(t){throw console.error("获取图片失败:",t),t}},async uploadImageToQiniu(e,t,n=3){if(0===n)throw new Error("上传图片到七牛云失败，已达到最大重试次数");const s=t||`image_${Date.now()}_${Math.floor(1e3*Math.random())}.jpg`;try{const t=new FormData;t.append("key",s),t.append("token",this.qiniuToken);const n=new File([e],s,{type:e.type||"image/jpeg"});t.append("file",n),console.log("七牛云上传参数:",{key:s,token:this.qiniuToken.substring(0,20)+"...",fileName:n.name,fileSize:n.size,fileType:n.type});const o=await fetch("https://up.qbox.me",{method:"POST",body:t});if(!o.ok)throw new Error(`七牛云响应错误: ${o.status} ${o.statusText}`);const a=await o.json();if(!a.key)throw console.error("七牛云返回结果无key:",a),new Error("七牛云返回结果无key");return console.log("七牛云上传成功, 返回结果:",a),"https://static.qiaoxuesi.com/"+a.key}catch(o){throw console.error("上传图片到七牛云失败:",o),o}},async processImages(e){const t=[];let n=0;for(const c of e)(c.image.startsWith("blob:")||c.image.startsWith("data:")||!0===c.isTemporary)&&n++;if(_.set({total:n,current:0,success:0,failed:0,status:n>0?"uploading":"completed",errorMessage:""}),0===n)return e;try{this.qiniuToken||await this.getQiniuToken()}catch(r){return console.error("获取七牛云token失败，无法继续上传:",r),_.update(e=>({...e,status:"error",errorMessage:"获取上传授权失败，请重试"})),e}const s=async()=>{try{return this.qiniuToken="",await this.getQiniuToken(),!0}catch(r){return console.error("刷新七牛云token失败:",r),!1}};let o=[];for(const c of e)if(c.image.startsWith("blob:")||c.image.startsWith("data:")||!0===c.isTemporary)try{_.update(e=>({...e,current:e.current+1}));const e=await this.fetchImageAsBlob(c.image);let n=null,a=2;for(;a>=0&&!n;)try{n=await this.uploadImageToQiniu(e)}catch(i){if(console.error(`上传失败，剩余重试次数: ${a}`,i),a>0){if(!(await s()))break}a--}n?(t.push({...c,image:n,isTemporary:!1}),console.log("图片上传成功:",n),_.update(e=>({...e,success:e.success+1}))):(console.error("图片上传失败，已从结果中移除:",c),o.push(c),_.update(e=>({...e,failed:e.failed+1})))}catch(r){console.error("处理图片失败:",r),o.push(c),_.update(e=>({...e,failed:e.failed+1}))}else t.push(c);let a="";return o.length>0&&(a=`有 ${o.length} 张图片上传失败，已从结果中移除`),_.update(e=>({...e,status:o.length>0?"completed_with_errors":"completed",errorMessage:a})),t},resetProgress(){_.set({total:0,current:0,success:0,failed:0,status:"idle",errorMessage:""})}};E=a({isProcessing:!1,streamingContent:"",isComplete:!1,error:null});let O="",F="";function q(e){if(!e)return"";const t=O+e;if(t.includes("<think>")&&!t.includes("</think>"))return O=t,"";if(t.includes("<think>")&&t.includes("</think>")){console.log("发现完整的<think></think>标签对，移除标签内容");const e=t.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/^\n+/,"");return console.log("清理后内容长度:",e.length),O="",N(e)}return O&&!t.includes("<think>")?O.includes("<think>")?(console.log("之前有未闭合的<think>，继续缓存等待</think>"),O=t,""):(console.log("没有标签，输出所有内容"),O="",N(t)):(O&&(console.log("清除之前的缓存内容，输出当前内容"),O=""),N(e))}function N(e){if(!e)return"";const t=F+e;if(F||t.startsWith(">")){if(t.includes("\n\n")){console.log('发现以">"开头的思考内容并有结束标志');const e=t.indexOf("\n\n"),n=t.substring(0,e),s=t.substring(e+2);return n.split("\n").every(e=>e.trim().startsWith(">")||""===e.trim())?(console.log("移除思考内容，保留后续内容"),F="",N(s)):(console.log("不是所有行都是思考内容，保留整个文本"),F="",t)}return console.log("思考内容未结束，继续缓存"),F=t,""}return e}function R(e){if(!e)return"";const t=e.replace(/<think>[\s\S]*?<\/think>/g,"").split("\n");let n=[],s=!1;for(let a=0;a<t.length;a++){const e=t[a];e.trim().startsWith(">")?s=!0:""===e.trim()&&s?(a+1>=t.length||!t[a+1].trim().startsWith(">"))&&(s=!1):s||n.push(e)}let o=n.join("\n").replace(/^\n+/,"");return o=function(e){if(!e)return"";const t=e.split("\n"),n=t.map(e=>{const t=e.trim();return t.startsWith("**")&&(e=e.replace(/^\s*\*\*/,"")),t.endsWith("**")&&(e=e.replace(/\*\*\s*$/,"")),e});return n.join("\n")}(o),o}const v=n.difyApiUrl;M=function(e,t,n=""){return E.update(e=>({...e,isProcessing:!0,streamingContent:"",isComplete:!1,error:null})),O="",F="",async function(){try{console.log("开始连接Dify服务...");const o={query:"请根据修改需求优化当前内容",response_mode:"streaming",user:`gankaoUID-${n}`||"user",inputs:{current_content:e||"（特殊情况：产品助理漏写了这个模块）",modification_request:t||""}};console.log("请求数据:",JSON.stringify(o));const a=await fetch(v,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer app-9q3ma80jIMr7YIIrJTYJisbg",Accept:"text/event-stream"},body:JSON.stringify(o)});if(!a.ok)throw new Error(`API响应错误: ${a.status}`);const r=a.body.getReader(),i=new TextDecoder;async function c(){try{console.log("开始处理流式响应...");let t=!1;for(;;){const{done:n,value:s}=await r.read();if(n){t||(console.log("流结束，但未收到end事件，手动标记完成"),(O||F)&&(console.log("处理剩余的缓存内容"),O="",F=""),E.update(e=>{const t=R(e.streamingContent);return{...e,isProcessing:!1,isComplete:!0,streamingContent:t}}));break}const o=i.decode(s,{stream:!0}).split("\n\n");for(const a of o)if(a.startsWith("data: "))try{const e=JSON.parse(a.substring(6)),n=q(e.answer||"");n&&E.update(e=>({...e,streamingContent:e.streamingContent+n})),"end"===e.event&&(console.log("收到end事件，标记完成"),t=!0,O="",F="",E.update(e=>{const t=R(e.streamingContent);return{...e,isProcessing:!1,isComplete:!0,streamingContent:t}}))}catch(e){console.warn("解析数据出错:",e,a)}}}catch(t){console.error("读取流出错:",t),E.update(e=>({...e,isProcessing:!1,error:"流处理中断，请重试"}))}}return c(),()=>{r.cancel(),E.update(e=>({...e,isProcessing:!1}))}}catch(s){return console.error("创建流连接失败:",s),E.update(e=>({...e,isProcessing:!1,error:s.message})),()=>{}}}()}})();export{D as I,P as a,k as b,h as c,S as d,g as e,p as f,c as g,f as h,u as i,E as j,M as k,T as l,w as m,$ as s,_ as u,__tla};