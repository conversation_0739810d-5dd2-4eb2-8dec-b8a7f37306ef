import{g as t}from"./-7hijANM.js";var n,r={exports:{}};var e,i,o,a=(n||(n=1,i=r.exports,o=function(t){function n(t,n){var r,e;for(r=-1,e=t.length;++r<e;)n(t[r],r,t)}function r(t,r){var e;return e=Array(t.length),n(t,function(t,n,i){e[n]=r(t,n,i)}),e}function e(t,r,e){return n(t,function(t,n,i){e=r(t,n,i)}),e}function i(t,r){var e,i,o;return o=this._matrix=[],t==r?this.distance=0:""==t?this.distance=r.length:""==r?this.distance=t.length:(e=[0],n(t,function(t,n){n++,e[n]=n}),o[0]=e,n(r,function(a,c){i=[++c],n(t,function(n,o){o++,t.charAt(o-1)==r.charAt(c-1)?i[o]=e[o-1]:i[o]=Math.min(e[o]+1,i[o-1]+1,e[o-1]+1)}),e=i,o[o.length]=e}),this.distance=i[i.length-1])}return i.prototype.toString=i.prototype.inspect=function(t){var n,i,o,a;for(i=e(n=this.getMatrix(),function(t,n){return Math.max(t,e(n,Math.max,0))},0),o=Array((i+"").length).join(" "),a=[];a.length<(n[0]&&n[0].length||0);)a[a.length]=Array(o.length+1).join("-");return a=a.join("-+")+"-",r(n,function(t){return r(t,function(t){return(o+t).slice(-o.length)}).join(" |")+" "}).join("\n"+a+"\n")},i.prototype.getMatrix=function(){return this._matrix.slice()},i.prototype.valueOf=function(){return this.distance},i},(e=r)&&e.exports?e.exports=o():i.Levenshtein=o()),r.exports);const c=t(a);export{c as L};
