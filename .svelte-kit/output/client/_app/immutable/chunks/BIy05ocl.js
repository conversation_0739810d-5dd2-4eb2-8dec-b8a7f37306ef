import{p as B,o as C,b as F,i as G,F as f,u as m,G as h,f as H,v as a,x as M,$ as P,g as d,m as p,s as t,t as $,h as x,e as b,r as g,j as q,z as A,H as I}from"./CLjOhJ05.js";import{a as i}from"./zG-a9-JQ.js";var J=$('<div class="error-boundary p-6 bg-red-50 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-lg shadow-md"><div class="flex items-center mb-4"><div class="flex-shrink-0 text-red-600 dark:text-red-400"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg></div> <h3 class="ml-2 text-lg font-semibold text-red-800 dark:text-red-200">\u53D1\u751F\u9519\u8BEF</h3></div> <div class="mb-4 text-red-700 dark:text-red-300"> </div> <button class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors">\u91CD\u8BD5</button></div>');function K(k,l){B(l,!1);let e=p(null),o=p(!1);function v(r){t(o,!0),t(e,r.error||r.reason||new Error("\u53D1\u751F\u672A\u77E5\u9519\u8BEF")),i(d(e)),r.preventDefault()}function u(r){t(o,!0),t(e,r.reason||new Error("\u53D1\u751F\u672A\u5904\u7406\u7684Promise\u9519\u8BEF")),i(d(e)),r.preventDefault()}function E(){t(o,!1),t(e,null)}C(()=>{typeof window!="undefined"&&(window.addEventListener("error",v),window.addEventListener("unhandledrejection",u))}),F(()=>{typeof window!="undefined"&&(window.removeEventListener("error",v),window.removeEventListener("unhandledrejection",u))}),G();var w=f();m("error",P,function(r){t(o,!0),t(e,r.error||new Error("\u53D1\u751F\u672A\u77E5\u9519\u8BEF")),i(d(e)),r.preventDefault()});var L=h(w),y=r=>{var n=J(),s=x(b(n),2),D=b(s,!0);g(s);var z=x(s,2);g(n),q(()=>{var c;return A(D,((c=d(e))==null?void 0:c.message)||"\u672A\u77E5\u9519\u8BEF")}),m("click",z,E),a(r,n)},j=r=>{var n=f(),s=h(n);I(s,l,"default",{}),a(r,n)};H(L,r=>{d(o)?r(y):r(j,!1)}),a(k,w),M()}export{K as E};
