import{p as e,a as t,m as a,c as n,o as s,s as r,g as o,b as i,l,d as _,i as c,t as f,e as h,f as u,h as m,r as p,j as b,k as d,n as v,q as g,u as S,v as w,w as R,x as M,y as A,z as k,A as y}from"./B1xmz3ZD.js";const T={},B=function(e,t,a){let n=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),r=s?.nonce||s?.getAttribute("nonce");n=Promise.allSettled(t.map(t=>{if(t=function(e,t){return new URL(e,t).href}(t,a),t in T)return;T[t]=!0;const n=t.endsWith(".css"),s=n?'[rel="stylesheet"]':"";if(!!a)for(let a=e.length-1;a>=0;a--){const s=e[a];if(s.href===t&&(!n||"stylesheet"===s.rel))return}else if(document.querySelector(`link[href="${t}"]${s}`))return;const o=document.createElement("link");return o.rel=n?"stylesheet":"modulepreload",n||(o.as="script"),o.crossOrigin="",o.href=t,r&&o.setAttribute("nonce",r),document.head.appendChild(o),n?new Promise((e,a)=>{o.addEventListener("load",e),o.addEventListener("error",()=>a(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function s(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return n.then(t=>{for(const e of t||[])"rejected"===e.status&&s(e.reason);return e().catch(s)})};class x{constructor(e={}){this.options={sampleRate:16e3,bufferSize:4096,...e},this.recording=!1,this.initialized=!1,this.callbacks={dataAvailable:null,onDataAvailable:null,complete:null,onComplete:null,error:null,onError:null},this._lastLogTime=0,this._sampleData={offset:0},this.recorder=null,this._scriptsLoaded=!1,console.log("[MP3Recorder] 初始化，配置:",this.options)}async init(){try{if(console.log("[MP3Recorder] 开始初始化录音..."),this._scriptsLoaded||window.Recorder||(console.log("[MP3Recorder] 加载录音相关脚本..."),await B(()=>Promise.resolve().then(()=>W),void 0,import.meta.url),await B(()=>Promise.resolve().then(()=>X),void 0,import.meta.url),await B(()=>Promise.resolve().then(()=>F),void 0,import.meta.url),this._scriptsLoaded=!0),!window.Recorder)throw new Error("Recorder库未成功加载");return this.recorder=new window.Recorder({type:"mp3",sampleRate:this.options.sampleRate,bitRate:16,bufferSize:this.options.bufferSize,context:this,onProcess:(e,t,a,n)=>{if(window.Recorder&&window.Recorder.SampleData&&this.recording){const t=window.Recorder.SampleData(e,n,16e3,this._sampleData||{offset:0});if(this._sampleData=t,t&&t.data&&t.data.length){const e=new Blob([t.data],{type:"audio/mp3"});"function"==typeof this.callbacks.dataAvailable&&this.callbacks.dataAvailable(e)}}}}),console.log("[MP3Recorder] 录音实例创建完成，等待调用open"),this.initialized=!0,!0}catch(e){return console.error("[MP3Recorder] 初始化失败:",e),"function"==typeof this.callbacks.error&&this.callbacks.error(e),"function"==typeof this.callbacks.onError&&this.callbacks.onError(e),!1}}async start(){return console.log("[MP3Recorder] 开始录音"),this.recorder?(this.recorder._mp3_chunk&&(this.recorder._mp3_chunk=[]),this._sampleData={offset:0},new Promise((e,t)=>{console.log("[MP3Recorder] 调用recorder.open()..."),this.recorder.open(()=>{console.log("[MP3Recorder] recorder.open()成功，调用recorder.start()"),this.recording=!0,this.recorder.start(),console.log("[MP3Recorder] 录音已开始"),e(!0)},e=>{console.error("[MP3Recorder] recorder.open()失败:",e),"function"==typeof this.callbacks.error&&this.callbacks.error(e),"function"==typeof this.callbacks.onError&&this.callbacks.onError(e),t(e)})})):(console.error("[MP3Recorder] 录音器未初始化"),!1)}stop(){console.log("[MP3Recorder] 停止录音"),this.recorder?(this.recording=!1,this.recorder.stop(e=>{console.log("[MP3Recorder] 录音停止成功，生成Blob:",e),"function"==typeof this.callbacks.complete&&(console.log("[MP3Recorder] 触发complete回调"),this.callbacks.complete(e)),"function"==typeof this.callbacks.onComplete&&(console.log("[MP3Recorder] 触发onComplete回调"),this.callbacks.onComplete(e))},e=>{console.error("[MP3Recorder] 录音停止失败:",e),"function"==typeof this.callbacks.error&&this.callbacks.error(e),"function"==typeof this.callbacks.onError&&this.callbacks.onError(e)})):console.error("[MP3Recorder] 录音器未初始化")}close(){if(console.log("[MP3Recorder] 关闭录音器"),this.recorder){this.recording=!1,this.initialized=!1;try{this.recorder.close(()=>{console.log("[MP3Recorder] 录音器关闭完成")})}catch(e){console.error("[MP3Recorder] 关闭录音器时出错:",e)}this.recorder=null}else console.error("[MP3Recorder] 录音器未初始化")}on(e,t){console.log("[MP3Recorder] 注册事件:",e);const a=e.startsWith("on"),n=a?e.substring(2).toLowerCase():e,s=a?e:"on"+e.charAt(0).toUpperCase()+e.slice(1);void 0!==this.callbacks[n]&&(this.callbacks[n]=t),void 0!==this.callbacks[s]&&(this.callbacks[s]=t)}}class E{constructor(e={}){this.options={appKey:"lj6Yy707ebVlsK78",token:"",enablePunctuation:!0,...e},this.wsClient=null,this.connected=!1,this.taskId="",this.lastResult="",this._lastLogTime=0,this._lastResultLogTime=0,this._lastBlobLogTime=0,this.callbacks={onOpen:null,onMessage:null,onClose:null,onError:null,onRecognitionCompleted:null}}generateUUID(){return Array.from({length:32},()=>{const e=Math.floor(16*Math.random());return String.fromCharCode(e<10?e+48:e-10+97)}).join("")}async connect(){this.connected&&this.disconnect(),this.taskId=this.generateUUID(),this.lastResult="";try{return console.log("[AliyunWebSocket] 尝试连接WebSocket，URL:",`wss://nls-gateway-cn-shanghai.aliyuncs.com/ws/v1?token=${this.options.token.substring(0,10)}...`),this.wsClient=new WebSocket(`wss://nls-gateway-cn-shanghai.aliyuncs.com/ws/v1?token=${this.options.token}`),new Promise((e,t)=>{const a=setTimeout(()=>{this.connected||(console.error("[AliyunWebSocket] WebSocket连接超时"),t(new Error("WebSocket连接超时")))},1e4);this.wsClient.onopen=()=>{console.log("[AliyunWebSocket] WebSocket连接已打开，发送StartTranscription命令"),this.wsClient.send(JSON.stringify({header:{appkey:this.options.appKey,message_id:this.generateUUID(),task_id:this.taskId,name:"StartTranscription",namespace:"SpeechTranscriber"},payload:{enable_intermediate_result:!0,enable_punctuation_prediction:this.options.enablePunctuation,max_sentence_silence:6e3}}))},this.wsClient.onmessage=t=>{try{const n=JSON.parse(t.data);(!this._lastLogTime||Date.now()-this._lastLogTime>5e3)&&(console.log("[AliyunWebSocket] 收到WebSocket消息:",n?.header?.name),this._lastLogTime=Date.now()),"TranscriptionStarted"===n?.header?.name?(console.log("[AliyunWebSocket] 识别任务已开始"),clearTimeout(a),this.connected=!0,this.callbacks.onOpen&&this.callbacks.onOpen(),e(!0)):"TranscriptionCompleted"===n?.header?.name?(console.log("[AliyunWebSocket] 识别任务已完成, 最终结果:",this.lastResult),this.callbacks.onRecognitionCompleted&&this.callbacks.onRecognitionCompleted(this.lastResult),setTimeout(()=>{this.disconnect()},500)):n?.payload?.result&&(this.lastResult=n.payload.result,(!this._lastResultLogTime||Date.now()-this._lastResultLogTime>1e3)&&(console.log("[AliyunWebSocket] 收到识别结果:",this.lastResult),this._lastResultLogTime=Date.now()),this.callbacks.onMessage&&this.callbacks.onMessage(this.lastResult))}catch(n){"string"==typeof t.data&&(console.warn("[AliyunWebSocket] 解析消息失败:",n,typeof t.data),t.data.length<100?console.warn("[AliyunWebSocket] 消息内容:",t.data):console.warn("[AliyunWebSocket] 消息内容(前100字符):",t.data.slice(0,100)))}},this.wsClient.onclose=e=>{console.log("[AliyunWebSocket] WebSocket连接已关闭",e),clearTimeout(a),this.connected=!1,this.callbacks.onClose&&this.callbacks.onClose(e),this.connected||t(e)},this.wsClient.onerror=e=>{console.error("[AliyunWebSocket] WebSocket连接错误",e),clearTimeout(a),this.callbacks.onError&&this.callbacks.onError(e),this.connected||t(e)}})}catch(e){throw console.error("[AliyunWebSocket] 连接失败:",e),this.callbacks.onError&&this.callbacks.onError(e),e}}disconnect(){if(this.wsClient){console.log("[AliyunWebSocket] 断开WebSocket连接");try{this.wsClient.close()}catch(e){console.error("[AliyunWebSocket] 关闭WebSocket时出错:",e)}this.connected=!1}}sendStopSignal(){if(!this.connected||!this.wsClient)return console.warn("[AliyunWebSocket] WebSocket未连接，无法发送停止信号"),!1;try{return console.log("[AliyunWebSocket] 发送停止识别信号"),this.wsClient.send(JSON.stringify({header:{appkey:this.options.appKey,message_id:this.generateUUID(),task_id:this.taskId,name:"StopTranscription",namespace:"SpeechTranscriber"}})),!0}catch(e){return console.error("[AliyunWebSocket] 发送停止信号失败:",e),!1}}sendAudioData(e){if(this.connected&&this.wsClient)try{if(this.wsClient.readyState!==WebSocket.OPEN)return void console.warn("[AliyunWebSocket] WebSocket未就绪，状态:",this.wsClient.readyState);if(e instanceof Blob)return void this.wsClient.send(e);if(e&&e.data&&e.data.length){const t=new Blob([e.data],{type:"audio/mp3"});return void this.wsClient.send(t)}console.warn("[AliyunWebSocket] 收到未知类型数据，尝试简单转换:",typeof e);try{const t=new Blob([e],{type:"audio/mp3"});this.wsClient.send(t)}catch(t){console.error("[AliyunWebSocket] 无法将数据转换为Blob:",t)}}catch(a){console.error("[AliyunWebSocket] 发送音频数据失败:",a)}else console.warn("[AliyunWebSocket] WebSocket未连接，无法发送数据")}on(e,t){void 0!==this.callbacks[`on${e.charAt(0).toUpperCase()+e.slice(1)}`]?this.callbacks[`on${e.charAt(0).toUpperCase()+e.slice(1)}`]=t:void 0!==this.callbacks[e]&&(this.callbacks[e]=t)}_convertInt16ToBase64(e){try{if(!e||!e.buffer)return console.error("[AliyunWebSocket] 无效的buffer:",e),"";const t=new Uint8Array(e.buffer);return btoa(String.fromCharCode(...new Uint8Array(t)))}catch(t){return console.error("[AliyunWebSocket] 转换Int16ToBase64失败:",t),""}}}async function P(e){try{const e=await fetch("https://api.gankao.com/api-yunying/gkasr/getToken"),t=await e.json();if(t.err)throw new Error(t.err.message||t.err);if(!t.result.token)throw new Error("未知原因");return t.result.token}catch(t){throw console.error("获取阿里云Token出错:",t),t}}function I(){const e={getUserMedia:!(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia),audioContext:!(!window.AudioContext&&!window.webkitAudioContext),webSocket:!!window.WebSocket};return e.getUserMedia&&e.audioContext&&e.webSocket}var L=f('<p class="text-gray-500">正在聆听...</p>'),C=f('<p class="text-gray-800"> </p>'),D=f('<div id="asr-text-panel" class="asr-text-panel absolute bg-white p-3 rounded-lg shadow-md transition-all svelte-1u7eg5l"><!></div>'),H=f('<span class="text-white text-xs">不支持</span>'),O=f('<div class="animate-spin w-8 h-8 border-4 border-gray-300 border-t-white rounded-full mx-auto"></div>'),V=f('<div class="flex flex-col items-center"><span class="text-white text-xs"> </span> <span class="text-white text-2xl">■</span></div>'),N=f('<span class="text-white text-xl"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 mx-auto"><path d="M12 15c1.66 0 3-1.34 3-3V6c0-1.66-1.34-3-3-3S9 4.34 9 6v6c0 1.66 1.34 3 3 3z"></path><path d="M17 12c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-2.08c3.39-.49 6-3.39 6-6.92h-2z"></path></svg></span>'),Y=f('<div class="asr-container relative flex flex-col items-center justify-center svelte-1u7eg5l"><!> <button id="asr-talk-btn"><!></button></div>');function j(f,T){e(T,!1);const B=a(),j=a(),q=a(),W=a();let X=t(T,"appKey",8,"lj6Yy707ebVlsK78"),F=t(T,"duration",8,30),z=t(T,"direction",8,"top"),U=t(T,"punctuation",8,!0),K=t(T,"showTextPanel",8,!0),Z=a("none"),G=a(""),Q=null,$=null,J=a(F()),ee=null,te="",ae=a(!0),ne="",se=0;const re=n();async function oe(){se=Date.now();try{if(!o(ae))throw new Error("浏览器不支持录音功能");if(console.log("[ASR] 开始录音流程"),r(Z,"loading"),r(G,""),ne="",console.log("[ASR] 开始获取Token"),te=await P(X()),console.log("[ASR] Token获取成功:",te.substring(0,10)+"..."),"loading"!==o(Z))return void console.log("[ASR] 状态已改变，取消WebSocket初始化");if(console.log("[ASR] 初始化WebSocket"),$=new E({appKey:X(),token:te,enablePunctuation:U()}),$.on("message",e=>{console.log("[ASR] 收到识别结果:",e),r(G,e),ne=e,re("textInput",{content:e,status:"changed"})}),$.on("error",e=>{console.error("[ASR] WebSocket错误:",e),r(Z,"none"),re("error",{error:e})}),$.on("close",e=>{console.log("[ASR] WebSocket连接已关闭",e),re("close",{event:e}),"recording"!==o(Z)&&"finishing"!==o(Z)||(console.log("[ASR] WebSocket异常关闭，重置录音状态"),r(Z,"none"))}),$.on("recognitionCompleted",e=>{console.log("[ASR] 识别完成，最终结果:",e||ne);const t=e||ne;ne=t,t?(console.log("[ASR] 发送textCompleted事件, 内容:",t),re("textCompleted",{content:t}),re("textInput",{content:t,status:"completed"})):console.warn("[ASR] 识别完成但没有结果"),setTimeout(()=>{r(Z,"none"),re("stop",{result:t})},100)}),console.log("[ASR] 连接WebSocket"),"loading"!==o(Z))return void console.log("[ASR] 状态已改变，取消WebSocket连接");if(await $.connect(),console.log("[ASR] WebSocket连接成功"),Q){console.log("[ASR] 关闭并重新创建录音器");try{Q.close()}catch(e){console.error("[ASR] 关闭已有录音器失败:",e)}Q=null}if("loading"!==o(Z))return console.log("[ASR] 状态已改变，取消录音器初始化"),void($&&$.connected&&($.disconnect(),$=null));console.log("[ASR] 创建新的录音器实例"),Q=new x({sampleRate:16e3}),console.log("[ASR] 绑定录音事件处理程序"),Q.on("dataAvailable",e=>{$&&$.connected?(console.log("[ASR] 发送音频数据到WebSocket",Date.now()-se),se=Date.now(),$.sendAudioData(e)):"recording"===o(Z)&&console.warn("[ASR] WebSocket未连接，无法发送音频数据")}),Q.on("error",e=>{console.error("[ASR] 录音错误:",e),r(Z,"none"),re("error",{error:e})}),console.log("[ASR] 初始化录音设备");if(!(await Q.init()))throw new Error("无法初始化录音");if(console.log("[ASR] 录音设备初始化成功"),"loading"!==o(Z)){if(console.log("[ASR] 状态已改变，取消开始录音"),Q){try{Q.close()}catch(e){console.error("[ASR] 关闭录音器失败:",e)}Q=null}return void($&&$.connected&&($.disconnect(),$=null))}console.log("[ASR] 开始录音");try{await Q.start(),r(Z,"recording"),r(J,F()),_e(),console.log("[ASR] 开始计时器, 时长:",F()),ee=setInterval(()=>{r(J,o(J)-1),o(J)<=0&&(console.log("[ASR] 计时结束，自动停止录音"),_e(),ie())},1e3),console.log("[ASR] 发送start事件"),re("start")}catch(t){throw console.error("[ASR] 启动录音失败:",t),new Error("启动录音失败: "+(t.message||t))}}catch(a){console.error("[ASR] 开始录音失败:",a),r(Z,"none"),re("error",{error:a})}}async function ie(){if("recording"===o(Z)||"loading"===o(Z)){console.log("[ASR] 停止录音"),r(Z,"finishing"),_e();try{Q?(console.log("[ASR] 停止录音设备"),Q.stop(),setTimeout(()=>{Q&&(console.log("[ASR] 关闭录音设备"),Q.close(),Q=null,console.log("[ASR] 录音器已释放")),$&&$.connected?(console.log("[ASR] 发送停止识别信号"),$.sendStopSignal()):(console.warn("[ASR] WebSocket未连接，无法发送停止信号"),r(Z,"none"),re("stop",{result:ne}))},200)):(r(Z,"none"),re("stop",{result:ne}))}catch(e){if(console.error("[ASR] 停止录音过程出错:",e),r(Z,"none"),re("error",{error:e}),Q){try{Q.close()}catch(t){console.error("[ASR] 关闭录音器出错:",t)}Q=null}}}}async function le(){try{if(console.log("[ASR] 预初始化"),r(ae,I()),!o(ae))return console.error("[ASR] 浏览器不支持录音功能"),re("error",{error:new Error("浏览器不支持录音功能")}),!1;try{console.log("[ASR] 预获取Token"),te=await P(X()),console.log("[ASR] Token预获取成功:",te.substring(0,10)+"...")}catch(e){console.warn("[ASR] 预获取Token失败，将在录音时重试:",e)}if(Q){try{Q.close()}catch(t){console.warn("[ASR] 关闭已有录音器失败:",t)}Q=null}console.log("[ASR] 预初始化录音设备"),Q=new x({sampleRate:16e3}),Q.on("dataAvailable",e=>{console.log("[ASR] 预初始化-测试数据收集事件:",e instanceof Blob?`Blob大小: ${e.size}bytes`:"非Blob数据")}),Q.on("error",e=>{console.error("[ASR] 录音错误:",e),re("error",{error:e})});try{console.log("[ASR] 尝试初始化录音设备");if(!(await Q.init()))throw new Error("无法初始化录音");console.log("[ASR] 录音设备初始化成功"),console.log("[ASR] 执行录音测试"),await Q.start(),console.log("[ASR] 预初始化测试启动成功"),await new Promise(e=>setTimeout(e,300));const e=await Q.stop();console.log("[ASR] 预初始化测试停止成功",e instanceof Blob?`生成测试音频: ${e.size}bytes`:"无测试音频");try{console.log("[ASR] 释放测试录音器"),Q.close()}catch(t){console.warn("[ASR] 关闭测试录音器失败:",t)}return Q=null,console.log("[ASR] 录音系统预初始化和测试完成"),!0}catch(a){console.error("[ASR] 预初始化录音设备失败:",a);try{Q&&(Q.close(),Q=null)}catch(n){console.error("[ASR] 关闭录音器出错:",n)}return!1}}catch(s){return console.error("[ASR] 预初始化失败:",s),!1}}function _e(){ee&&(console.log("[ASR] 清除计时器"),clearInterval(ee),ee=null)}async function ce(){try{return Q&&Q.getStream?Q.getStream():await navigator.mediaDevices.getUserMedia({audio:!0})}catch(e){return console.error("[ASR] 获取音频流失败:",e),null}}s(()=>{console.log("[ASR] 组件挂载"),r(ae,I()),o(ae)||(console.error("[ASR] 浏览器不支持录音功能"),re("error",{error:new Error("浏览器不支持录音功能")}))}),i(()=>{if(console.log("[ASR] 组件销毁"),ie(),_e(),Q){console.log("[ASR] 组件销毁时关闭录音器");try{Q.close()}catch(e){console.error("[ASR] 销毁时关闭录音器出错:",e)}Q=null}$&&($.disconnect(),$=null)}),l(()=>o(Z),()=>{r(B,"recording"===o(Z))}),l(()=>o(Z),()=>{r(j,"loading"===o(Z))}),l(()=>o(Z),()=>{r(q,"finishing"===o(Z))}),l(()=>{},()=>{r(W,function(){const e={};switch(z()){case"top":default:e.bottom="calc(3.5rem + 10px)",e.width="80vw",e.maxWidth="350px";break;case"bottom":e.top="calc(3.5rem + 10px)",e.width="80vw",e.maxWidth="350px";break;case"left":e.right="calc(3.5rem + 10px)",e.width="calc(min(80vw, 350px))";break;case"right":e.left="calc(3.5rem + 10px)",e.width="calc(min(80vw, 350px))"}return e}())}),_(),c();var fe=Y(),he=h(fe),ue=e=>{var t=D(),a=h(t),n=e=>{var t=L();w(e,t)},s=e=>{var t=C(),a=h(t,!0);p(t),b(()=>k(a,o(G))),w(e,t)};u(a,e=>{o(B)&&!o(G)?e(n):e(s,!1)}),p(t),b(e=>A(t,e),[()=>Object.entries(o(W)).map(([e,t])=>`${e}: ${t}`).join(";")],d),w(e,t)};u(he,e=>{K()&&(o(G)||o(B))&&e(ue)});var me=m(he,2);let pe;var be=h(me),de=e=>{var t=H();w(e,t)},ve=(e,t)=>{var a=e=>{var t=O();w(e,t)},n=(e,t)=>{var a=e=>{var t=V(),a=h(t),n=h(a,!0);p(a),y(2),p(t),b(e=>k(n,e),[()=>function(e){e=Math.max(0,e);const t=Math.floor(e/60),a=Math.floor(e%60);return`${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`}(o(J))],d),w(e,t)},n=e=>{var t=N();w(e,t)};u(e,e=>{o(B)?e(a):e(n,!1)},t)};u(e,e=>{o(j)?e(a):e(n,!1)},t)};return u(be,e=>{o(ae)?e(ve,!1):e(de)}),p(me),p(fe),b(e=>{pe=v(me,1,"asr-talk-btn relative w-14 h-14 rounded-full focus:outline-none transition-all transform svelte-1u7eg5l",null,pe,e),me.disabled=o(j)||o(q)||!o(ae),g(me,"aria-label",o(B)?"停止录音":"开始录音")},[()=>({"bg-blue-500":!o(B)&&!o(j)&&!o(q),"bg-red-500":o(B),"bg-gray-400":o(j)||o(q),"scale-110":o(B)})],d),S("click",me,function(){"none"===o(Z)?(console.log("[ASR] 点击按钮，开始录音"),oe()):"recording"===o(Z)&&(console.log("[ASR] 点击按钮，停止录音"),ie())}),w(f,fe),R(T,"startRecording",oe),R(T,"stopRecording",ie),R(T,"preInitialize",le),R(T,"getAudioStream",ce),M({startRecording:oe,stopRecording:ie,preInitialize:le,getAudioStream:ce})}class q{constructor(){this.audioContext=null,this.analyser=null,this.source=null,this.isDetecting=!1,this.config={wordDetection:{timeWindow:3e3,baseThreshold:5,multiplier:2.5,enabled:!0},volumeDetection:{highThreshold:-10,lowThreshold:-50,enabled:!0,checkInterval:1e3}},this.wordDetectionState={startTime:null,recognizedWords:[],currentTargetWordCount:1,lastContent:""},this.volumeDetectionState={timer:null,lastWarningTime:0,warningCooldown:5e3},this.wordWarningState={lastWarningTime:0,warningCooldown:5e3},this.onEnvironmentWarning=null,this.lastWordsCount=0}async initialize(e,t){try{return this.onEnvironmentWarning=t,this.config.volumeDetection.enabled&&await this.initVolumeDetection(e),console.log("[EnvironmentDetector] 环境检测器初始化成功"),!0}catch(a){return console.error("[EnvironmentDetector] 初始化失败:",a),!1}}async initVolumeDetection(e){this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=256,this.source=this.audioContext.createMediaStreamSource(e),this.source.connect(this.analyser),console.log("[EnvironmentDetector] 音量检测初始化完成")}startDetection(e=1){this.isDetecting&&this.stopDetection(),this.isDetecting=!0,console.log("[EnvironmentDetector] 开始环境检测，目标单词数:",e),this.config.wordDetection.enabled&&this.startWordDetection(e),this.config.volumeDetection.enabled&&this.analyser&&this.startVolumeDetection()}startWordDetection(e){this.lastWordsCount=0,this.wordDetectionState={startTime:Date.now(),recognizedWords:[],currentTargetWordCount:e,lastContent:""},console.log("[EnvironmentDetector] 单词量检测已启动，目标单词数:",e)}startVolumeDetection(){this.volumeDetectionState.timer=setInterval(()=>{this.checkVolumeLevel()},this.config.volumeDetection.checkInterval),console.log("[EnvironmentDetector] 音量检测已启动")}checkVolumeLevel(){if(!this.analyser)return;const e=new Uint8Array(this.analyser.frequencyBinCount);this.analyser.getByteFrequencyData(e);const t=e.reduce((e,t)=>e+t)/e.length,a=20*Math.log10(t/255),n=Date.now(),{highThreshold:s,lowThreshold:r}=this.config.volumeDetection,{lastWarningTime:o,warningCooldown:i}=this.volumeDetectionState;if(n-o<i)return;let l=null;a>s?l="环境音量过高，可能影响识别准确性，建议降低环境噪音！":a<r&&(l="环境音量过低，请确保麦克风正常工作并靠近说话！"),l&&this.onEnvironmentWarning&&(this.volumeDetectionState.lastWarningTime=n,this.onEnvironmentWarning(l,"volume"),console.log("[EnvironmentDetector] 音量警告:",l,"当前分贝:",a.toFixed(1)))}processASRResult(e){if(!this.config.wordDetection.enabled||!this.isDetecting)return;const t=Date.now(),{startTime:a,currentTargetWordCount:n}=this.wordDetectionState,s=t-a;if(s>this.config.wordDetection.timeWindow)return;const r=this.countWords(e);this.lastWordsCount>1.5*r&&(this.lastWordsCount=0);const o=r-this.lastWordsCount,i=Math.max(this.config.wordDetection.baseThreshold,Math.ceil(n*this.config.wordDetection.multiplier));if(console.log("[EnvironmentDetector] 单词检测:",{content:e,wordCount:o,threshold:i,timeElapsed:s+"ms"}),o>i){const{lastWarningTime:e,warningCooldown:a}=this.wordWarningState;if(t-e>=a){const e="当前环境嘈杂，会影响评测质量，请换到安静环境！";this.onEnvironmentWarning&&(this.onEnvironmentWarning(e,"words"),console.log("[EnvironmentDetector] 单词量警告:",e,{wordCount:o,threshold:i,timeElapsed:s+"ms"})),this.wordWarningState.lastWarningTime=t}else console.log("[EnvironmentDetector] 单词量超阈值但在冷却期内，跳过警告",{wordCount:o,threshold:i,cooldownRemaining:a-(t-e)+"ms"})}this.lastWordsCount=r}countWords(e){if(!e)return 0;const t=e.replace(/[，。！？；：""''（）【】《》〈〉「」『』〔〕［］｛｝〖〗〘〙〚〛,.!?;:"'()\[\]{}<>]/g,"").replace(/\s+/g," ").trim();if(!t)return 0;const a=t.match(/[\u4e00-\u9fff]/g)||[],n=t.match(/[a-zA-Z]+/g)||[],s=Math.ceil(a.length/2),r=n.length,o=s+r;return console.log("[EnvironmentDetector] 单词计数:",{text:t,chineseChars:a.length,englishWords:r,chineseWordCount:s,totalWords:o}),o}stopDetection(){this.isDetecting=!1,this.volumeDetectionState.timer&&(clearInterval(this.volumeDetectionState.timer),this.volumeDetectionState.timer=null),console.log("[EnvironmentDetector] 环境检测已停止")}cleanup(){this.stopDetection(),this.source&&(this.source.disconnect(),this.source=null),this.audioContext&&"closed"!==this.audioContext.state&&(this.audioContext.close(),this.audioContext=null),this.analyser=null,this.onEnvironmentWarning=null,this.wordWarningState.lastWarningTime=0,this.volumeDetectionState.lastWarningTime=0,console.log("[EnvironmentDetector] 资源清理完成")}updateConfig(e){this.config={...this.config,...e},console.log("[EnvironmentDetector] 配置已更新:",this.config)}}!function(e){var t=function(){},a=function(e){return new g(e)};a.LM="2023-02-01 18:05";var n="Recorder",s="getUserMedia",r="srcSampleRate",o="sampleRate",i="catch";a.IsOpen=function(){var e=a.Stream;if(e){var t=(e.getTracks&&e.getTracks()||e.audioTracks||[])[0];if(t){var n=t.readyState;return"live"==n||n==t.LIVE}}return!1},a.BufferSize=4096,a.Destroy=function(){for(var e in b(n+" Destroy"),p(),l)l[e]()};var l={};a.BindDestroy=function(e,t){l[e]=t},a.Support=function(){var e=navigator.mediaDevices||{};return e[s]||(e=navigator)[s]||(e[s]=e.webkitGetUserMedia||e.mozGetUserMedia||e.msGetUserMedia),!!e[s]&&(a.Scope=e,!!a.GetContext())},a.GetContext=function(){var t=e.AudioContext;return t||(t=e.webkitAudioContext),t?(a.Ctx&&"closed"!=a.Ctx.state||(a.Ctx=new t,a.BindDestroy("Ctx",function(){var e=a.Ctx;e&&e.close&&(e.close(),a.Ctx=0)})),a.Ctx):null};var _="ConnectEnableWebM";a[_]=!0;var c="ConnectEnableWorklet";a[c]=!1;var f=function(t,s){var r,l,f,h=t.BufferSize||a.BufferSize,p=a.Ctx,d=t.Stream,v=function(e){var t=d._m=p.createMediaStreamSource(d),a=p.destination,n="createMediaStreamDestination";p[n]&&(a=p[n]()),t.connect(e),e.connect(a)},g="",w=d._call,R=function(e){for(var t in w){for(var a=e.length,n=new Int16Array(a),s=0,r=0;r<a;r++){var o=Math.max(-1,Math.min(1,e[r]));o=o<0?32768*o:32767*o,n[r]=o,s+=Math.abs(o)}for(var i in w)w[i](n,s);return}},M="ScriptProcessor",A="audioWorklet",k=n+" "+A,y="RecProc",T="MediaRecorder",B=T+".WebM.PCM",x=p.createScriptProcessor||p.createJavaScriptNode,E="。由于"+A+"内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启"+A+"。",P=function(){l=d.isWorklet=!1,u(d),b("Connect采用老的"+M+"，"+(a[c]?"但已":"可")+"设置"+n+"."+c+"=true尝试启用"+A+g+E,3);var e=d._p=x.call(p,h,1,1);v(e);var t="_D220626",s=a[t];s&&b("Use "+n+"."+t,3),e.onaudioprocess=function(e){var t=e.inputBuffer.getChannelData(0);s?(t=new Float32Array(t),setTimeout(function(){R(t)})):R(t)}},I=function(){r=d.isWebM=!1,m(d),l=d.isWorklet=!x||a[c];var t=e.AudioWorkletNode;if(l&&p[A]&&t){var s=function(){var e=function(e){return e.toString().replace(/^function|DEL_/g,"").replace(/\$RA/g,k)},t="class "+y+" extends AudioWorkletProcessor{";return t+="constructor "+e(function(e){DEL_super(e);var t=this,a=e.processorOptions.bufferSize;t.bufferSize=a,t.buffer=new Float32Array(2*a),t.pos=0,t.port.onmessage=function(e){e.data.kill&&(t.kill=!0,console.log("$RA kill call"))},console.log("$RA .ctor call",e)}),t+="process "+e(function(e,t,a){var n=this,s=n.bufferSize,r=n.buffer,o=n.pos;if((e=(e[0]||[])[0]||[]).length){r.set(e,o);var i=~~((o+=e.length)/s)*s;if(i){this.port.postMessage({val:r.slice(0,i)});var l=r.subarray(i,o);(r=new Float32Array(2*s)).set(l),o=l.length,n.buffer=r}n.pos=o}return!n.kill}),t+='}try{registerProcessor("'+y+'", '+y+')}catch(e){console.error("'+k+'注册失败",e)}',"data:text/javascript;base64,"+btoa(unescape(encodeURIComponent(t)))},o=function(){return l&&d._na},_=d._na=function(){""!==f&&(clearTimeout(f),f=setTimeout(function(){f=0,o()&&(b(A+"未返回任何音频，恢复使用"+M,3),x&&P())},500))},u=function(){if(o()){var e=d._n=new t(p,y,{processorOptions:{bufferSize:h}});v(e),e.port.onmessage=function(e){f&&(clearTimeout(f),f=""),o()?R(e.data.val):l||b(A+"多余回调",3)},b("Connect采用"+A+"，设置"+n+"."+c+"=false可恢复老式"+M+g+E,3)}};p.resume()[w&&"finally"](function(){if(o())if(p[y])u();else{var e=s();p[A].addModule(e).then(function(e){o()&&(p[y]=1,u(),f&&_())})[i](function(e){b(A+".addModule失败",1,e),o()&&P()})}})}else P()};!function(){var t=e[T],i="ondataavailable",l="audio/webm; codecs=pcm";r=d.isWebM=a[_];var c=t&&i in t.prototype&&t.isTypeSupported(l);if(g=c?"":"（此浏览器不支持"+B+"）",s&&r&&c){var u=function(){return r&&d._ra};d._ra=function(){""!==f&&(clearTimeout(f),f=setTimeout(function(){u()&&(b(T+"未返回任何音频，降级使用"+A,3),I())},500))};var m=Object.assign({mimeType:l},a.ConnectWebMOptions),v=d._r=new t(d,m),w=d._rd={sampleRate:p[o]};v[i]=function(e){var t=new FileReader;t.onloadend=function(){if(u()){var e=S(new Uint8Array(t.result),w);if(!e)return;if(-1==e)return void I();f&&(clearTimeout(f),f=""),R(e)}else r||b(T+"多余回调",3)},t.readAsArrayBuffer(e.data)},v.start(~~(h/48)),b("Connect采用"+B+"，设置"+n+"."+_+"=false可恢复使用"+A+"或老式"+M)}else I()}()},h=function(e){e._na&&e._na(),e._ra&&e._ra()},u=function(e){e._na=null,e._n&&(e._n.port.postMessage({kill:!0}),e._n.disconnect(),e._n=null)},m=function(e){e._ra=null,e._r&&(e._r.stop(),e._r=null)},p=function(e){var t=(e=e||a)==a,n=e.Stream;if(n&&(n._m&&(n._m.disconnect(),n._m=null),n._p&&(n._p.disconnect(),n._p.onaudioprocess=n._p=null),u(n),m(n),t)){for(var s=n.getTracks&&n.getTracks()||n.audioTracks||[],r=0;r<s.length;r++){var o=s[r];o.stop&&o.stop()}n.stop&&n.stop()}e.Stream=0};a.SampleData=function(e,t,a,n,s){n||(n={});var r=n.index||0,o=n.offset||0,i=n.frameNext||[];s||(s={});var l=s.frameSize||1;s.frameType&&(l="mp3"==s.frameType?1152:1);var _=e.length;r>_+1&&b("SampleData似乎传入了未重置chunk "+r+">"+_,3);for(var c=0,f=r;f<_;f++)c+=e[f].length;c=Math.max(0,c-Math.floor(o));var h=t/a;h>1?c=Math.floor(c/h):(h=1,a=t),c+=i.length;var u=new Int16Array(c),m=0;for(f=0;f<i.length;f++)u[m]=i[f],m++;for(;r<_;r++){for(var p=e[r],d=(f=o,p.length);f<d;){var v=Math.floor(f),g=Math.ceil(f),S=f-v,w=p[v],R=g<d?p[g]:(e[r+1]||[w])[0]||0;u[m]=w+(R-w)*S,m++,f+=h}o=f-d}i=null;var M=u.length%l;if(M>0){var A=2*(u.length-M);i=new Int16Array(u.buffer.slice(A)),u=new Int16Array(u.buffer.slice(0,A))}return{index:r,offset:o,frameNext:i,sampleRate:a,data:u}},a.PowerLevel=function(e,t){var a=e/t||0;return a<1251?Math.round(a/1250*10):Math.round(Math.min(100,Math.max(0,100*(1+Math.log(a/1e4)/Math.log(10)))))},a.PowerDBFS=function(e){var t=Math.max(.1,e||0),a=32767;return t=Math.min(t,a),t=20*Math.log(t/a)/Math.log(10),Math.max(-100,Math.round(t))},a.CLog=function(t,a){var s=new Date,r=("0"+s.getMinutes()).substr(-2)+":"+("0"+s.getSeconds()).substr(-2)+"."+("00"+s.getMilliseconds()).substr(-3),o=this&&this.envIn&&this.envCheck&&this.id,i=["["+r+" "+n+(o?":"+o:"")+"]"+t],l=arguments,_=e.console||{},c=2,f=_.log;for("number"==typeof a?f=1==a?_.error:3==a?_.warn:f:c=1;c<l.length;c++)i.push(l[c]);d?f&&f("[IsLoser]"+i[0],i.length>1?i:""):f.apply(_,i)};var b=function(){a.CLog.apply(this,arguments)},d=!0;try{d=!console.log.apply}catch(y){}var v=0;function g(e){this.id=++v,k();var a={type:"mp3",bitRate:16,sampleRate:16e3,onProcess:t};for(var n in e)a[n]=e[n];this.set=a,this._S=9,this.Sync={O:9,C:9}}a.Sync={O:9,C:9},a.prototype=g.prototype={CLog:b,_streamStore:function(){return this.set.sourceStream?this:a},open:function(n,r){var o=this,l=o._streamStore();n=n||t;var _=function(e,t){t=!!t,o.CLog("录音open失败："+e+",isUserNotAllow:"+t,1),r&&r(e,t)},c=function(){o.CLog("open ok id:"+o.id),n(),o._SO=0},h=l.Sync,u=++h.O,m=h.C;o._O=o._O_=u,o._SO=o._S;var b=function(){if(m!=h.C||!o._O){var e="open被取消";return u==h.O?o.close():e="open被中断",_(e),!0}},d=o.envCheck({envName:"H5",canProcess:!0});if(d)_("不能录音："+d);else if(o.set.sourceStream){if(!a.GetContext())return void _("不支持此浏览器从流中获取录音");p(l),o.Stream=o.set.sourceStream,o.Stream._call={};try{f(l)}catch(y){return void _("从流中打开录音失败："+y.message)}c()}else{var v=function(t,a){try{e.top.a}catch(y){return void _('无权录音(跨域，请尝试给iframe添加麦克风访问策略，如allow="camera;microphone")')}/Permission|Allow/i.test(t)?_("用户拒绝了录音权限",!0):!1===e.isSecureContext?_("浏览器禁止不安全页面录音，可开启https解决"):/Found/i.test(t)?_(a+"，无可用麦克风"):_(a)};if(a.IsOpen())c();else if(a.Support()){var g=function(e){setTimeout(function(){e._call={};var t=a.Stream;t&&(p(),e._call=t._call),a.Stream=e,b()||(a.IsOpen()?(t&&o.CLog("发现同时多次调用open",1),f(l,1),c()):_("录音功能无效：无音频流"))},100)},S=function(e){var t=e.name||e.message||e.code+":"+e;o.CLog("请求录音权限错误",1,e),v(t,"无法录音："+t)},w={noiseSuppression:!1,echoCancellation:!1},R=o.set.audioTrackSet;for(var M in R)w[M]=R[M];w.sampleRate=a.Ctx.sampleRate;try{var A=a.Scope[s]({audio:w},g,S)}catch(y){o.CLog(s,3,y),A=a.Scope[s]({audio:!0},g,S)}A&&A.then&&A.then(g)[i](S)}else v("","此浏览器不支持录音")}},close:function(e){e=e||t;var a=this,n=a._streamStore();a._stop();var s=n.Sync;if(a._O=0,a._O_!=s.O)return a.CLog("close被忽略（因为同时open了多个rec，只有最后一个会真正close）",3),void e();s.C++,p(n),a.CLog("close"),e()},mock:function(e,t){var a=this;return a._stop(),a.isMock=1,a.mockEnvInfo=null,a.buffers=[e],a.recSize=e.length,a[r]=t,a},envCheck:function(t){var n,s=this,r=s.set,o="CPU_BE";if(n||a[o]||!e.Int8Array||new Int8Array(new Int32Array([1]).buffer)[0]||(k(o),n="不支持"+o+"架构"),!n){var i=r.type;s[i+"_envCheck"]?n=s[i+"_envCheck"](t,r):r.takeoffEncodeChunk&&(n=i+"类型"+(s[i]?"":"(未加载编码器)")+"不支持设置takeoffEncodeChunk")}return n||""},envStart:function(e,t){var a=this,n=a.set;a.isMock=e?1:0,a.mockEnvInfo=e,a.buffers=[],a.recSize=0,a.envInLast=0,a.envInFirst=0,a.envInFix=0,a.envInFixTs=[];var s=n[o];if(s>t?n[o]=t:s=0,a[r]=t,a.CLog(r+": "+t+" set."+o+": "+n[o]+(s?" 忽略"+s:""),s?3:0),a.engineCtx=0,a[n.type+"_start"]){var i=a.engineCtx=a[n.type+"_start"](n);i&&(i.pcmDatas=[],i.pcmSize=0)}},envResume:function(){this.envInFixTs=[]},envIn:function(e,t){var n=this,s=n.set,i=n.engineCtx,l=n[r],_=e.length,c=a.PowerLevel(t,_),f=n.buffers,h=f.length;f.push(e);var u=f,m=h,p=Date.now(),b=Math.round(_/l*1e3);n.envInLast=p,1==n.buffers.length&&(n.envInFirst=p-b);var d=n.envInFixTs;d.splice(0,0,{t:p,d:b});for(var v=p,g=0,S=0;S<d.length;S++){var w=d[S];if(p-w.t>3e3){d.length=S;break}v=w.t,g+=w.d}var R=d[1],M=p-v;if(M-g>M/3&&(R&&M>1e3||d.length>=6)){var A=p-R.t-b;if(A>b/5){var k=!s.disableEnvInFix;if(n.CLog("["+p+"]"+(k?"":"未")+"补偿"+A+"ms",3),n.envInFix+=A,k){var T=new Int16Array(A*l/1e3);_+=T.length,f.push(T)}}}var B=n.recSize,x=_,E=B+x;if(n.recSize=E,i){var P=a.SampleData(f,l,s[o],i.chunkInfo);i.chunkInfo=P,E=(B=i.pcmSize)+(x=P.data.length),i.pcmSize=E,f=i.pcmDatas,h=f.length,f.push(P.data),l=P[o]}var I=Math.round(E/l*1e3),L=f.length,C=u.length,D=function(){for(var e=H?0:-x,t=null==f[0],a=h;a<L;a++){var r=f[a];null==r?t=1:(e+=r.length,i&&r.length&&n[s.type+"_encode"](i,r))}if(t&&i)for(a=m,u[0]&&(a=0);a<C;a++)u[a]=null;t&&(e=H?x:0,f[0]=null),i?i.pcmSize+=e:n.recSize+=e},H=0,O="rec.set.onProcess";try{H=s.onProcess(f,c,I,l,h,D)}catch(y){console.error(O+"回调出错是不允许的，需保证不会抛异常",y)}var V=Date.now()-p;if(V>10&&n.envInFirst-p>1e3&&n.CLog(O+"低性能，耗时"+V+"ms",3),!0===H){var N=0;for(S=h;S<L;S++)null==f[S]?N=1:f[S]=new Int16Array(0);N?n.CLog("未进入异步前不能清除buffers",3):i?i.pcmSize-=x:n.recSize-=x}else D()},start:function(){var e=this,t=a.Ctx,n=1;if(e.set.sourceStream?e.Stream||(n=0):a.IsOpen()||(n=0),n)if(e.CLog("开始录音"),e._stop(),e.state=3,e.envStart(null,t[o]),e._SO&&e._SO+1!=e._S)e.CLog("start被中断",3);else{e._SO=0;var s=function(){3==e.state&&(e.state=1,e.resume())};if("suspended"==t.state){var r="AudioContext resume: ";e.CLog(r+"wait..."),t.resume().then(function(){e.CLog(r+t.state),s()})[i](function(a){e.CLog(r+t.state+" 可能无法录音："+a.message,1,a),s()})}else s()}else e.CLog("未open",1)},pause:function(){var e=this;e.state&&(e.state=2,e.CLog("pause"),delete e._streamStore().Stream._call[e.id])},resume:function(){var e=this;if(e.state){e.state=1,e.CLog("resume"),e.envResume();var t=e._streamStore().Stream;t._call[e.id]=function(t,a){1==e.state&&e.envIn(t,a)},h(t)}},_stop:function(e){var t=this,a=t.set;t.isMock||t._S++,t.state&&(t.pause(),t.state=0),!e&&t[a.type+"_stop"]&&(t[a.type+"_stop"](t.engineCtx),t.engineCtx=0)},stop:function(e,t,n){var s,i=this,l=i.set,_=i.envInLast-i.envInFirst,c=_&&i.buffers.length;i.CLog("stop 和start时差"+(_?_+"ms 补偿"+i.envInFix+"ms envIn:"+c+" fps:"+(c/_*1e3).toFixed(1):"-"));var f=function(){i._stop(),n&&i.close()},h=function(e){i.CLog("结束录音失败："+e,1),t&&t(e),f()},u=function(t,a){if(i.CLog("结束录音 编码花"+(Date.now()-s)+"ms 音频时长"+a+"ms 文件大小"+t.size+"b"),l.takeoffEncodeChunk)i.CLog("启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据",3);else if(t.size<Math.max(100,a/2))return void h("生成的"+l.type+"无效");e&&e(t,a),f()};if(!i.isMock){var m=3==i.state;if(!i.state||m)return void h("未开始录音"+(m?"，开始录音前无用户交互导致AudioContext未运行":""));i._stop(!0)}var p=i.recSize;if(p)if(i.buffers[0])if(i[l.type]){if(i.isMock){var b=i.envCheck(i.mockEnvInfo||{envName:"mock",canProcess:!1});if(b)return void h("录音错误："+b)}var d=i.engineCtx;if(i[l.type+"_complete"]&&d){var v=Math.round(d.pcmSize/l[o]*1e3);return s=Date.now(),void i[l.type+"_complete"](d,function(e){u(e,v)},h)}s=Date.now();var g=a.SampleData(i.buffers,i[r],l[o]);l[o]=g[o];var S=g.data;v=Math.round(S.length/l[o]*1e3),i.CLog("采样"+p+"->"+S.length+" 花:"+(Date.now()-s)+"ms"),setTimeout(function(){s=Date.now(),i[l.type](S,function(e){u(e,v)},function(e){h(e)})})}else h("未加载"+l.type+"编码器");else h("音频buffers被释放");else h("未采集到录音")}},e[n]&&(b("重复引入"+n,3),e[n]&&e[n].Destroy&&e[n].Destroy()),e[n]=a;var S=function(e,t){t.pos||(t.pos=[0],t.tracks={},t.bytes=[]);var a=t.tracks,n=[t.pos[0]],s=function(){t.pos[0]=n[0]},r=t.bytes.length,i=new Uint8Array(r+e.length);if(i.set(t.bytes),i.set(e,r),t.bytes=i,!t._ht){if(M(i,n),A(i,n),!w(M(i,n),[24,83,128,103]))return;for(M(i,n);n[0]<i.length;){var l=M(i,n),_=A(i,n),c=[0],f=0;if(!_)return;if(w(l,[22,84,174,107])){for(;c[0]<_.length;){var h=M(_,c),u=A(_,c),m=[0],p={channels:0,sampleRate:0};if(w(h,[174]))for(;m[0]<u.length;){var d=M(u,m),v=A(u,m),g=[0];if(w(d,[215])){var S=R(v);p.number=S,a[S]=p}else if(w(d,[131]))1==(S=R(v))?p.type="video":2==S?(p.type="audio",f||(t.track0=p),p.idx=f++):p.type="Type-"+S;else if(w(d,[134])){for(var k="",y=0;y<v.length;y++)k+=String.fromCharCode(v[y]);p.codec=k}else if(w(d,[225]))for(;g[0]<v.length;){var T=M(v,g),B=A(v,g);if(w(T,[181])){S=0;var x=new Uint8Array(B.reverse()).buffer;4==B.length?S=new Float32Array(x)[0]:8==B.length?S=new Float64Array(x)[0]:b("WebM Track !Float",1,B),p[o]=Math.round(S)}else w(T,[98,100])?p.bitDepth=R(B):w(T,[159])&&(p.channels=R(B))}}}t._ht=1,b("WebM Tracks",a),s();break}}}var E=t.track0;if(E){if(16==E.bitDepth&&/FLOAT/i.test(E.codec)&&(E.bitDepth=32,b("WebM 16改32位",3)),E[o]!=t[o]||32!=E.bitDepth||E.channels<1||!/(\b|_)PCM\b/i.test(E.codec))return t.bytes=[],t.bad||b("WebM Track非预期",3,t),t.bad=1,-1;for(var P=[],I=0;n[0]<i.length&&(h=M(i,n),u=A(i,n));){if(w(h,[163])){var L=15&u[0];if(p=a[L]){if(0===p.idx){var C=new Uint8Array(u.length-4);for(y=4;y<u.length;y++)C[y-4]=u[y];P.push(C),I+=C.length}}else b("WebM !Track"+L,1,a)}s()}if(I){var D=new Uint8Array(i.length-t.pos[0]);D.set(i.subarray(t.pos[0])),t.bytes=D,t.pos[0]=0,C=new Uint8Array(I),y=0;for(var H=0;y<P.length;y++)C.set(P[y],H),H+=P[y].length;if(x=new Float32Array(C.buffer),E.channels>1){var O=[];for(y=0;y<x.length;)O.push(x[y]),y+=E.channels;x=new Float32Array(O)}return x}}},w=function(e,t){if(!e||e.length!=t.length)return!1;if(1==e.length)return e[0]==t[0];for(var a=0;a<e.length;a++)if(e[a]!=t[a])return!1;return!0},R=function(e){for(var t="",a=0;a<e.length;a++){var n=e[a];t+=(n<16?"0":"")+n.toString(16)}return parseInt(t,16)||0},M=function(e,t,a){var n=t[0];if(!(n>=e.length)){var s=("0000000"+e[n].toString(2)).substr(-8),r=/^(0*1)(\d*)$/.exec(s);if(r){var o=r[1].length,i=[];if(!(n+o>e.length)){for(var l=0;l<o;l++)i[l]=e[n],n++;return a&&(i[0]=parseInt(r[2]||"0",2)),t[0]=n,i}}}},A=function(e,t){var a=M(e,t,1);if(a){var n=R(a),s=t[0],r=[];if(n<2147483647){if(s+n>e.length)return;for(var o=0;o<n;o++)r[o]=e[s],s++}return t[0]=s,r}};a.TrafficImgUrl="//ia.51.la/go1?id=20469973&pvFlag=1";var k=a.Traffic=function(e){e=e?"/"+n+"/Report/"+e:"";var t=a.TrafficImgUrl;if(t){var s=a.Traffic,r=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],o=r[1]||"http://file/",i=(r[0]||o)+e;0==t.indexOf("//")&&(t=/^https:/i.test(i)?"https:"+t:"http:"+t),e&&(t=t+"&cu="+encodeURIComponent(o+e)),s[i]||(s[i]=1,(new Image).src=t,b("Traffic Analysis Image: "+(e||n+".TrafficImgUrl="+a.TrafficImgUrl)))}}}(window),"function"==typeof define&&define.amd&&define(function(){return Recorder}),"object"==typeof module&&module.exports&&(module.exports=Recorder);const W=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));!function(){var e;Recorder.prototype.enc_mp3={stable:!0,testmsg:"采样率范围48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000"},Recorder.prototype.mp3=function(e,t,a){var r=this,o=r.set,i=e.length,l=r.mp3_start(o);if(l)return r.mp3_encode(l,e),void r.mp3_complete(l,t,a,1);var _=new Recorder.lamejs.Mp3Encoder(1,o.sampleRate,o.bitRate),c=[],f=0,h=0,u=function(){if(f<i){(a=_.encodeBuffer(e.subarray(f,f+57600))).length>0&&(h+=a.buffer.byteLength,c.push(a.buffer)),f+=57600,setTimeout(u)}else{var a;(a=_.flush()).length>0&&(h+=a.buffer.byteLength,c.push(a.buffer));var r=n.fn(c,h,i,o.sampleRate);s(r,o),t(new Blob(c,{type:"audio/mp3"}))}};u()},Recorder.BindDestroy("mp3Worker",function(){Recorder.CLog("mp3Worker Destroy"),e&&e.terminate(),e=null}),Recorder.prototype.mp3_envCheck=function(e,t){var n="";return t.takeoffEncodeChunk&&(e.canProcess?a()||(n="当前浏览器版本太低，无法实时处理"):n=e.envName+"环境不支持实时处理"),n},Recorder.prototype.mp3_start=function(e){return a(e)};var t={id:0},a=function(a){var s=e;try{if(!s){var r=");wk_lame();var wk_ctxs={};self.onmessage="+function(e){var t=e.data,a=wk_ctxs[t.id];if("init"==t.action)wk_ctxs[t.id]={sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:t.takeoff,mp3Size:0,pcmSize:0,encArr:[],encObj:new wk_lame.Mp3Encoder(1,t.sampleRate,t.bitRate)};else if(!a)return;switch(t.action){case"stop":a.encObj=null,delete wk_ctxs[t.id];break;case"encode":a.pcmSize+=t.pcm.length,(n=a.encObj.encodeBuffer(t.pcm)).length>0&&(a.takeoff?self.postMessage({action:"takeoff",id:t.id,chunk:n}):(a.mp3Size+=n.buffer.byteLength,a.encArr.push(n.buffer)));break;case"complete":var n;(n=a.encObj.flush()).length>0&&(a.takeoff?self.postMessage({action:"takeoff",id:t.id,chunk:n}):(a.mp3Size+=n.buffer.byteLength,a.encArr.push(n.buffer)));var s=wk_mp3TrimFix.fn(a.encArr,a.mp3Size,a.pcmSize,a.sampleRate);self.postMessage({action:t.action,id:t.id,blob:new Blob(a.encArr,{type:"audio/mp3"}),meta:s})}};r+=";var wk_mp3TrimFix={rm:"+n.rm+",fn:"+n.fn+"}";var o=Recorder.lamejs.toString(),i=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_lame=(",o,r],{type:"text/javascript"}));s=new Worker(i),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(i)},1e4),s.onmessage=function(e){var a=e.data,n=t[a.id];n&&("takeoff"==a.action?n.set.takeoffEncodeChunk(new Uint8Array(a.chunk.buffer)):(n.call&&n.call(a),n.call=null))}}var l={worker:s,set:a,takeoffQueue:[]};return a?(l.id=++t.id,t[l.id]=l,s.postMessage({action:"init",id:l.id,sampleRate:a.sampleRate,bitRate:a.bitRate,takeoff:!!a.takeoffEncodeChunk,x:new Int16Array(5)})):s.postMessage({x:new Int16Array(5)}),e=s,l}catch(_){return s&&s.terminate(),console.error(_),null}};Recorder.prototype.mp3_stop=function(e){if(e&&e.worker){e.worker.postMessage({action:"stop",id:e.id}),e.worker=null,delete t[e.id];var a=-1;for(var n in t)a++;a&&Recorder.CLog("mp3 worker剩"+a+"个在串行等待",3)}},Recorder.prototype.mp3_encode=function(e,t){e&&e.worker&&e.worker.postMessage({action:"encode",id:e.id,pcm:t})},Recorder.prototype.mp3_complete=function(e,t,a,n){var r=this;e&&e.worker?(e.call=function(a){s(a.meta,e.set),t(a.blob),n&&r.mp3_stop(e)},e.worker.postMessage({action:"complete",id:e.id})):a("mp3编码器未打开")},Recorder.mp3ReadMeta=function(e,t){var a="object"==typeof window?window.parseInt:self.parseInt,n=new Uint8Array(e[0]||[]);if(n.length<4)return null;var s=function(e,t){return("0000000"+((t||n)[e]||0).toString(2)).substr(-8)},r=s(0)+s(1),o=s(2)+s(3);if(!/^1{11}/.test(r))return null;var i={"00":2.5,10:2,11:1}[r.substr(11,2)],l={"01":3}[r.substr(13,2)],_={1:[44100,48e3,32e3],2:[22050,24e3,16e3],2.5:[11025,12e3,8e3]}[i];_&&(_=_[a(o.substr(4,2),2)]);var c=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320]][1==i?1:0][a(o.substr(0,4),2)];if(!(i&&l&&c&&_))return null;for(var f=Math.round(8*t/c),h=1==l?384:2==l||1==i?1152:576,u=h/_*1e3,m=Math.floor(h*c/8/_*1e3),p=0,b=0,d=0;d<e.length;d++){var v=e[d];if((b+=v.byteLength)>=m+3){var g=new Uint8Array(v);p="1"==s(v.byteLength-(b-(m+3)+1),g).charAt(6);break}}return p&&m++,{version:i,layer:l,sampleRate:_,bitRate:c,duration:f,size:t,hasPadding:p,frameSize:m,frameDurationFloat:u}};var n={rm:Recorder.mp3ReadMeta,fn:function(e,t,a,n){var s=this.rm(e,t);if(!s)return{err:"mp3非预定格式"};var r=Math.round(a/n*1e3),o=Math.floor((s.duration-r)/s.frameDurationFloat);if(o>0){var i=o*s.frameSize-(s.hasPadding?1:0);t-=i;for(var l=0,_=[],c=0;c<e.length;c++){var f=e[c];if(i<=0)break;i>=f.byteLength?(i-=f.byteLength,_.push(f),e.splice(c,1),c--):(e[c]=f.slice(i),l=f,i=0)}if(!this.rm(e,t)){l&&(e[0]=l);for(c=0;c<_.length;c++)e.splice(c,0,_[c]);s.err="fix后数据错误，已还原，错误原因不明"}var h=s.trimFix={};h.remove=o,h.removeDuration=Math.round(o*s.frameDurationFloat),h.duration=Math.round(8*t/s.bitRate)}return s}},s=function(e,t){var a="MP3信息 ";(e.sampleRate&&e.sampleRate!=t.sampleRate||e.bitRate&&e.bitRate!=t.bitRate)&&(Recorder.CLog(a+"和设置的不匹配set:"+t.bitRate+"kbps "+t.sampleRate+"hz，已更新set:"+e.bitRate+"kbps "+e.sampleRate+"hz",3,t),t.sampleRate=e.sampleRate,t.bitRate=e.bitRate);var n=e.trimFix;n?(a+="Fix移除"+n.remove+"帧"+n.removeDuration+"ms -> "+n.duration+"ms",n.remove>2&&(e.err=(e.err?e.err+", ":"")+"移除帧数过多")):a+=(e.duration||"-")+"ms",e.err?Recorder.CLog(a,1,e.err,e):Recorder.CLog(a,e)}}();const X=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));!function(){function e(){var t=function(e){return Math.log(e)/Math.log(10)};function a(e){return new Int8Array(e)}function n(e){return new Int32Array(e)}function s(e){return new Float32Array(e)}function r(e){if(1==e.length)return s(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(r(e));return a}function o(e){if(1==e.length)return n(e[0]);var t=e[0];e=e.slice(1);for(var a=[],s=0;s<t;s++)a.push(o(e));return a}function i(e){if(1==e.length)return t=e[0],new Int16Array(t);var t,a=e[0];e=e.slice(1);for(var n=[],s=0;s<a;s++)n.push(i(e));return n}function l(e){if(1==e.length)return new Array(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(l(e));return a}var _={fill:function(e,t,a,n){if(2==arguments.length)for(var s=0;s<e.length;s++)e[s]=arguments[1];else for(s=t;s<a;s++)e[s]=n}},c={arraycopy:function(e,t,a,n,s){for(var r=t+s;t<r;)a[n++]=e[t++]}},f={};function h(e){this.ordinal=e}f.SQRT2=1.4142135623730951,f.FAST_LOG10=function(e){return t(e)},f.FAST_LOG10_X=function(e,a){return t(e)*a},h.short_block_allowed=new h(0),h.short_block_coupled=new h(1),h.short_block_dispensed=new h(2),h.short_block_forced=new h(3);var u={};function m(e){this.ordinal=e}function p(e){var t=e;this.ordinal=function(){return t}}function b(){this.getLameVersion=function(){return"3.98.4"},this.getLameShortVersion=function(){return"3.98.4"},this.getLameVeryShortVersion=function(){return"LAME3.98r"},this.getPsyVersion=function(){return"0.93"},this.getLameUrl=function(){return"http://www.mp3dev.org/"},this.getLameOsBitness=function(){return"32bits"}}function d(){var e=null;function t(e){this.bits=0|e}this.qupvt=null,this.setModules=function(t){this.qupvt=t,e=t};var a=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]];function s(e,t,a,n,s,r){var o=.5946/t;for(e>>=1;0!=e--;)s[r++]=o>a[n++]?0:1,s[r++]=o>a[n++]?0:1}function r(t,a,n,s,r,o){var i=(t>>=1)%2;for(t>>=1;0!=t--;){var l,_,c,f,h,u,m,p;l=n[s++]*a,_=n[s++]*a,h=0|l,c=n[s++]*a,u=0|_,f=n[s++]*a,m=0|c,l+=e.adj43[h],p=0|f,_+=e.adj43[u],r[o++]=0|l,c+=e.adj43[m],r[o++]=0|_,f+=e.adj43[p],r[o++]=0|c,r[o++]=0|f}0!=i&&(h=0|(l=n[s++]*a),u=0|(_=n[s++]*a),l+=e.adj43[h],_+=e.adj43[u],r[o++]=0|l,r[o++]=0|_)}var o=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];function i(e,t,a,n){var s=function(e,t,a){var n=0,s=0;do{var r=e[t++],o=e[t++];n<r&&(n=r),s<o&&(s=o)}while(t<a);return n<s&&(n=s),n}(e,t,a);switch(s){case 0:return s;case 1:return function(e,t,a,n){var s=0,r=A.ht[1].hlen;do{var o=2*e[t+0]+e[t+1];t+=2,s+=r[o]}while(t<a);return n.bits+=s,1}(e,t,a,n);case 2:case 3:return function(e,t,a,n,s){var r,o,i=0,l=A.ht[n].xlen;o=2==n?A.table23:A.table56;do{var _=e[t+0]*l+e[t+1];t+=2,i+=o[_]}while(t<a);return r=65535&i,(i>>=16)>r&&(i=r,n++),s.bits+=i,n}(e,t,a,o[s-1],n);case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:return function(e,t,a,n,s){var r=0,o=0,i=0,l=A.ht[n].xlen,_=A.ht[n].hlen,c=A.ht[n+1].hlen,f=A.ht[n+2].hlen;do{var h=e[t+0]*l+e[t+1];t+=2,r+=_[h],o+=c[h],i+=f[h]}while(t<a);var u=n;return r>o&&(r=o,u++),r>i&&(r=i,u=n+2),s.bits+=r,u}(e,t,a,o[s-1],n);default:if(s>L.IXMAX_VAL)return n.bits=L.LARGE_BITS,-1;var r,i;for(s-=15,r=24;r<32&&!(A.ht[r].linmax>=s);r++);for(i=r-8;i<24&&!(A.ht[i].linmax>=s);i++);return function(e,t,a,n,s,r){var o,i=65536*A.ht[n].xlen+A.ht[s].xlen,l=0;do{var _=e[t++],c=e[t++];0!=_&&(_>14&&(_=15,l+=i),_*=16),0!=c&&(c>14&&(c=15,l+=i),_+=c),l+=A.largetbl[_]}while(t<a);return o=65535&l,(l>>=16)>o&&(l=o,n=s),r.bits+=l,n}(e,t,a,i,r,n)}}function l(e,a,n,s,r,o,l,_){for(var c=a.big_values,f=2;f<Y.SBMAX_l+1;f++){var h=e.scalefac_band.l[f];if(h>=c)break;var u=r[f-2]+a.count1bits;if(n.part2_3_length<=u)break;var m=new t(u),p=i(s,h,c,m);u=m.bits,n.part2_3_length<=u||(n.assign(a),n.part2_3_length=u,n.region0_count=o[f-2],n.region1_count=f-2-o[f-2],n.table_select[0]=l[f-2],n.table_select[1]=_[f-2],n.table_select[2]=p)}}this.noquant_count_bits=function(e,a,n){var s=a.l3_enc,r=Math.min(576,a.max_nonzero_coeff+2>>1<<1);for(null!=n&&(n.sfb_count1=0);r>1&&0==(s[r-1]|s[r-2]);r-=2);a.count1=r;for(var o=0,l=0;r>3;r-=4){var _;if((2147483647&(s[r-1]|s[r-2]|s[r-3]|s[r-4]))>1)break;_=2*(2*(2*s[r-4]+s[r-3])+s[r-2])+s[r-1],o+=A.t32l[_],l+=A.t33l[_]}var c=o;if(a.count1table_select=0,o>l&&(c=l,a.count1table_select=1),a.count1bits=c,a.big_values=r,0==r)return c;if(a.block_type==Y.SHORT_TYPE)(o=3*e.scalefac_band.s[3])>a.big_values&&(o=a.big_values),l=a.big_values;else if(a.block_type==Y.NORM_TYPE){if(o=a.region0_count=e.bv_scf[r-2],l=a.region1_count=e.bv_scf[r-1],l=e.scalefac_band.l[o+l+2],o=e.scalefac_band.l[o+1],l<r){var f=new t(c);a.table_select[2]=i(s,l,r,f),c=f.bits}}else a.region0_count=7,a.region1_count=Y.SBMAX_l-1-7-1,(o=e.scalefac_band.l[8])>(l=r)&&(o=l);if(o=Math.min(o,r),l=Math.min(l,r),0<o){f=new t(c);a.table_select[0]=i(s,0,o,f),c=f.bits}if(o<l){f=new t(c);a.table_select[1]=i(s,o,l,f),c=f.bits}if(2==e.use_best_huffman&&(a.part2_3_length=c,best_huffman_divide(e,a),c=a.part2_3_length),null!=n&&a.block_type==Y.NORM_TYPE){for(var h=0;e.scalefac_band.l[h]<a.big_values;)h++;n.sfb_count1=h}return c},this.count_bits=function(t,a,n,o){var i=n.l3_enc,l=L.IXMAX_VAL/e.IPOW20(n.global_gain);if(n.xrpow_max>l)return L.LARGE_BITS;if(function(t,a,n,o,i){var l,c,f,h=0,u=0,m=0,p=0,b=a,d=0,v=b,g=0,S=t,w=0;for(f=null!=i&&o.global_gain==i.global_gain,c=o.block_type==Y.SHORT_TYPE?38:21,l=0;l<=c;l++){var R=-1;if((f||o.block_type==Y.NORM_TYPE)&&(R=o.global_gain-(o.scalefac[l]+(0!=o.preflag?e.pretab[l]:0)<<o.scalefac_scale+1)-8*o.subblock_gain[o.window[l]]),f&&i.step[l]==R)0!=u&&(r(u,n,S,w,v,g),u=0),0!=m&&(s(m,n,S,w,v,g),m=0);else{var M,A=o.width[l];if(h+o.width[l]>o.max_nonzero_coeff&&(M=o.max_nonzero_coeff-h+1,_.fill(a,o.max_nonzero_coeff,576,0),(A=M)<0&&(A=0),l=c+1),0==u&&0==m&&(v=b,g=d,S=t,w=p),null!=i&&i.sfb_count1>0&&l>=i.sfb_count1&&i.step[l]>0&&R>=i.step[l]?(0!=u&&(r(u,n,S,w,v,g),u=0,v=b,g=d,S=t,w=p),m+=A):(0!=m&&(s(m,n,S,w,v,g),m=0,v=b,g=d,S=t,w=p),u+=A),A<=0){0!=m&&(s(m,n,S,w,v,g),m=0),0!=u&&(r(u,n,S,w,v,g),u=0);break}}l<=c&&(d+=o.width[l],p+=o.width[l],h+=o.width[l])}0!=u&&(r(u,n,S,w,v,g),u=0),0!=m&&(s(m,n,S,w,v,g),m=0)}(a,i,e.IPOW20(n.global_gain),n,o),2&t.substep_shaping)for(var c=0,f=n.global_gain+n.scalefac_scale,h=.634521682242439/e.IPOW20(f),u=0;u<n.sfbmax;u++){var m,p=n.width[u];if(0==t.pseudohalf[u])c+=p;else for(m=c,c+=p;m<c;++m)i[m]=a[m]>=h?i[m]:0}return this.noquant_count_bits(t,n,o)},this.best_huffman_divide=function(e,a){var s=new D,r=a.l3_enc,o=n(23),_=n(23),c=n(23),f=n(23);if(a.block_type!=Y.SHORT_TYPE||1!=e.mode_gr){s.assign(a),a.block_type==Y.NORM_TYPE&&(!function(e,a,n,s,r,o,l){for(var _=a.big_values,c=0;c<=22;c++)s[c]=L.LARGE_BITS;for(c=0;c<16;c++){var f=e.scalefac_band.l[c+1];if(f>=_)break;var h=0,u=new t(h),m=i(n,0,f,u);h=u.bits;for(var p=0;p<8;p++){var b=e.scalefac_band.l[c+p+2];if(b>=_)break;var d=h,v=i(n,f,b,u=new t(d));d=u.bits,s[c+p]>d&&(s[c+p]=d,r[c+p]=c,o[c+p]=m,l[c+p]=v)}}}(e,a,r,o,_,c,f),l(e,s,a,r,o,_,c,f));var h=s.big_values;if(!(0==h||(r[h-2]|r[h-1])>1||(h=a.count1+2)>576)){s.assign(a),s.count1=h;for(var u=0,m=0;h>s.big_values;h-=4){var p=2*(2*(2*r[h-4]+r[h-3])+r[h-2])+r[h-1];u+=A.t32l[p],m+=A.t33l[p]}if(s.big_values=h,s.count1table_select=0,u>m&&(u=m,s.count1table_select=1),s.count1bits=u,s.block_type==Y.NORM_TYPE)l(e,s,a,r,o,_,c,f);else{if(s.part2_3_length=u,(u=e.scalefac_band.l[8])>h&&(u=h),u>0){var b=new t(s.part2_3_length);s.table_select[0]=i(r,0,u,b),s.part2_3_length=b.bits}if(h>u){b=new t(s.part2_3_length);s.table_select[1]=i(r,u,h,b),s.part2_3_length=b.bits}a.part2_3_length>s.part2_3_length&&a.assign(s)}}}};var f=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],h=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],u=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],m=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];d.slen1_tab=u,d.slen2_tab=m,this.best_scalefac_store=function(t,a,n,s){var r,o,i,l,_=s.tt[a][n],c=0;for(i=0,r=0;r<_.sfbmax;r++){var p=_.width[r];for(i+=p,l=-p;l<0&&0==_.l3_enc[l+i];l++);0==l&&(_.scalefac[r]=c=-2)}if(0==_.scalefac_scale&&0==_.preflag){var b=0;for(r=0;r<_.sfbmax;r++)_.scalefac[r]>0&&(b|=_.scalefac[r]);if(!(1&b)&&0!=b){for(r=0;r<_.sfbmax;r++)_.scalefac[r]>0&&(_.scalefac[r]>>=1);_.scalefac_scale=c=1}}if(0==_.preflag&&_.block_type!=Y.SHORT_TYPE&&2==t.mode_gr){for(r=11;r<Y.SBPSY_l&&!(_.scalefac[r]<e.pretab[r]&&-2!=_.scalefac[r]);r++);if(r==Y.SBPSY_l){for(r=11;r<Y.SBPSY_l;r++)_.scalefac[r]>0&&(_.scalefac[r]-=e.pretab[r]);_.preflag=c=1}}for(o=0;o<4;o++)s.scfsi[n][o]=0;for(2==t.mode_gr&&1==a&&s.tt[0][n].block_type!=Y.SHORT_TYPE&&s.tt[1][n].block_type!=Y.SHORT_TYPE&&(!function(e,t){for(var a,n=t.tt[1][e],s=t.tt[0][e],r=0;r<A.scfsi_band.length-1;r++){for(a=A.scfsi_band[r];a<A.scfsi_band[r+1]&&!(s.scalefac[a]!=n.scalefac[a]&&n.scalefac[a]>=0);a++);if(a==A.scfsi_band[r+1]){for(a=A.scfsi_band[r];a<A.scfsi_band[r+1];a++)n.scalefac[a]=-1;t.scfsi[e][r]=1}}var o=0,i=0;for(a=0;a<11;a++)-1!=n.scalefac[a]&&(i++,o<n.scalefac[a]&&(o=n.scalefac[a]));for(var l=0,_=0;a<Y.SBPSY_l;a++)-1!=n.scalefac[a]&&(_++,l<n.scalefac[a]&&(l=n.scalefac[a]));for(r=0;r<16;r++)if(o<f[r]&&l<h[r]){var c=u[r]*i+m[r]*_;n.part2_length>c&&(n.part2_length=c,n.scalefac_compress=r)}}(n,s),c=0),r=0;r<_.sfbmax;r++)-2==_.scalefac[r]&&(_.scalefac[r]=0);0!=c&&(2==t.mode_gr?this.scale_bitcount(_):this.scale_bitcount_lsf(t,_))};var p=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],b=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],v=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(t){var a,n,s,r=0,o=0,i=t.scalefac;if(t.block_type==Y.SHORT_TYPE)s=p,0!=t.mixed_block_flag&&(s=b);else if(s=v,0==t.preflag){for(n=11;n<Y.SBPSY_l&&!(i[n]<e.pretab[n]);n++);if(n==Y.SBPSY_l)for(t.preflag=1,n=11;n<Y.SBPSY_l;n++)i[n]-=e.pretab[n]}for(n=0;n<t.sfbdivide;n++)r<i[n]&&(r=i[n]);for(;n<t.sfbmax;n++)o<i[n]&&(o=i[n]);for(t.part2_length=L.LARGE_BITS,a=0;a<16;a++)r<f[a]&&o<h[a]&&t.part2_length>s[a]&&(t.part2_length=s[a],t.scalefac_compress=a);return t.part2_length==L.LARGE_BITS};var g=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(t,a){var s,r,o,i,l,_,f,h,u=n(4),m=a.scalefac;for(s=0!=a.preflag?2:0,f=0;f<4;f++)u[f]=0;if(a.block_type==Y.SHORT_TYPE){r=1;var p=e.nr_of_sfb_block[s][r];for(h=0,o=0;o<4;o++)for(i=p[o]/3,f=0;f<i;f++,h++)for(l=0;l<3;l++)m[3*h+l]>u[o]&&(u[o]=m[3*h+l])}else{r=0;p=e.nr_of_sfb_block[s][r];for(h=0,o=0;o<4;o++)for(i=p[o],f=0;f<i;f++,h++)m[h]>u[o]&&(u[o]=m[h])}for(_=!1,o=0;o<4;o++)u[o]>g[s][o]&&(_=!0);if(!_){var b,d,v,w;for(a.sfb_partition_table=e.nr_of_sfb_block[s][r],o=0;o<4;o++)a.slen[o]=S[u[o]];switch(b=a.slen[0],d=a.slen[1],v=a.slen[2],w=a.slen[3],s){case 0:a.scalefac_compress=(5*b+d<<4)+(v<<2)+w;break;case 1:a.scalefac_compress=400+(5*b+d<<2)+v;break;case 2:a.scalefac_compress=500+3*b+d;break;default:c.err.printf("intensity stereo not implemented yet\n")}}if(!_)for(a.part2_length=0,o=0;o<4;o++)a.part2_length+=a.slen[o]*a.sfb_partition_table[o];return _};var S=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(e){for(var t=2;t<=576;t+=2){for(var n,s=0;e.scalefac_band.l[++s]<t;);for(n=a[s][0];e.scalefac_band.l[n+1]>t;)n--;for(n<0&&(n=a[s][0]),e.bv_scf[t-2]=n,n=a[s][1];e.scalefac_band.l[n+e.bv_scf[t-2]+2]>t;)n--;n<0&&(n=a[s][1]),e.bv_scf[t-1]=n}}}function v(){}function g(){function e(e,t,a,n,s,r,o,i,l,_,c,f,h,u,m){this.vbr_q=e,this.quant_comp=t,this.quant_comp_s=a,this.expY=n,this.st_lrm=s,this.st_s=r,this.masking_adj=o,this.masking_adj_short=i,this.ath_lower=l,this.ath_curve=_,this.ath_sensitivity=c,this.interch=f,this.safejoint=h,this.sfb21mod=u,this.msfix=m}function t(e,t,a,n,s,r,o,i,l,_,c,f,h,u){this.quant_comp=t,this.quant_comp_s=a,this.safejoint=n,this.nsmsfix=s,this.st_lrm=r,this.st_s=o,this.nsbass=i,this.scale=l,this.masking_adj=_,this.ath_lower=c,this.ath_curve=f,this.interch=h,this.sfscale=u}var a;this.setModules=function(e){a=e};var n=[new e(0,9,9,0,5.2,125,-4.2,-6.3,4.8,1,0,0,2,21,.97),new e(1,9,9,0,5.3,125,-3.6,-5.6,4.5,1.5,0,0,2,21,1.35),new e(2,9,9,0,5.6,125,-2.2,-3.5,2.8,2,0,0,2,21,1.49),new e(3,9,9,1,5.8,130,-1.8,-2.8,2.6,3,-4,0,2,20,1.64),new e(4,9,9,1,6,135,-.7,-1.1,1.1,3.5,-8,0,2,0,1.79),new e(5,9,9,1,6.4,140,.5,.4,-7.5,4,-12,2e-4,0,0,1.95),new e(6,9,9,1,6.6,145,.67,.65,-14.7,6.5,-19,4e-4,0,0,2.3),new e(7,9,9,1,6.6,145,.8,.75,-19.7,8,-22,6e-4,0,0,2.7),new e(8,9,9,1,6.6,145,1.2,1.15,-27.5,10,-23,7e-4,0,0,0),new e(9,9,9,1,6.6,145,1.6,1.6,-36,11,-25,8e-4,0,0,0),new e(10,9,9,1,6.6,145,2,2,-36,12,-25,8e-4,0,0,0)],s=[new e(0,9,9,0,4.2,25,-7,-4,7.5,1,0,0,2,26,.97),new e(1,9,9,0,4.2,25,-5.6,-3.6,4.5,1.5,0,0,2,21,1.35),new e(2,9,9,0,4.2,25,-4.4,-1.8,2,2,0,0,2,18,1.49),new e(3,9,9,1,4.2,25,-3.4,-1.25,1.1,3,-4,0,2,15,1.64),new e(4,9,9,1,4.2,25,-2.2,.1,0,3.5,-8,0,2,0,1.79),new e(5,9,9,1,4.2,25,-1,1.65,-7.7,4,-12,2e-4,0,0,1.95),new e(6,9,9,1,4.2,25,-0,2.47,-7.7,6.5,-19,4e-4,0,0,2),new e(7,9,9,1,4.2,25,.5,2,-14.5,8,-22,6e-4,0,0,2),new e(8,9,9,1,4.2,25,1,2.4,-22,10,-23,7e-4,0,0,2),new e(9,9,9,1,4.2,25,1.5,2.95,-30,11,-25,8e-4,0,0,2),new e(10,9,9,1,4.2,25,2,2.95,-36,12,-30,8e-4,0,0,2)];function r(e,t,a){var r=e.VBR==m.vbr_rh?n:s,o=e.VBR_q_frac,i=r[t],l=r[t+1],_=i;i.st_lrm=i.st_lrm+o*(l.st_lrm-i.st_lrm),i.st_s=i.st_s+o*(l.st_s-i.st_s),i.masking_adj=i.masking_adj+o*(l.masking_adj-i.masking_adj),i.masking_adj_short=i.masking_adj_short+o*(l.masking_adj_short-i.masking_adj_short),i.ath_lower=i.ath_lower+o*(l.ath_lower-i.ath_lower),i.ath_curve=i.ath_curve+o*(l.ath_curve-i.ath_curve),i.ath_sensitivity=i.ath_sensitivity+o*(l.ath_sensitivity-i.ath_sensitivity),i.interch=i.interch+o*(l.interch-i.interch),i.msfix=i.msfix+o*(l.msfix-i.msfix),function(e,t){var a=0;0>t&&(a=-1,t=0);9<t&&(a=-1,t=9);e.VBR_q=t,e.VBR_q_frac=0}(e,_.vbr_q),0!=a?e.quant_comp=_.quant_comp:Math.abs(e.quant_comp- -1)>0||(e.quant_comp=_.quant_comp),0!=a?e.quant_comp_short=_.quant_comp_s:Math.abs(e.quant_comp_short- -1)>0||(e.quant_comp_short=_.quant_comp_s),0!=_.expY&&(e.experimentalY=0!=_.expY),0!=a?e.internal_flags.nsPsy.attackthre=_.st_lrm:Math.abs(e.internal_flags.nsPsy.attackthre- -1)>0||(e.internal_flags.nsPsy.attackthre=_.st_lrm),0!=a?e.internal_flags.nsPsy.attackthre_s=_.st_s:Math.abs(e.internal_flags.nsPsy.attackthre_s- -1)>0||(e.internal_flags.nsPsy.attackthre_s=_.st_s),0!=a?e.maskingadjust=_.masking_adj:Math.abs(e.maskingadjust-0)>0||(e.maskingadjust=_.masking_adj),0!=a?e.maskingadjust_short=_.masking_adj_short:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=_.masking_adj_short),0!=a?e.ATHlower=-_.ath_lower/10:Math.abs(10*-e.ATHlower-0)>0||(e.ATHlower=-_.ath_lower/10),0!=a?e.ATHcurve=_.ath_curve:Math.abs(e.ATHcurve- -1)>0||(e.ATHcurve=_.ath_curve),0!=a?e.athaa_sensitivity=_.ath_sensitivity:Math.abs(e.athaa_sensitivity- -1)>0||(e.athaa_sensitivity=_.ath_sensitivity),_.interch>0&&(0!=a?e.interChRatio=_.interch:Math.abs(e.interChRatio- -1)>0||(e.interChRatio=_.interch)),_.safejoint>0&&(e.exp_nspsytune=e.exp_nspsytune|_.safejoint),_.sfb21mod>0&&(e.exp_nspsytune=e.exp_nspsytune|_.sfb21mod<<20),0!=a?e.msfix=_.msfix:Math.abs(e.msfix- -1)>0||(e.msfix=_.msfix),0==a&&(e.VBR_q=t,e.VBR_q_frac=o)}var o=[new t(8,9,9,0,0,6.6,145,0,.95,0,-30,11,.0012,1),new t(16,9,9,0,0,6.6,145,0,.95,0,-25,11,.001,1),new t(24,9,9,0,0,6.6,145,0,.95,0,-20,11,.001,1),new t(32,9,9,0,0,6.6,145,0,.95,0,-15,11,.001,1),new t(40,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new t(48,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new t(56,9,9,0,0,6.6,145,0,.95,0,-6,11,8e-4,1),new t(64,9,9,0,0,6.6,145,0,.95,0,-2,11,8e-4,1),new t(80,9,9,0,0,6.6,145,0,.95,0,0,8,7e-4,1),new t(96,9,9,0,2.5,6.6,145,0,.95,0,1,5.5,6e-4,1),new t(112,9,9,0,2.25,6.6,145,0,.95,0,2,4.5,5e-4,1),new t(128,9,9,0,1.95,6.4,140,0,.95,0,3,4,2e-4,1),new t(160,9,9,1,1.79,6,135,0,.95,-2,5,3.5,0,1),new t(192,9,9,1,1.49,5.6,125,0,.97,-4,7,3,0,0),new t(224,9,9,1,1.25,5.2,125,0,.98,-6,9,2,0,0),new t(256,9,9,1,.97,5.2,125,0,1,-8,10,1,0,0),new t(320,9,9,1,.9,5.2,125,0,1,-10,12,0,0,0)];function i(e,t,n){var s=t,r=a.nearestBitrateFullIndex(t);if(e.VBR=m.vbr_abr,e.VBR_mean_bitrate_kbps=s,e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320),e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.brate=e.VBR_mean_bitrate_kbps,e.VBR_mean_bitrate_kbps>320&&(e.disable_reservoir=!0),o[r].safejoint>0&&(e.exp_nspsytune=2|e.exp_nspsytune),o[r].sfscale>0&&(e.internal_flags.noise_shaping=2),Math.abs(o[r].nsbass)>0){var i=int(4*o[r].nsbass);i<0&&(i+=64),e.exp_nspsytune=e.exp_nspsytune|i<<2}return 0!=n?e.quant_comp=o[r].quant_comp:Math.abs(e.quant_comp- -1)>0||(e.quant_comp=o[r].quant_comp),0!=n?e.quant_comp_short=o[r].quant_comp_s:Math.abs(e.quant_comp_short- -1)>0||(e.quant_comp_short=o[r].quant_comp_s),0!=n?e.msfix=o[r].nsmsfix:Math.abs(e.msfix- -1)>0||(e.msfix=o[r].nsmsfix),0!=n?e.internal_flags.nsPsy.attackthre=o[r].st_lrm:Math.abs(e.internal_flags.nsPsy.attackthre- -1)>0||(e.internal_flags.nsPsy.attackthre=o[r].st_lrm),0!=n?e.internal_flags.nsPsy.attackthre_s=o[r].st_s:Math.abs(e.internal_flags.nsPsy.attackthre_s- -1)>0||(e.internal_flags.nsPsy.attackthre_s=o[r].st_s),0!=n?e.scale=o[r].scale:Math.abs(e.scale- -1)>0||(e.scale=o[r].scale),0!=n?e.maskingadjust=o[r].masking_adj:Math.abs(e.maskingadjust-0)>0||(e.maskingadjust=o[r].masking_adj),o[r].masking_adj>0?0!=n?e.maskingadjust_short=.9*o[r].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=.9*o[r].masking_adj):0!=n?e.maskingadjust_short=1.1*o[r].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=1.1*o[r].masking_adj),0!=n?e.ATHlower=-o[r].ath_lower/10:Math.abs(10*-e.ATHlower-0)>0||(e.ATHlower=-o[r].ath_lower/10),0!=n?e.ATHcurve=o[r].ath_curve:Math.abs(e.ATHcurve- -1)>0||(e.ATHcurve=o[r].ath_curve),0!=n?e.interChRatio=o[r].interch:Math.abs(e.interChRatio- -1)>0||(e.interChRatio=o[r].interch),t}this.apply_preset=function(e,t,a){switch(t){case K.R3MIX:t=K.V3,e.VBR=m.vbr_mtrh;break;case K.MEDIUM:t=K.V4,e.VBR=m.vbr_rh;break;case K.MEDIUM_FAST:t=K.V4,e.VBR=m.vbr_mtrh;break;case K.STANDARD:t=K.V2,e.VBR=m.vbr_rh;break;case K.STANDARD_FAST:t=K.V2,e.VBR=m.vbr_mtrh;break;case K.EXTREME:t=K.V0,e.VBR=m.vbr_rh;break;case K.EXTREME_FAST:t=K.V0,e.VBR=m.vbr_mtrh;break;case K.INSANE:return t=320,e.preset=t,i(e,t,a),e.VBR=m.vbr_off,t}switch(e.preset=t,t){case K.V9:return r(e,9,a),t;case K.V8:return r(e,8,a),t;case K.V7:return r(e,7,a),t;case K.V6:return r(e,6,a),t;case K.V5:return r(e,5,a),t;case K.V4:return r(e,4,a),t;case K.V3:return r(e,3,a),t;case K.V2:return r(e,2,a),t;case K.V1:return r(e,1,a),t;case K.V0:return r(e,0,a),t}return 8<=t&&t<=320?i(e,t,a):(e.preset=0,t)}}function S(){var e;this.setModules=function(t){e=t},this.ResvFrameBegin=function(t,a){var n,s=t.internal_flags,r=s.l3_side,o=e.getframebits(t);a.bits=(o-8*s.sideinfo_len)/s.mode_gr;var i=2048*s.mode_gr-8;t.brate>320?n=8*int(1e3*t.brate/(t.out_samplerate/1152)/8+.5):(n=11520,t.strict_ISO&&(n=8*int(32e4/(t.out_samplerate/1152)/8+.5))),s.ResvMax=n-o,s.ResvMax>i&&(s.ResvMax=i),(s.ResvMax<0||t.disable_reservoir)&&(s.ResvMax=0);var l=a.bits*s.mode_gr+Math.min(s.ResvSize,s.ResvMax);return l>n&&(l=n),r.resvDrain_pre=0,null!=s.pinfo&&(s.pinfo.mean_bits=a.bits/2,s.pinfo.resvsize=s.ResvSize),l},this.ResvMaxBits=function(e,t,a,n){var s,r=e.internal_flags,o=r.ResvSize,i=r.ResvMax;0!=n&&(o+=t),1&r.substep_shaping&&(i*=.9),a.bits=t,10*o>9*i?(s=o-9*i/10,a.bits+=s,r.substep_shaping|=128):(s=0,r.substep_shaping&=127,e.disable_reservoir||1&r.substep_shaping||(a.bits-=.1*t));var l=o<6*r.ResvMax/10?o:6*r.ResvMax/10;return(l-=s)<0&&(l=0),l},this.ResvAdjust=function(e,t){e.ResvSize-=t.part2_3_length+t.part2_length},this.ResvFrameEnd=function(e,t){var a,n=e.l3_side;e.ResvSize+=t*e.mode_gr;var s=0;n.resvDrain_post=0,n.resvDrain_pre=0,0!=(a=e.ResvSize%8)&&(s+=a),(a=e.ResvSize-s-e.ResvMax)>0&&(s+=a);var r=Math.min(8*n.main_data_begin,s)/8;n.resvDrain_pre+=8*r,s-=8*r,e.ResvSize-=8*r,n.main_data_begin-=r,n.resvDrain_post+=s,e.ResvSize-=s}}function w(){this.setModules=function(e,t,a){};var e=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];function t(t,a){return a=a>>8^e[255&(a^t)]}this.updateMusicCRC=function(e,a,n,s){for(var r=0;r<s;++r)e[0]=t(a[n+r],e[0])}}function R(){var e=this,s=null,o=null,i=null,l=null;this.setModules=function(e,t,a,n){s=e,o=t,i=a,l=n};var f=null,h=0,u=0,m=0;function p(e){c.arraycopy(e.header[e.w_ptr].buf,0,f,u,e.sideinfo_len),u+=e.sideinfo_len,h+=8*e.sideinfo_len,e.w_ptr=e.w_ptr+1&F.MAX_HEADER_BUF-1}function b(e,t,a){for(;a>0;){var n;0==m&&(m=8,u++,e.header[e.w_ptr].write_timing==h&&p(e),f[u]=0),a-=n=Math.min(a,m),m-=n,f[u]|=t>>a<<m,h+=n}}function g(e,t,a){for(;a>0;){var n;0==m&&(m=8,u++,f[u]=0),a-=n=Math.min(a,m),m-=n,f[u]|=t>>a<<m,h+=n}}function S(e,t){var a,n=e.internal_flags;if(t>=8&&(b(n,76,8),t-=8),t>=8&&(b(n,65,8),t-=8),t>=8&&(b(n,77,8),t-=8),t>=8&&(b(n,69,8),t-=8),t>=32){var s=i.getLameShortVersion();if(t>=32)for(a=0;a<s.length&&t>=8;++a)t-=8,b(n,s.charCodeAt(a),8)}for(;t>=1;t-=1)b(n,n.ancillary_flag,1),n.ancillary_flag^=e.disable_reservoir?0:1}function w(e,t,a){for(var n=e.header[e.h_ptr].ptr;a>0;){var s=Math.min(a,8-(7&n));a-=s,e.header[e.h_ptr].buf[n>>3]|=t>>a<<8-(7&n)-s,n+=s}e.header[e.h_ptr].ptr=n}function R(e,t){e<<=8;for(var a=0;a<8;a++)65536&((t<<=1)^(e<<=1))&&(t^=32773);return t}function M(e,t){var a,n=A.ht[t.count1table_select+32],s=0,r=t.big_values,o=t.big_values;for(a=(t.count1-t.big_values)/4;a>0;--a){var i=0,l=0;0!=t.l3_enc[r+0]&&(l+=8,t.xr[o+0]<0&&i++),0!=t.l3_enc[r+1]&&(l+=4,i*=2,t.xr[o+1]<0&&i++),0!=t.l3_enc[r+2]&&(l+=2,i*=2,t.xr[o+2]<0&&i++),0!=t.l3_enc[r+3]&&(l++,i*=2,t.xr[o+3]<0&&i++),r+=4,o+=4,b(e,i+n.table[l],n.hlen[l]),s+=n.hlen[l]}return s}function k(e,t,a,n,s){var r=A.ht[t],o=0;if(0==t)return o;for(var i=a;i<n;i+=2){var l=0,_=0,c=r.xlen,f=r.xlen,h=0,u=s.l3_enc[i],m=s.l3_enc[i+1];if(0!=u&&(s.xr[i]<0&&h++,l--),t>15){if(u>14)h|=u-15<<1,_=c,u=15;if(m>14)h<<=c,h|=m-15,_+=c,m=15;f=16}0!=m&&(h<<=1,s.xr[i+1]<0&&h++,l--),u=u*f+m,_-=l,l+=r.hlen[u],b(e,r.table[u],l),b(e,h,_),o+=l+_}return o}function y(e,t){var a=3*e.scalefac_band.s[3];a>t.big_values&&(a=t.big_values);var n=k(e,t.table_select[0],0,a,t);return n+=k(e,t.table_select[1],a,t.big_values,t)}function T(e,t){var a,n,s,r;a=t.big_values;var o=t.region0_count+1;return s=e.scalefac_band.l[o],o+=t.region1_count+1,s>a&&(s=a),(r=e.scalefac_band.l[o])>a&&(r=a),n=k(e,t.table_select[0],0,s,t),n+=k(e,t.table_select[1],s,r,t),n+=k(e,t.table_select[2],r,a,t)}function B(){this.total=0}function x(t,a){var n,s,r,o,i,l=t.internal_flags;return i=l.w_ptr,-1==(o=l.h_ptr-1)&&(o=F.MAX_HEADER_BUF-1),n=l.header[o].write_timing-h,a.total=n,n>=0&&(s=1+o-i,o<i&&(s=1+o-i+F.MAX_HEADER_BUF),n-=8*s*l.sideinfo_len),n+=r=e.getframebits(t),a.total+=r,a.total%8!=0?a.total=1+a.total/8:a.total=a.total/8,a.total+=u+1,n<0&&c.err.println("strange error flushing buffer ... \n"),n}this.getframebits=function(e){var t,a=e.internal_flags;return t=0!=a.bitrate_index?A.bitrate_table[e.version][a.bitrate_index]:e.brate,8*(0|72e3*(e.version+1)*t/e.out_samplerate+a.padding)},this.CRC_writeheader=function(e,t){var a=65535;a=R(255&t[2],a),a=R(255&t[3],a);for(var n=6;n<e.sideinfo_len;n++)a=R(255&t[n],a);t[4]=byte(a>>8),t[5]=byte(255&a)},this.flush_bitstream=function(e){var a,n,r=e.internal_flags;if(r.h_ptr,a=r.l3_side,!((n=x(e,new B))<0)){if(S(e,n),r.ResvSize=0,a.main_data_begin=0,r.findReplayGain){var o=s.GetTitleGain(r.rgdata);r.RadioGain=0|Math.floor(10*o+.5)}r.findPeakSample&&(r.noclipGainChange=0|Math.ceil(20*t(r.PeakSample/32767)*10),r.noclipGainChange>0&&(EQ(e.scale,1)||EQ(e.scale,0))?r.noclipScale=Math.floor(32767/r.PeakSample*100)/100:r.noclipScale=-1)}},this.add_dummy_byte=function(e,t,a){for(var n,s=e.internal_flags;a-- >0;)for(g(0,t,8),n=0;n<F.MAX_HEADER_BUF;++n)s.header[n].write_timing+=8},this.format_bitstream=function(e){var t,a=e.internal_flags;t=a.l3_side;var n=this.getframebits(e);S(e,t.resvDrain_pre),function(e,t){var a,n,s,r=e.internal_flags;if(a=r.l3_side,r.header[r.h_ptr].ptr=0,_.fill(r.header[r.h_ptr].buf,0,r.sideinfo_len,0),e.out_samplerate<16e3?w(r,4094,12):w(r,4095,12),w(r,e.version,1),w(r,1,2),w(r,e.error_protection?0:1,1),w(r,r.bitrate_index,4),w(r,r.samplerate_index,2),w(r,r.padding,1),w(r,e.extension,1),w(r,e.mode.ordinal(),2),w(r,r.mode_ext,2),w(r,e.copyright,1),w(r,e.original,1),w(r,e.emphasis,2),e.error_protection&&w(r,0,16),1==e.version){for(w(r,a.main_data_begin,9),2==r.channels_out?w(r,a.private_bits,3):w(r,a.private_bits,5),s=0;s<r.channels_out;s++){var o;for(o=0;o<4;o++)w(r,a.scfsi[s][o],1)}for(n=0;n<2;n++)for(s=0;s<r.channels_out;s++)w(r,(i=a.tt[n][s]).part2_3_length+i.part2_length,12),w(r,i.big_values/2,9),w(r,i.global_gain,8),w(r,i.scalefac_compress,4),i.block_type!=Y.NORM_TYPE?(w(r,1,1),w(r,i.block_type,2),w(r,i.mixed_block_flag,1),14==i.table_select[0]&&(i.table_select[0]=16),w(r,i.table_select[0],5),14==i.table_select[1]&&(i.table_select[1]=16),w(r,i.table_select[1],5),w(r,i.subblock_gain[0],3),w(r,i.subblock_gain[1],3),w(r,i.subblock_gain[2],3)):(w(r,0,1),14==i.table_select[0]&&(i.table_select[0]=16),w(r,i.table_select[0],5),14==i.table_select[1]&&(i.table_select[1]=16),w(r,i.table_select[1],5),14==i.table_select[2]&&(i.table_select[2]=16),w(r,i.table_select[2],5),w(r,i.region0_count,4),w(r,i.region1_count,3)),w(r,i.preflag,1),w(r,i.scalefac_scale,1),w(r,i.count1table_select,1)}else for(w(r,a.main_data_begin,8),w(r,a.private_bits,r.channels_out),n=0,s=0;s<r.channels_out;s++){var i;w(r,(i=a.tt[n][s]).part2_3_length+i.part2_length,12),w(r,i.big_values/2,9),w(r,i.global_gain,8),w(r,i.scalefac_compress,9),i.block_type!=Y.NORM_TYPE?(w(r,1,1),w(r,i.block_type,2),w(r,i.mixed_block_flag,1),14==i.table_select[0]&&(i.table_select[0]=16),w(r,i.table_select[0],5),14==i.table_select[1]&&(i.table_select[1]=16),w(r,i.table_select[1],5),w(r,i.subblock_gain[0],3),w(r,i.subblock_gain[1],3),w(r,i.subblock_gain[2],3)):(w(r,0,1),14==i.table_select[0]&&(i.table_select[0]=16),w(r,i.table_select[0],5),14==i.table_select[1]&&(i.table_select[1]=16),w(r,i.table_select[1],5),14==i.table_select[2]&&(i.table_select[2]=16),w(r,i.table_select[2],5),w(r,i.region0_count,4),w(r,i.region1_count,3)),w(r,i.scalefac_scale,1),w(r,i.count1table_select,1)}e.error_protection&&CRC_writeheader(r,r.header[r.h_ptr].buf);var l=r.h_ptr;r.h_ptr=l+1&F.MAX_HEADER_BUF-1,r.header[r.h_ptr].write_timing=r.header[l].write_timing+t,r.h_ptr==r.w_ptr&&c.err.println("Error: MAX_HEADER_BUF too small in bitstream.c \n")}(e,n);var s=8*a.sideinfo_len;if(s+=function(e){var t,a,n,s,r=0,o=e.internal_flags,i=o.l3_side;if(1==e.version)for(t=0;t<2;t++)for(a=0;a<o.channels_out;a++){var l=i.tt[t][a],_=d.slen1_tab[l.scalefac_compress],c=d.slen2_tab[l.scalefac_compress];for(s=0,n=0;n<l.sfbdivide;n++)-1!=l.scalefac[n]&&(b(o,l.scalefac[n],_),s+=_);for(;n<l.sfbmax;n++)-1!=l.scalefac[n]&&(b(o,l.scalefac[n],c),s+=c);l.block_type==Y.SHORT_TYPE?s+=y(o,l):s+=T(o,l),r+=s+=M(o,l)}else for(t=0,a=0;a<o.channels_out;a++){var f,h,u=0;if(s=0,n=0,h=0,(l=i.tt[t][a]).block_type==Y.SHORT_TYPE){for(;h<4;h++){var m=l.sfb_partition_table[h]/3,p=l.slen[h];for(f=0;f<m;f++,n++)b(o,Math.max(l.scalefac[3*n+0],0),p),b(o,Math.max(l.scalefac[3*n+1],0),p),b(o,Math.max(l.scalefac[3*n+2],0),p),u+=3*p}s+=y(o,l)}else{for(;h<4;h++)for(m=l.sfb_partition_table[h],p=l.slen[h],f=0;f<m;f++,n++)b(o,Math.max(l.scalefac[n],0),p),u+=p;s+=T(o,l)}r+=u+(s+=M(o,l))}return r}(e),S(e,t.resvDrain_post),s+=t.resvDrain_post,t.main_data_begin+=(n-s)/8,x(e,new B)!=a.ResvSize&&c.err.println("Internal buffer inconsistency. flushbits <> ResvSize"),8*t.main_data_begin!=a.ResvSize&&(c.err.printf("bit reservoir error: \nl3_side.main_data_begin: %d \nResvoir size:             %d \nresv drain (post)         %d \nresv drain (pre)          %d \nheader and sideinfo:      %d \ndata bits:                %d \ntotal bits:               %d (remainder: %d) \nbitsperframe:             %d \n",8*t.main_data_begin,a.ResvSize,t.resvDrain_post,t.resvDrain_pre,8*a.sideinfo_len,s-t.resvDrain_post-8*a.sideinfo_len,s,s%8,n),c.err.println("This is a fatal error.  It has several possible causes:"),c.err.println("90%%  LAME compiled with buggy version of gcc using advanced optimizations"),c.err.println(" 9%%  Your system is overclocked"),c.err.println(" 1%%  bug in LAME encoding library"),a.ResvSize=8*t.main_data_begin),h>1e9){var r;for(r=0;r<F.MAX_HEADER_BUF;++r)a.header[r].write_timing-=h;h=0}return 0},this.copy_buffer=function(e,t,a,i,_){var h=u+1;if(h<=0)return 0;if(0!=i&&h>i)return-1;if(c.arraycopy(f,0,t,a,h),u=-1,m=0,0!=_){var p=n(1);if(p[0]=e.nMusicCRC,l.updateMusicCRC(p,t,a,h),e.nMusicCRC=p[0],h>0&&(e.VBR_seek_table.nBytesWritten+=h),e.decode_on_the_fly)for(var b,d=r([2,1152]),g=h,S=-1;0!=S;)if(S=o.hip_decode1_unclipped(e.hip,t,a,g,d[0],d[1]),g=0,-1==S&&(S=0),S>0){if(e.findPeakSample){for(b=0;b<S;b++)d[0][b]>e.PeakSample?e.PeakSample=d[0][b]:-d[0][b]>e.PeakSample&&(e.PeakSample=-d[0][b]);if(e.channels_out>1)for(b=0;b<S;b++)d[1][b]>e.PeakSample?e.PeakSample=d[1][b]:-d[1][b]>e.PeakSample&&(e.PeakSample=-d[1][b])}if(e.findReplayGain&&s.AnalyzeSamples(e.rgdata,d[0],0,d[1],0,S,e.channels_out)==v.GAIN_ANALYSIS_ERROR)return-6}}return h},this.init_bit_stream_w=function(e){f=a(K.LAME_MAXMP3BUFFER),e.h_ptr=e.w_ptr=0,e.header[e.h_ptr].write_timing=0,u=-1,m=0,h=0}}function M(e,t,a,n){this.xlen=e,this.linmax=t,this.table=a,this.hlen=n}u.MAX_VALUE=34028235e31,m.vbr_off=new m(0),m.vbr_mt=new m(1),m.vbr_rh=new m(2),m.vbr_abr=new m(3),m.vbr_mtrh=new m(4),m.vbr_default=m.vbr_mtrh,p.STEREO=new p(0),p.JOINT_STEREO=new p(1),p.DUAL_CHANNEL=new p(2),p.MONO=new p(3),p.NOT_SET=new p(4),v.STEPS_per_dB=100,v.MAX_dB=120,v.GAIN_NOT_ENOUGH_SAMPLES=-24601,v.GAIN_ANALYSIS_ERROR=0,v.GAIN_ANALYSIS_OK=1,v.INIT_GAIN_ANALYSIS_ERROR=0,v.INIT_GAIN_ANALYSIS_OK=1,v.YULE_ORDER=10,v.MAX_ORDER=v.YULE_ORDER,v.MAX_SAMP_FREQ=48e3,v.RMS_WINDOW_TIME_NUMERATOR=1,v.RMS_WINDOW_TIME_DENOMINATOR=20,v.MAX_SAMPLES_PER_WINDOW=v.MAX_SAMP_FREQ*v.RMS_WINDOW_TIME_NUMERATOR/v.RMS_WINDOW_TIME_DENOMINATOR+1,w.NUMTOCENTRIES=100,w.MAXFRAMESIZE=2880,R.EQ=function(e,t){return Math.abs(e)>Math.abs(t)?Math.abs(e-t)<=1e-6*Math.abs(e):Math.abs(e-t)<=1e-6*Math.abs(t)},R.NEQ=function(e,t){return!R.EQ(e,t)};var A={};function k(e){this.bits=e}function y(){this.over_noise=0,this.tot_noise=0,this.max_noise=0,this.over_count=0,this.over_SSD=0,this.bits=0}function T(){this.setModules=function(e,t){}}function B(){this.useAdjust=0,this.aaSensitivityP=0,this.adjust=0,this.adjustLimit=0,this.decay=0,this.floor=0,this.l=s(Y.SBMAX_l),this.s=s(Y.SBMAX_s),this.psfb21=s(Y.PSFB21),this.psfb12=s(Y.PSFB12),this.cb_l=s(Y.CBANDS),this.cb_s=s(Y.CBANDS),this.eql_w=s(Y.BLKSIZE/2)}function x(){this.class_id=0,this.num_samples=0,this.num_channels=0,this.in_samplerate=0,this.out_samplerate=0,this.scale=0,this.scale_left=0,this.scale_right=0,this.analysis=!1,this.bWriteVbrTag=!1,this.decode_only=!1,this.quality=0,this.mode=p.STEREO,this.force_ms=!1,this.free_format=!1,this.findReplayGain=!1,this.decode_on_the_fly=!1,this.write_id3tag_automatic=!1,this.brate=0,this.compression_ratio=0,this.copyright=0,this.original=0,this.extension=0,this.emphasis=0,this.error_protection=0,this.strict_ISO=!1,this.disable_reservoir=!1,this.quant_comp=0,this.quant_comp_short=0,this.experimentalY=!1,this.experimentalZ=0,this.exp_nspsytune=0,this.preset=0,this.VBR=null,this.VBR_q_frac=0,this.VBR_q=0,this.VBR_mean_bitrate_kbps=0,this.VBR_min_bitrate_kbps=0,this.VBR_max_bitrate_kbps=0,this.VBR_hard_min=0,this.lowpassfreq=0,this.highpassfreq=0,this.lowpasswidth=0,this.highpasswidth=0,this.maskingadjust=0,this.maskingadjust_short=0,this.ATHonly=!1,this.ATHshort=!1,this.noATH=!1,this.ATHtype=0,this.ATHcurve=0,this.ATHlower=0,this.athaa_type=0,this.athaa_loudapprox=0,this.athaa_sensitivity=0,this.short_blocks=null,this.useTemporal=!1,this.interChRatio=0,this.msfix=0,this.tune=!1,this.tune_value_a=0,this.version=0,this.encoder_delay=0,this.encoder_padding=0,this.framesize=0,this.frameNum=0,this.lame_allocated_gfp=0,this.internal_flags=null}function E(e){var t=e;this.quantize=t,this.iteration_loop=function(e,t,a,r){var o,i=e.internal_flags,l=s(H.SFBMAX),_=s(576),c=n(2),f=0,h=i.l3_side,u=new k(f);this.quantize.rv.ResvFrameBegin(e,u),f=u.bits;for(var m=0;m<i.mode_gr;m++){o=this.quantize.qupvt.on_pe(e,t,c,f,m,m),i.mode_ext==Y.MPG_MD_MS_LR&&(this.quantize.ms_convert(i.l3_side,m),this.quantize.qupvt.reduce_side(c,a[m],f,o));for(var p=0;p<i.channels_out;p++){var b,d,v=h.tt[m][p];v.block_type!=Y.SHORT_TYPE?(b=0,d=i.PSY.mask_adjust-b):(b=0,d=i.PSY.mask_adjust_short-b),i.masking_lower=Math.pow(10,.1*d),this.quantize.init_outer_loop(i,v),this.quantize.init_xrpow(i,v,_)&&(this.quantize.qupvt.calc_xmin(e,r[m][p],v,l),this.quantize.outer_loop(e,v,l,_,p,c[p])),this.quantize.iteration_finish_one(i,m,p)}}this.quantize.rv.ResvFrameEnd(i,f)}}function P(){}function I(e,t,a,s){this.l=n(1+Y.SBMAX_l),this.s=n(1+Y.SBMAX_s),this.psfb21=n(1+Y.PSFB21),this.psfb12=n(1+Y.PSFB12);var r=this.l,o=this.s;4==arguments.length&&(this.arrL=arguments[0],this.arrS=arguments[1],this.arr21=arguments[2],this.arr12=arguments[3],c.arraycopy(this.arrL,0,r,0,Math.min(this.arrL.length,this.l.length)),c.arraycopy(this.arrS,0,o,0,Math.min(this.arrS.length,this.s.length)),c.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),c.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function L(){var e=null,a=null,r=null;function o(e){return p[e+L.Q_MAX2]}this.setModules=function(t,n,s){e=t,a=n,r=s},this.IPOW20=function(e){return b[e]};var i=2220446049250313e-31,l=L.IXMAX_VAL+2,_=L.Q_MAX,c=L.Q_MAX2;this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var h=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=h,this.sfBandIndex=[new I([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new I([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new I([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new I([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new I([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new I([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new I([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new I([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new I([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var p=s(_+c+1),b=s(_),d=s(l),v=s(l);function g(e,t){var a=r.ATHformula(t,e);return a-=100,a=Math.pow(10,a/10+e.ATHlower)}function S(e){this.s=e}this.adj43=v,this.iteration_init=function(a){var n,s=a.internal_flags,r=s.l3_side;if(0==s.iteration_init_init){for(s.iteration_init_init=1,r.main_data_begin=0,function(e){for(var a=e.internal_flags.ATH.l,n=e.internal_flags.ATH.psfb21,s=e.internal_flags.ATH.s,r=e.internal_flags.ATH.psfb12,o=e.internal_flags,i=e.out_samplerate,l=0;l<Y.SBMAX_l;l++){var _=o.scalefac_band.l[l],c=o.scalefac_band.l[l+1];a[l]=u.MAX_VALUE;for(var f=_;f<c;f++){var h=g(e,f*i/1152);a[l]=Math.min(a[l],h)}}for(l=0;l<Y.PSFB21;l++)for(_=o.scalefac_band.psfb21[l],c=o.scalefac_band.psfb21[l+1],n[l]=u.MAX_VALUE,f=_;f<c;f++)h=g(e,f*i/1152),n[l]=Math.min(n[l],h);for(l=0;l<Y.SBMAX_s;l++){for(_=o.scalefac_band.s[l],c=o.scalefac_band.s[l+1],s[l]=u.MAX_VALUE,f=_;f<c;f++)h=g(e,f*i/384),s[l]=Math.min(s[l],h);s[l]*=o.scalefac_band.s[l+1]-o.scalefac_band.s[l]}for(l=0;l<Y.PSFB12;l++){for(_=o.scalefac_band.psfb12[l],c=o.scalefac_band.psfb12[l+1],r[l]=u.MAX_VALUE,f=_;f<c;f++)h=g(e,f*i/384),r[l]=Math.min(r[l],h);r[l]*=o.scalefac_band.s[13]-o.scalefac_band.s[12]}if(e.noATH){for(l=0;l<Y.SBMAX_l;l++)a[l]=1e-20;for(l=0;l<Y.PSFB21;l++)n[l]=1e-20;for(l=0;l<Y.SBMAX_s;l++)s[l]=1e-20;for(l=0;l<Y.PSFB12;l++)r[l]=1e-20}o.ATH.floor=10*t(g(e,-1))}(a),d[0]=0,n=1;n<l;n++)d[n]=Math.pow(n,4/3);for(n=0;n<l-1;n++)v[n]=n+1-Math.pow(.5*(d[n]+d[n+1]),.75);for(v[n]=.5,n=0;n<_;n++)b[n]=Math.pow(2,-.1875*(n-210));for(n=0;n<=_+c;n++)p[n]=Math.pow(2,.25*(n-210-c));var o,i,f,h;for(e.huffman_init(s),(n=a.exp_nspsytune>>2&63)>=32&&(n-=64),o=Math.pow(10,n/4/10),(n=a.exp_nspsytune>>8&63)>=32&&(n-=64),i=Math.pow(10,n/4/10),(n=a.exp_nspsytune>>14&63)>=32&&(n-=64),f=Math.pow(10,n/4/10),(n=a.exp_nspsytune>>20&63)>=32&&(n-=64),h=f*Math.pow(10,n/4/10),n=0;n<Y.SBMAX_l;n++){m=n<=6?o:n<=13?i:n<=20?f:h,s.nsPsy.longfact[n]=m}for(n=0;n<Y.SBMAX_s;n++){var m;m=n<=5?o:n<=10?i:n<=11?f:h,s.nsPsy.shortfact[n]=m}}},this.on_pe=function(e,t,s,r,o,i){var l,_,c=e.internal_flags,f=0,h=n(2),u=new k(f),m=a.ResvMaxBits(e,r,u,i),p=(f=u.bits)+m;for(p>F.MAX_BITS_PER_GRANULE&&(p=F.MAX_BITS_PER_GRANULE),l=0,_=0;_<c.channels_out;++_)s[_]=Math.min(F.MAX_BITS_PER_CHANNEL,f/c.channels_out),h[_]=0|s[_]*t[o][_]/700-s[_],h[_]>3*r/4&&(h[_]=3*r/4),h[_]<0&&(h[_]=0),h[_]+s[_]>F.MAX_BITS_PER_CHANNEL&&(h[_]=Math.max(0,F.MAX_BITS_PER_CHANNEL-s[_])),l+=h[_];if(l>m)for(_=0;_<c.channels_out;++_)h[_]=m*h[_]/l;for(_=0;_<c.channels_out;++_)s[_]+=h[_],m-=h[_];for(l=0,_=0;_<c.channels_out;++_)l+=s[_];if(l>F.MAX_BITS_PER_GRANULE){for(_=0;_<c.channels_out;++_)s[_]*=F.MAX_BITS_PER_GRANULE,s[_]/=l,s[_]}return p},this.reduce_side=function(e,t,a,n){var s=.33*(.5-t)/.5;s<0&&(s=0),s>.5&&(s=.5);var r=0|.5*s*(e[0]+e[1]);r>F.MAX_BITS_PER_CHANNEL-e[0]&&(r=F.MAX_BITS_PER_CHANNEL-e[0]),r<0&&(r=0),e[1]>=125&&(e[1]-r>125?(e[0]<a&&(e[0]+=r),e[1]-=r):(e[0]+=e[1]-125,e[1]=125)),(r=e[0]+e[1])>n&&(e[0]=n*e[0]/r,e[1]=n*e[1]/r)},this.athAdjust=function(e,t,a){var n=90.30873362,s=f.FAST_LOG10_X(t,10),r=e*e,o=0;return s-=a,r>1e-20&&(o=1+f.FAST_LOG10_X(r,10/n)),o<0&&(o=0),s*=o,s+=a+n-94.82444863,Math.pow(10,.1*s)},this.calc_xmin=function(e,t,a,n){var s,r=0,o=e.internal_flags,l=0,_=0,c=o.ATH,f=a.xr,h=e.VBR==m.vbr_mtrh?1:0,u=o.masking_lower;for(e.VBR!=m.vbr_mtrh&&e.VBR!=m.vbr_mt||(u=1),s=0;s<a.psy_lmax;s++){M=(w=e.VBR==m.vbr_rh||e.VBR==m.vbr_mtrh?athAdjust(c.adjust,c.l[s],c.floor):c.adjust*c.l[s])/(v=a.width[s]),A=i,B=v>>1,T=0;do{T+=x=f[l]*f[l],A+=x<M?x:M,T+=E=f[++l]*f[l],A+=E<M?E:M,l++}while(--B>0);if(T>w&&_++,s==Y.SBPSY_l)A<(y=w*o.nsPsy.longfact[s])&&(A=y);if(0!=h&&(w=A),!e.ATHonly)if((k=t.en.l[s])>0)y=T*t.thm.l[s]*u/k,0!=h&&(y*=o.nsPsy.longfact[s]),w<y&&(w=y);n[r++]=0!=h?w:w*o.nsPsy.longfact[s]}var p=575;if(a.block_type!=Y.SHORT_TYPE)for(var b=576;0!=b--&&R.EQ(f[b],0);)p=b;a.max_nonzero_coeff=p;for(var d=a.sfb_smin;s<a.psymax;d++,s+=3){var v,g,S;for(S=e.VBR==m.vbr_rh||e.VBR==m.vbr_mtrh?athAdjust(c.adjust,c.s[d],c.floor):c.adjust*c.s[d],v=a.width[s],g=0;g<3;g++){var w,M,A,k,y,T=0,B=v>>1;M=S/v,A=i;do{var x,E;T+=x=f[l]*f[l],A+=x<M?x:M,T+=E=f[++l]*f[l],A+=E<M?E:M,l++}while(--B>0);if(T>S&&_++,d==Y.SBPSY_s)A<(y=S*o.nsPsy.shortfact[d])&&(A=y);if(w=0!=h?A:S,!e.ATHonly&&!e.ATHshort)if((k=t.en.s[d][g])>0)y=T*t.thm.s[d][g]*u/k,0!=h&&(y*=o.nsPsy.shortfact[d]),w<y&&(w=y);n[r++]=0!=h?w:w*o.nsPsy.shortfact[d]}e.useTemporal&&(n[r-3]>n[r-3+1]&&(n[r-3+1]+=(n[r-3]-n[r-3+1])*o.decay),n[r-3+1]>n[r-3+2]&&(n[r-3+2]+=(n[r-3+1]-n[r-3+2])*o.decay))}return _},this.calc_noise_core=function(e,t,a,n){var r=0,o=t.s,i=e.l3_enc;if(o>e.count1)for(;0!=a--;){_=e.xr[o],o++,r+=_*_,_=e.xr[o],o++,r+=_*_}else if(o>e.big_values){var l=s(2);for(l[0]=0,l[1]=n;0!=a--;){_=Math.abs(e.xr[o])-l[i[o]],o++,r+=_*_,_=Math.abs(e.xr[o])-l[i[o]],o++,r+=_*_}}else for(;0!=a--;){var _;_=Math.abs(e.xr[o])-d[i[o]]*n,o++,r+=_*_,_=Math.abs(e.xr[o])-d[i[o]]*n,o++,r+=_*_}return t.s=o,r},this.calc_noise=function(e,t,a,n,s){var r,i,l=0,_=0,c=0,u=0,m=0,p=-20,b=0,d=e.scalefac,v=0;for(n.over_SSD=0,r=0;r<e.psymax;r++){var g,w=e.global_gain-(d[v++]+(0!=e.preflag?h[r]:0)<<e.scalefac_scale+1)-8*e.subblock_gain[e.window[r]],R=0;if(null!=s&&s.step[r]==w)R=s.noise[r],b+=e.width[r],a[l++]=R/t[_++],R=s.noise_log[r];else{var M,A=o(w);if(i=e.width[r]>>1,b+e.width[r]>e.max_nonzero_coeff)i=(M=e.max_nonzero_coeff-b+1)>0?M>>1:0;var k=new S(b);R=this.calc_noise_core(e,k,i,A),b=k.s,null!=s&&(s.step[r]=w,s.noise[r]=R),R=a[l++]=R/t[_++],R=f.FAST_LOG10(Math.max(R,1e-20)),null!=s&&(s.noise_log[r]=R)}if(null!=s&&(s.global_gain=e.global_gain),m+=R,R>0)g=Math.max(0|10*R+.5,1),n.over_SSD+=g*g,c++,u+=R;p=Math.max(p,R)}return n.over_count=c,n.tot_noise=m,n.over_noise=u,n.max_noise=p,c},this.set_pinfo=function(e,t,a,n,r){var o,i,l,_,c,f=e.internal_flags,u=0==t.scalefac_scale?.5:1,m=t.scalefac,p=s(H.SFBMAX),b=s(H.SFBMAX),d=new y;calc_xmin(e,a,t,p),calc_noise(t,p,b,d,null);var v=0;for(i=t.sfb_lmax,t.block_type!=Y.SHORT_TYPE&&0==t.mixed_block_flag&&(i=22),o=0;o<i;o++){var g=f.scalefac_band.l[o],S=(w=f.scalefac_band.l[o+1])-g;for(_=0;v<w;v++)_+=t.xr[v]*t.xr[v];_/=S,c=1e15,f.pinfo.en[n][r][o]=c*_,f.pinfo.xfsf[n][r][o]=c*p[o]*b[o]/S,a.en.l[o]>0&&!e.ATHonly?_/=a.en.l[o]:_=0,f.pinfo.thr[n][r][o]=c*Math.max(_*a.thm.l[o],f.ATH.l[o]),f.pinfo.LAMEsfb[n][r][o]=0,0!=t.preflag&&o>=11&&(f.pinfo.LAMEsfb[n][r][o]=-u*h[o]),o<Y.SBPSY_l&&(f.pinfo.LAMEsfb[n][r][o]-=u*m[o])}if(t.block_type==Y.SHORT_TYPE)for(i=o,o=t.sfb_smin;o<Y.SBMAX_s;o++){g=f.scalefac_band.s[o],S=(w=f.scalefac_band.s[o+1])-g;for(var w,R=0;R<3;R++){for(_=0,l=g;l<w;l++)_+=t.xr[v]*t.xr[v],v++;_=Math.max(_/S,1e-20),c=1e15,f.pinfo.en_s[n][r][3*o+R]=c*_,f.pinfo.xfsf_s[n][r][3*o+R]=c*p[i]*b[i]/S,a.en.s[o][R]>0?_/=a.en.s[o][R]:_=0,(e.ATHonly||e.ATHshort)&&(_=0),f.pinfo.thr_s[n][r][3*o+R]=c*Math.max(_*a.thm.s[o][R],f.ATH.s[o]),f.pinfo.LAMEsfb_s[n][r][3*o+R]=-2*t.subblock_gain[R],o<Y.SBPSY_s&&(f.pinfo.LAMEsfb_s[n][r][3*o+R]-=u*m[i]),i++}}f.pinfo.LAMEqss[n][r]=t.global_gain,f.pinfo.LAMEmainbits[n][r]=t.part2_3_length+t.part2_length,f.pinfo.LAMEsfbits[n][r]=t.part2_length,f.pinfo.over[n][r]=d.over_count,f.pinfo.max_noise[n][r]=10*d.max_noise,f.pinfo.over_noise[n][r]=10*d.over_noise,f.pinfo.tot_noise[n][r]=10*d.tot_noise,f.pinfo.over_SSD[n][r]=d.over_SSD}}function C(){this.global_gain=0,this.sfb_count1=0,this.step=n(39),this.noise=s(39),this.noise_log=s(39)}function D(){this.xr=s(576),this.l3_enc=n(576),this.scalefac=n(H.SFBMAX),this.xrpow_max=0,this.part2_3_length=0,this.big_values=0,this.count1=0,this.global_gain=0,this.scalefac_compress=0,this.block_type=0,this.mixed_block_flag=0,this.table_select=n(3),this.subblock_gain=n(4),this.region0_count=0,this.region1_count=0,this.preflag=0,this.scalefac_scale=0,this.count1table_select=0,this.part2_length=0,this.sfb_lmax=0,this.sfb_smin=0,this.psy_lmax=0,this.sfbmax=0,this.psymax=0,this.sfbdivide=0,this.width=n(H.SFBMAX),this.window=n(H.SFBMAX),this.count1bits=0,this.sfb_partition_table=null,this.slen=n(4),this.max_nonzero_coeff=0;var e=this;function t(e){return new Int32Array(e)}this.assign=function(a){var n;e.xr=(n=a.xr,new Float32Array(n)),e.l3_enc=t(a.l3_enc),e.scalefac=t(a.scalefac),e.xrpow_max=a.xrpow_max,e.part2_3_length=a.part2_3_length,e.big_values=a.big_values,e.count1=a.count1,e.global_gain=a.global_gain,e.scalefac_compress=a.scalefac_compress,e.block_type=a.block_type,e.mixed_block_flag=a.mixed_block_flag,e.table_select=t(a.table_select),e.subblock_gain=t(a.subblock_gain),e.region0_count=a.region0_count,e.region1_count=a.region1_count,e.preflag=a.preflag,e.scalefac_scale=a.scalefac_scale,e.count1table_select=a.count1table_select,e.part2_length=a.part2_length,e.sfb_lmax=a.sfb_lmax,e.sfb_smin=a.sfb_smin,e.psy_lmax=a.psy_lmax,e.sfbmax=a.sfbmax,e.psymax=a.psymax,e.sfbdivide=a.sfbdivide,e.width=t(a.width),e.window=t(a.window),e.count1bits=a.count1bits,e.sfb_partition_table=a.sfb_partition_table.slice(0),e.slen=t(a.slen),e.max_nonzero_coeff=a.max_nonzero_coeff}}A.t1HB=[1,1,1,0],A.t2HB=[1,2,1,3,1,1,3,2,0],A.t3HB=[3,2,1,1,1,1,3,2,0],A.t5HB=[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],A.t6HB=[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],A.t7HB=[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],A.t8HB=[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0],A.t9HB=[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],A.t10HB=[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0],A.t11HB=[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],A.t12HB=[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],A.t13HB=[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],A.t15HB=[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],A.t16HB=[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],A.t24HB=[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],A.t32HB=[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],A.t33HB=[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],A.t1l=[1,4,3,5],A.t2l=[1,4,7,4,5,7,6,7,8],A.t3l=[2,3,7,4,4,7,6,7,8],A.t5l=[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],A.t6l=[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],A.t7l=[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],A.t8l=[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13],A.t9l=[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],A.t10l=[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13],A.t11l=[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],A.t12l=[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],A.t13l=[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],A.t15l=[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],A.t16_5l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],A.t16l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],A.t24l=[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],A.t32l=[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],A.t33l=[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8],A.ht=[new M(0,0,null,null),new M(2,0,A.t1HB,A.t1l),new M(3,0,A.t2HB,A.t2l),new M(3,0,A.t3HB,A.t3l),new M(0,0,null,null),new M(4,0,A.t5HB,A.t5l),new M(4,0,A.t6HB,A.t6l),new M(6,0,A.t7HB,A.t7l),new M(6,0,A.t8HB,A.t8l),new M(6,0,A.t9HB,A.t9l),new M(8,0,A.t10HB,A.t10l),new M(8,0,A.t11HB,A.t11l),new M(8,0,A.t12HB,A.t12l),new M(16,0,A.t13HB,A.t13l),new M(0,0,null,A.t16_5l),new M(16,0,A.t15HB,A.t15l),new M(1,1,A.t16HB,A.t16l),new M(2,3,A.t16HB,A.t16l),new M(3,7,A.t16HB,A.t16l),new M(4,15,A.t16HB,A.t16l),new M(6,63,A.t16HB,A.t16l),new M(8,255,A.t16HB,A.t16l),new M(10,1023,A.t16HB,A.t16l),new M(13,8191,A.t16HB,A.t16l),new M(4,15,A.t24HB,A.t24l),new M(5,31,A.t24HB,A.t24l),new M(6,63,A.t24HB,A.t24l),new M(7,127,A.t24HB,A.t24l),new M(8,255,A.t24HB,A.t24l),new M(9,511,A.t24HB,A.t24l),new M(11,2047,A.t24HB,A.t24l),new M(13,8191,A.t24HB,A.t24l),new M(0,0,A.t32HB,A.t32l),new M(0,0,A.t33HB,A.t33l)],A.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366],A.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296],A.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369],A.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]],A.samplerate_table=[[22050,24e3,16e3,-1],[44100,48e3,32e3,-1],[11025,12e3,8e3,-1]],A.scfsi_band=[0,6,11,16,21],L.Q_MAX=257,L.Q_MAX2=116,L.LARGE_BITS=1e5,L.IXMAX_VAL=8206;var H={};function O(){var e,t,a;this.rv=null,this.qupvt=null;var n,r=new T;function o(e){this.ordinal=e}function i(e){for(var t=0;t<e.sfbmax;t++)if(e.scalefac[t]+e.subblock_gain[e.window[t]]==0)return!1;return!0}function l(e){return f.FAST_LOG10(.368+.632*e*e*e)}function h(e,t,a,n,s){var r;switch(e){default:case 9:t.over_count>0?(r=a.over_SSD<=t.over_SSD,a.over_SSD==t.over_SSD&&(r=a.bits<t.bits)):r=a.max_noise<0&&10*a.max_noise+a.bits<=10*t.max_noise+t.bits;break;case 0:r=a.over_count<t.over_count||a.over_count==t.over_count&&a.over_noise<t.over_noise||a.over_count==t.over_count&&R.EQ(a.over_noise,t.over_noise)&&a.tot_noise<t.tot_noise;break;case 8:a.max_noise=function(e,t){for(var a=1e-37,n=0;n<t.psymax;n++)a+=l(e[n]);return Math.max(1e-20,a)}(s,n);case 1:r=a.max_noise<t.max_noise;break;case 2:r=a.tot_noise<t.tot_noise;break;case 3:r=a.tot_noise<t.tot_noise&&a.max_noise<t.max_noise;break;case 4:r=a.max_noise<=0&&t.max_noise>.2||a.max_noise<=0&&t.max_noise<0&&t.max_noise>a.max_noise-.2&&a.tot_noise<t.tot_noise||a.max_noise<=0&&t.max_noise>0&&t.max_noise>a.max_noise-.2&&a.tot_noise<t.tot_noise+t.over_noise||a.max_noise>0&&t.max_noise>-.05&&t.max_noise>a.max_noise-.1&&a.tot_noise+a.over_noise<t.tot_noise+t.over_noise||a.max_noise>0&&t.max_noise>-.1&&t.max_noise>a.max_noise-.15&&a.tot_noise+a.over_noise+a.over_noise<t.tot_noise+t.over_noise+t.over_noise;break;case 5:r=a.over_noise<t.over_noise||R.EQ(a.over_noise,t.over_noise)&&a.tot_noise<t.tot_noise;break;case 6:r=a.over_noise<t.over_noise||R.EQ(a.over_noise,t.over_noise)&&(a.max_noise<t.max_noise||R.EQ(a.max_noise,t.max_noise)&&a.tot_noise<=t.tot_noise);break;case 7:r=a.over_count<t.over_count||a.over_noise<t.over_noise}return 0==t.over_count&&(r=r&&a.bits<t.bits),r}function u(e,t,s,r,o){var l=e.internal_flags;!function(e,t,a,n,s){var r,o=e.internal_flags;r=0==t.scalefac_scale?1.2968395546510096:1.6817928305074292;for(var i=0,l=0;l<t.sfbmax;l++)i<a[l]&&(i=a[l]);var _=o.noise_shaping_amp;switch(3==_&&(_=s?2:1),_){case 2:break;case 1:i>1?i=Math.pow(i,.5):i*=.95;break;default:i>1?i=1:i*=.95}var c=0;for(l=0;l<t.sfbmax;l++){var f,h=t.width[l];if(c+=h,!(a[l]<i)){if(2&o.substep_shaping&&(o.pseudohalf[l]=0==o.pseudohalf[l]?1:0,0==o.pseudohalf[l]&&2==o.noise_shaping_amp))return;for(t.scalefac[l]++,f=-h;f<0;f++)n[c+f]*=r,n[c+f]>t.xrpow_max&&(t.xrpow_max=n[c+f]);if(2==o.noise_shaping_amp)return}}}(e,t,s,r,o);var c=i(t);return!c&&(!(c=2==l.mode_gr?n.scale_bitcount(t):n.scale_bitcount_lsf(l,t))||(l.noise_shaping>1&&(_.fill(l.pseudohalf,0),0==t.scalefac_scale?(!function(e,t){for(var n=0,s=0;s<e.sfbmax;s++){var r=e.width[s],o=e.scalefac[s];if(0!=e.preflag&&(o+=a.pretab[s]),n+=r,1&o){o++;for(var i=-r;i<0;i++)t[n+i]*=1.2968395546510096,t[n+i]>e.xrpow_max&&(e.xrpow_max=t[n+i])}e.scalefac[s]=o>>1}e.preflag=0,e.scalefac_scale=1}(t,r),c=!1):t.block_type==Y.SHORT_TYPE&&l.subblock_gain>0&&(c=function(e,t,n){var s,r=t.scalefac;for(s=0;s<t.sfb_lmax;s++)if(r[s]>=16)return!0;for(var o=0;o<3;o++){var i=0,l=0;for(s=t.sfb_lmax+o;s<t.sfbdivide;s+=3)i<r[s]&&(i=r[s]);for(;s<t.sfbmax;s+=3)l<r[s]&&(l=r[s]);if(!(i<16&&l<8)){if(t.subblock_gain[o]>=7)return!0;t.subblock_gain[o]++;var _=e.scalefac_band.l[t.sfb_lmax];for(s=t.sfb_lmax+o;s<t.sfbmax;s+=3){var c=t.width[s],f=r[s];if((f-=4>>t.scalefac_scale)>=0)r[s]=f,_+=3*c;else{r[s]=0;var h=210+(f<<t.scalefac_scale+1);m=a.IPOW20(h),_+=c*(o+1);for(var u=-c;u<0;u++)n[_+u]*=m,n[_+u]>t.xrpow_max&&(t.xrpow_max=n[_+u]);_+=c*(3-o-1)}}var m=a.IPOW20(202);for(_+=t.width[s]*(o+1),u=-t.width[s];u<0;u++)n[_+u]*=m,n[_+u]>t.xrpow_max&&(t.xrpow_max=n[_+u])}}return!1}(l,t,r)||i(t))),c||(c=2==l.mode_gr?n.scale_bitcount(t):n.scale_bitcount_lsf(l,t)),!c))}this.setModules=function(s,o,i,l){e=s,t=o,this.rv=o,a=i,this.qupvt=i,n=l,r.setModules(a,n)},this.ms_convert=function(e,t){for(var a=0;a<576;++a){var n=e.tt[t][0].xr[a],s=e.tt[t][1].xr[a];e.tt[t][0].xr[a]=(n+s)*(.5*f.SQRT2),e.tt[t][1].xr[a]=(n-s)*(.5*f.SQRT2)}},this.init_xrpow=function(e,t,a){var n=0,s=0|t.max_nonzero_coeff;if(t.xrpow_max=0,_.fill(a,s,576,0),n=function(e,t,a,n){n=0;for(var s=0;s<=a;++s){var r=Math.abs(e.xr[s]);n+=r,t[s]=Math.sqrt(r*Math.sqrt(r)),t[s]>e.xrpow_max&&(e.xrpow_max=t[s])}return n}(t,a,s,n),n>1e-20){var r=0;2&e.substep_shaping&&(r=1);for(var o=0;o<t.psymax;o++)e.pseudohalf[o]=r;return!0}return _.fill(t.l3_enc,0,576,0),!1},this.init_outer_loop=function(e,t){t.part2_3_length=0,t.big_values=0,t.count1=0,t.global_gain=210,t.scalefac_compress=0,t.table_select[0]=0,t.table_select[1]=0,t.table_select[2]=0,t.subblock_gain[0]=0,t.subblock_gain[1]=0,t.subblock_gain[2]=0,t.subblock_gain[3]=0,t.region0_count=0,t.region1_count=0,t.preflag=0,t.scalefac_scale=0,t.count1table_select=0,t.part2_length=0,t.sfb_lmax=Y.SBPSY_l,t.sfb_smin=Y.SBPSY_s,t.psy_lmax=e.sfb21_extra?Y.SBMAX_l:Y.SBPSY_l,t.psymax=t.psy_lmax,t.sfbmax=t.sfb_lmax,t.sfbdivide=11;for(var n=0;n<Y.SBMAX_l;n++)t.width[n]=e.scalefac_band.l[n+1]-e.scalefac_band.l[n],t.window[n]=3;if(t.block_type==Y.SHORT_TYPE){var r=s(576);t.sfb_smin=0,t.sfb_lmax=0,0!=t.mixed_block_flag&&(t.sfb_smin=3,t.sfb_lmax=2*e.mode_gr+4),t.psymax=t.sfb_lmax+3*((e.sfb21_extra?Y.SBMAX_s:Y.SBPSY_s)-t.sfb_smin),t.sfbmax=t.sfb_lmax+3*(Y.SBPSY_s-t.sfb_smin),t.sfbdivide=t.sfbmax-18,t.psy_lmax=t.sfb_lmax;var o=e.scalefac_band.l[t.sfb_lmax];c.arraycopy(t.xr,0,r,0,576);for(n=t.sfb_smin;n<Y.SBMAX_s;n++)for(var i=e.scalefac_band.s[n],l=e.scalefac_band.s[n+1],f=0;f<3;f++)for(var h=i;h<l;h++)t.xr[o++]=r[3*h+f];var u=t.sfb_lmax;for(n=t.sfb_smin;n<Y.SBMAX_s;n++)t.width[u]=t.width[u+1]=t.width[u+2]=e.scalefac_band.s[n+1]-e.scalefac_band.s[n],t.window[u]=0,t.window[u+1]=1,t.window[u+2]=2,u+=3}t.count1bits=0,t.sfb_partition_table=a.nr_of_sfb_block[0][0],t.slen[0]=0,t.slen[1]=0,t.slen[2]=0,t.slen[3]=0,t.max_nonzero_coeff=575,_.fill(t.scalefac,0),function(e,t){var n=e.ATH,s=t.xr;if(t.block_type!=Y.SHORT_TYPE)for(var r=!1,o=Y.PSFB21-1;o>=0&&!r;o--){var i=e.scalefac_band.psfb21[o],l=e.scalefac_band.psfb21[o+1],_=a.athAdjust(n.adjust,n.psfb21[o],n.floor);e.nsPsy.longfact[21]>1e-12&&(_*=e.nsPsy.longfact[21]);for(var c=l-1;c>=i;c--){if(!(Math.abs(s[c])<_)){r=!0;break}s[c]=0}}else for(var f=0;f<3;f++)for(r=!1,o=Y.PSFB12-1;o>=0&&!r;o--){l=(i=3*e.scalefac_band.s[12]+(e.scalefac_band.s[13]-e.scalefac_band.s[12])*f+(e.scalefac_band.psfb12[o]-e.scalefac_band.psfb12[0]))+(e.scalefac_band.psfb12[o+1]-e.scalefac_band.psfb12[o]);var h=a.athAdjust(n.adjust,n.psfb12[o],n.floor);for(e.nsPsy.shortfact[12]>1e-12&&(h*=e.nsPsy.shortfact[12]),c=l-1;c>=i;c--){if(!(Math.abs(s[c])<h)){r=!0;break}s[c]=0}}}(e,t)},o.BINSEARCH_NONE=new o(0),o.BINSEARCH_UP=new o(1),o.BINSEARCH_DOWN=new o(2),this.trancate_smallspectrums=function(e,t,r,o){var i=s(H.SFBMAX);if((4&e.substep_shaping||t.block_type!=Y.SHORT_TYPE)&&!(128&e.substep_shaping)){a.calc_noise(t,r,i,new y,null);for(var l=0;l<576;l++){var c=0;0!=t.l3_enc[l]&&(c=Math.abs(t.xr[l])),o[l]=c}l=0;var f=8;t.block_type==Y.SHORT_TYPE&&(f=6);do{var h,u,m,p,b=t.width[f];if(l+=b,!(i[f]>=1||(_.sort(o,l-b,b),R.EQ(o[l-1],0)))){h=(1-i[f])*r[f],u=0,p=0;do{var d;for(m=1;p+m<b&&!R.NEQ(o[p+l-b],o[p+l+m-b]);m++);if(h<(d=o[p+l-b]*o[p+l-b]*m)){0!=p&&(u=o[p+l-b-1]);break}h-=d,p+=m}while(p<b);if(!R.EQ(u,0))do{Math.abs(t.xr[l-b])<=u&&(t.l3_enc[l-b]=0)}while(--b>0)}}while(++f<t.psymax);t.part2_3_length=n.noquant_count_bits(e,t,null)}},this.outer_loop=function(e,t,r,i,l,_){var f=e.internal_flags,p=new D,b=s(576),d=s(H.SFBMAX),v=new y,g=new C,S=9999999,w=!1,R=!1,M=0;if(function(e,t,a,s,r){var i,l=e.CurrentStep[s],_=!1,c=e.OldValue[s],f=o.BINSEARCH_NONE;for(t.global_gain=c,a-=t.part2_length;;){var h;if(i=n.count_bits(e,r,t,null),1==l||i==a)break;i>a?(f==o.BINSEARCH_DOWN&&(_=!0),_&&(l/=2),f=o.BINSEARCH_UP,h=l):(f==o.BINSEARCH_UP&&(_=!0),_&&(l/=2),f=o.BINSEARCH_DOWN,h=-l),t.global_gain+=h,t.global_gain<0&&(t.global_gain=0,_=!0),t.global_gain>255&&(t.global_gain=255,_=!0)}for(;i>a&&t.global_gain<255;)t.global_gain++,i=n.count_bits(e,r,t,null);e.CurrentStep[s]=c-t.global_gain>=4?4:2,e.OldValue[s]=t.global_gain,t.part2_3_length=i}(f,t,_,l,i),0==f.noise_shaping)return 100;a.calc_noise(t,r,d,v,g),v.bits=t.part2_3_length,p.assign(t);var A=0;for(c.arraycopy(i,0,b,0,576);!w;){do{var k,T=new y,B=255;if(k=2&f.substep_shaping?20:3,f.sfb21_extra){if(d[p.sfbmax]>1)break;if(p.block_type==Y.SHORT_TYPE&&(d[p.sfbmax+1]>1||d[p.sfbmax+2]>1))break}if(!u(e,p,d,i,R))break;0!=p.scalefac_scale&&(B=254);var x=_-p.part2_length;if(x<=0)break;for(;(p.part2_3_length=n.count_bits(f,i,p,g))>x&&p.global_gain<=B;)p.global_gain++;if(p.global_gain>B)break;if(0==v.over_count){for(;(p.part2_3_length=n.count_bits(f,i,p,g))>S&&p.global_gain<=B;)p.global_gain++;if(p.global_gain>B)break}if(a.calc_noise(p,r,d,T,g),T.bits=p.part2_3_length,0!=(h(t.block_type!=Y.SHORT_TYPE?e.quant_comp:e.quant_comp_short,v,T,p,d)?1:0))S=t.part2_3_length,v=T,t.assign(p),A=0,c.arraycopy(i,0,b,0,576);else if(0==f.full_outer_loop){if(++A>k&&0==v.over_count)break;if(3==f.noise_shaping_amp&&R&&A>30)break;if(3==f.noise_shaping_amp&&R&&p.global_gain-M>15)break}}while(p.global_gain+p.scalefac_scale<255);3==f.noise_shaping_amp?R?w=!0:(p.assign(t),c.arraycopy(b,0,i,0,576),A=0,M=p.global_gain,R=!0):w=!0}return e.VBR==m.vbr_rh||e.VBR==m.vbr_mtrh?c.arraycopy(b,0,i,0,576):1&f.substep_shaping&&trancate_smallspectrums(f,t,r,i),v.over_count},this.iteration_finish_one=function(e,a,s){var r=e.l3_side,o=r.tt[a][s];n.best_scalefac_store(e,a,s,r),1==e.use_best_huffman&&n.best_huffman_divide(e,o),t.ResvAdjust(e,o)},this.VBR_encode_granule=function(e,t,a,n,r,o,i){var l,f=e.internal_flags,h=new D,u=s(576),m=i,p=i+1,b=(i+o)/2,d=0,v=f.sfb21_extra;_.fill(h.l3_enc,0);do{f.sfb21_extra=!(b>m-42)&&v,outer_loop(e,t,a,n,r,b)<=0?(d=1,p=t.part2_3_length,h.assign(t),c.arraycopy(n,0,u,0,576),l=(i=p-32)-o,b=(i+o)/2):(l=i-(o=b+32),b=(i+o)/2,0!=d&&(d=2,t.assign(h),c.arraycopy(u,0,n,0,576)))}while(l>12);f.sfb21_extra=v,2==d&&c.arraycopy(h.l3_enc,0,t.l3_enc,0,576)},this.get_framebits=function(a,n){var s=a.internal_flags;s.bitrate_index=s.VBR_min_bitrate;var r=e.getframebits(a);s.bitrate_index=1,r=e.getframebits(a);for(var o=1;o<=s.VBR_max_bitrate;o++){s.bitrate_index=o;var i=new k(r);n[o]=t.ResvFrameBegin(a,i),r=i.bits}},this.VBR_old_prepare=function(e,n,s,r,o,i,l,_,c){var f,h=e.internal_flags,u=0,m=1,p=0;h.bitrate_index=h.VBR_max_bitrate;var b=t.ResvFrameBegin(e,new k(0))/h.mode_gr;get_framebits(e,i);for(var d=0;d<h.mode_gr;d++){var v=a.on_pe(e,n,_[d],b,d,0);h.mode_ext==Y.MPG_MD_MS_LR&&(ms_convert(h.l3_side,d),a.reduce_side(_[d],s[d],b,v));for(var g=0;g<h.channels_out;++g){var S=h.l3_side.tt[d][g];S.block_type!=Y.SHORT_TYPE?(u=1.28/(1+Math.exp(3.5-n[d][g]/300))-.05,f=h.PSY.mask_adjust-u):(u=2.56/(1+Math.exp(3.5-n[d][g]/300))-.14,f=h.PSY.mask_adjust_short-u),h.masking_lower=Math.pow(10,.1*f),init_outer_loop(h,S),c[d][g]=a.calc_xmin(e,r[d][g],S,o[d][g]),0!=c[d][g]&&(m=0),l[d][g]=126,p+=_[d][g]}}for(d=0;d<h.mode_gr;d++)for(g=0;g<h.channels_out;g++)p>i[h.VBR_max_bitrate]&&(_[d][g]*=i[h.VBR_max_bitrate],_[d][g]/=p),l[d][g]>_[d][g]&&(l[d][g]=_[d][g]);return m},this.bitpressure_strategy=function(e,t,a,n){for(var s=0;s<e.mode_gr;s++)for(var r=0;r<e.channels_out;r++){for(var o=e.l3_side.tt[s][r],i=t[s][r],l=0,_=0;_<o.psy_lmax;_++)i[l++]*=1+.029*_*_/Y.SBMAX_l/Y.SBMAX_l;if(o.block_type==Y.SHORT_TYPE)for(_=o.sfb_smin;_<Y.SBMAX_s;_++)i[l++]*=1+.029*_*_/Y.SBMAX_s/Y.SBMAX_s,i[l++]*=1+.029*_*_/Y.SBMAX_s/Y.SBMAX_s,i[l++]*=1+.029*_*_/Y.SBMAX_s/Y.SBMAX_s;n[s][r]=0|Math.max(a[s][r],.9*n[s][r])}},this.VBR_new_prepare=function(e,n,s,r,o,i){var l,_=e.internal_flags,c=1,f=0,h=0;if(e.free_format){_.bitrate_index=0;u=new k(f);l=t.ResvFrameBegin(e,u),f=u.bits,o[0]=l}else{_.bitrate_index=_.VBR_max_bitrate;var u=new k(f);t.ResvFrameBegin(e,u),f=u.bits,get_framebits(e,o),l=o[_.VBR_max_bitrate]}for(var m=0;m<_.mode_gr;m++){a.on_pe(e,n,i[m],f,m,0),_.mode_ext==Y.MPG_MD_MS_LR&&ms_convert(_.l3_side,m);for(var p=0;p<_.channels_out;++p){var b=_.l3_side.tt[m][p];_.masking_lower=Math.pow(10,.1*_.PSY.mask_adjust),init_outer_loop(_,b),0!=a.calc_xmin(e,s[m][p],b,r[m][p])&&(c=0),h+=i[m][p]}}for(m=0;m<_.mode_gr;m++)for(p=0;p<_.channels_out;p++)h>l&&(i[m][p]*=l,i[m][p]/=h);return c},this.calc_target_bits=function(n,s,r,o,i,l){var _,c,f,h,u=n.internal_flags,m=u.l3_side,p=0;u.bitrate_index=u.VBR_max_bitrate;var b=new k(p);for(l[0]=t.ResvFrameBegin(n,b),p=b.bits,u.bitrate_index=1,p=e.getframebits(n)-8*u.sideinfo_len,i[0]=p/(u.mode_gr*u.channels_out),p=n.VBR_mean_bitrate_kbps*n.framesize*1e3,1&u.substep_shaping&&(p*=1.09),p/=n.out_samplerate,p-=8*u.sideinfo_len,p/=u.mode_gr*u.channels_out,(_=.93+.07*(11-n.compression_ratio)/5.5)<.9&&(_=.9),_>1&&(_=1),c=0;c<u.mode_gr;c++){var d=0;for(f=0;f<u.channels_out;f++){if(o[c][f]=int(_*p),s[c][f]>700){var v=int((s[c][f]-700)/1.4),g=m.tt[c][f];o[c][f]=int(_*p),g.block_type==Y.SHORT_TYPE&&v<p/2&&(v=p/2),v>3*p/2?v=3*p/2:v<0&&(v=0),o[c][f]+=v}o[c][f]>F.MAX_BITS_PER_CHANNEL&&(o[c][f]=F.MAX_BITS_PER_CHANNEL),d+=o[c][f]}if(d>F.MAX_BITS_PER_GRANULE)for(f=0;f<u.channels_out;++f)o[c][f]*=F.MAX_BITS_PER_GRANULE,o[c][f]/=d}if(u.mode_ext==Y.MPG_MD_MS_LR)for(c=0;c<u.mode_gr;c++)a.reduce_side(o[c],r[c],p*u.channels_out,F.MAX_BITS_PER_GRANULE);for(h=0,c=0;c<u.mode_gr;c++)for(f=0;f<u.channels_out;f++)o[c][f]>F.MAX_BITS_PER_CHANNEL&&(o[c][f]=F.MAX_BITS_PER_CHANNEL),h+=o[c][f];if(h>l[0])for(c=0;c<u.mode_gr;c++)for(f=0;f<u.channels_out;f++)o[c][f]*=l[0],o[c][f]/=h}}function V(){var e=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.035780907*f.SQRT2*.5/2384e-9,.017876148*f.SQRT2*.5/2384e-9,.003134727*f.SQRT2*.5/2384e-9,.002457142*f.SQRT2*.5/2384e-9,971317e-9*f.SQRT2*.5/2384e-9,218868e-9*f.SQRT2*.5/2384e-9,101566e-9*f.SQRT2*.5/2384e-9,13828e-9*f.SQRT2*.5/2384e-9,12804.797818791945,1945.5515939597317,313.4244966442953,20.801593959731544,1995.1556208053692,9.000838926174497,-29.20218120805369],t=[[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,940084909404969e-27,6423305872147839e-28,2382191739347918e-28,5456116108943412e-27,4878985199565852e-27,4240448995017367e-27,3559909094758252e-27,2858043359288075e-27,2156177623817898e-27,1475637723558783e-27,8371015190102974e-28,2599706096327376e-28,-5456116108943412e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758252e-27,-2858043359288076e-27,-2156177623817898e-27,-1475637723558783e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347923e-28,-6423305872147843e-28,-9400849094049696e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049694e-28,-642330587214784e-27,-2382191739347918e-28],[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,9400849094049688e-28,6423305872147841e-28,2382191739347918e-28,5456116108943413e-27,4878985199565852e-27,4240448995017367e-27,3559909094758253e-27,2858043359288075e-27,2156177623817898e-27,1475637723558782e-27,8371015190102975e-28,2599706096327376e-28,-5461314069809755e-27,-4921085770524055e-27,-4343405037091838e-27,-3732668368707687e-27,-3093523840190885e-27,-2430835727329465e-27,-1734679010007751e-27,-974825365660928e-27,-2797435120168326e-28,0,0,0,0,0,0,-2283748241799531e-28,-4037858874020686e-28,-2146547464825323e-28],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2283748241799531e-28,4037858874020686e-28,2146547464825323e-28,5461314069809755e-27,4921085770524055e-27,4343405037091838e-27,3732668368707687e-27,3093523840190885e-27,2430835727329466e-27,1734679010007751e-27,974825365660928e-27,2797435120168326e-28,-5456116108943413e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758253e-27,-2858043359288075e-27,-2156177623817898e-27,-1475637723558782e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347913e-28,-6423305872147834e-28,-9400849094049688e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049688e-28,-6423305872147841e-28,-2382191739347918e-28]],a=t[Y.SHORT_TYPE],n=t[Y.SHORT_TYPE],r=t[Y.SHORT_TYPE],o=t[Y.SHORT_TYPE],i=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];function l(t,a,n){for(var s,r,o,i=10,l=a+238-14-286,_=-15;_<0;_++){var c,h,u;c=e[i+-10],h=t[l+-224]*c,u=t[a+224]*c,c=e[i+-9],h+=t[l+-160]*c,u+=t[a+160]*c,c=e[i+-8],h+=t[l+-96]*c,u+=t[a+96]*c,c=e[i+-7],h+=t[l+-32]*c,u+=t[a+32]*c,c=e[i+-6],h+=t[l+32]*c,u+=t[a+-32]*c,c=e[i+-5],h+=t[l+96]*c,u+=t[a+-96]*c,c=e[i+-4],h+=t[l+160]*c,u+=t[a+-160]*c,c=e[i+-3],h+=t[l+224]*c,u+=t[a+-224]*c,c=e[i+-2],h+=t[a+-256]*c,u-=t[l+256]*c,c=e[i+-1],h+=t[a+-192]*c,u-=t[l+192]*c,c=e[i+0],h+=t[a+-128]*c,u-=t[l+128]*c,c=e[i+1],h+=t[a+-64]*c,u-=t[l+64]*c,c=e[i+2],h+=t[a+0]*c,u-=t[l+0]*c,c=e[i+3],h+=t[a+64]*c,u-=t[l+-64]*c,c=e[i+4],h+=t[a+128]*c,u-=t[l+-128]*c,c=e[i+5],h+=t[a+192]*c,c=(u-=t[l+-192]*c)-(h*=e[i+6]),n[30+2*_]=u+h,n[31+2*_]=e[i+7]*c,i+=18,a--,l++}u=t[a+-16]*e[i+-10],h=t[a+-32]*e[i+-2],u+=(t[a+-48]-t[a+16])*e[i+-9],h+=t[a+-96]*e[i+-1],u+=(t[a+-80]+t[a+48])*e[i+-8],h+=t[a+-160]*e[i+0],u+=(t[a+-112]-t[a+80])*e[i+-7],h+=t[a+-224]*e[i+1],u+=(t[a+-144]+t[a+112])*e[i+-6],h-=t[a+32]*e[i+2],u+=(t[a+-176]-t[a+144])*e[i+-5],h-=t[a+96]*e[i+3],u+=(t[a+-208]+t[a+176])*e[i+-4],h-=t[a+160]*e[i+4],u+=(t[a+-240]-t[a+208])*e[i+-3],s=(h-=t[a+224])-u,r=h+u,u=n[14],h=n[15]-u,n[31]=r+u,n[30]=s+h,n[15]=s-h,n[14]=r-u,o=n[28]-n[0],n[0]+=n[28],n[28]=o*e[i+-36+7],o=n[29]-n[1],n[1]+=n[29],n[29]=o*e[i+-36+7],o=n[26]-n[2],n[2]+=n[26],n[26]=o*e[i+-72+7],o=n[27]-n[3],n[3]+=n[27],n[27]=o*e[i+-72+7],o=n[24]-n[4],n[4]+=n[24],n[24]=o*e[i+-108+7],o=n[25]-n[5],n[5]+=n[25],n[25]=o*e[i+-108+7],o=n[22]-n[6],n[6]+=n[22],n[22]=o*f.SQRT2,o=n[23]-n[7],n[7]+=n[23],n[23]=o*f.SQRT2-n[7],n[7]-=n[6],n[22]-=n[7],n[23]-=n[22],o=n[6],n[6]=n[31]-o,n[31]=n[31]+o,o=n[7],n[7]=n[30]-o,n[30]=n[30]+o,o=n[22],n[22]=n[15]-o,n[15]=n[15]+o,o=n[23],n[23]=n[14]-o,n[14]=n[14]+o,o=n[20]-n[8],n[8]+=n[20],n[20]=o*e[i+-180+7],o=n[21]-n[9],n[9]+=n[21],n[21]=o*e[i+-180+7],o=n[18]-n[10],n[10]+=n[18],n[18]=o*e[i+-216+7],o=n[19]-n[11],n[11]+=n[19],n[19]=o*e[i+-216+7],o=n[16]-n[12],n[12]+=n[16],n[16]=o*e[i+-252+7],o=n[17]-n[13],n[13]+=n[17],n[17]=o*e[i+-252+7],o=-n[20]+n[24],n[20]+=n[24],n[24]=o*e[i+-216+7],o=-n[21]+n[25],n[21]+=n[25],n[25]=o*e[i+-216+7],o=n[4]-n[8],n[4]+=n[8],n[8]=o*e[i+-216+7],o=n[5]-n[9],n[5]+=n[9],n[9]=o*e[i+-216+7],o=n[0]-n[12],n[0]+=n[12],n[12]=o*e[i+-72+7],o=n[1]-n[13],n[1]+=n[13],n[13]=o*e[i+-72+7],o=n[16]-n[28],n[16]+=n[28],n[28]=o*e[i+-72+7],o=-n[17]+n[29],n[17]+=n[29],n[29]=o*e[i+-72+7],o=f.SQRT2*(n[2]-n[10]),n[2]+=n[10],n[10]=o,o=f.SQRT2*(n[3]-n[11]),n[3]+=n[11],n[11]=o,o=f.SQRT2*(-n[18]+n[26]),n[18]+=n[26],n[26]=o-n[18],o=f.SQRT2*(-n[19]+n[27]),n[19]+=n[27],n[27]=o-n[19],o=n[2],n[19]-=n[3],n[3]-=o,n[2]=n[31]-o,n[31]+=o,o=n[3],n[11]-=n[19],n[18]-=o,n[3]=n[30]-o,n[30]+=o,o=n[18],n[27]-=n[11],n[19]-=o,n[18]=n[15]-o,n[15]+=o,o=n[19],n[10]-=o,n[19]=n[14]-o,n[14]+=o,o=n[10],n[11]-=o,n[10]=n[23]-o,n[23]+=o,o=n[11],n[26]-=o,n[11]=n[22]-o,n[22]+=o,o=n[26],n[27]-=o,n[26]=n[7]-o,n[7]+=o,o=n[27],n[27]=n[6]-o,n[6]+=o,o=f.SQRT2*(n[0]-n[4]),n[0]+=n[4],n[4]=o,o=f.SQRT2*(n[1]-n[5]),n[1]+=n[5],n[5]=o,o=f.SQRT2*(n[16]-n[20]),n[16]+=n[20],n[20]=o,o=f.SQRT2*(n[17]-n[21]),n[17]+=n[21],n[21]=o,o=-f.SQRT2*(n[8]-n[12]),n[8]+=n[12],n[12]=o-n[8],o=-f.SQRT2*(n[9]-n[13]),n[9]+=n[13],n[13]=o-n[9],o=-f.SQRT2*(n[25]-n[29]),n[25]+=n[29],n[29]=o-n[25],o=-f.SQRT2*(n[24]+n[28]),n[24]-=n[28],n[28]=o-n[24],o=n[24]-n[16],n[24]=o,o=n[20]-o,n[20]=o,o=n[28]-o,n[28]=o,o=n[25]-n[17],n[25]=o,o=n[21]-o,n[21]=o,o=n[29]-o,n[29]=o,o=n[17]-n[1],n[17]=o,o=n[9]-o,n[9]=o,o=n[25]-o,n[25]=o,o=n[5]-o,n[5]=o,o=n[21]-o,n[21]=o,o=n[13]-o,n[13]=o,o=n[29]-o,n[29]=o,o=n[1]-n[0],n[1]=o,o=n[16]-o,n[16]=o,o=n[17]-o,n[17]=o,o=n[8]-o,n[8]=o,o=n[9]-o,n[9]=o,o=n[24]-o,n[24]=o,o=n[25]-o,n[25]=o,o=n[4]-o,n[4]=o,o=n[5]-o,n[5]=o,o=n[20]-o,n[20]=o,o=n[21]-o,n[21]=o,o=n[12]-o,n[12]=o,o=n[13]-o,n[13]=o,o=n[28]-o,n[28]=o,o=n[29]-o,n[29]=o,o=n[0],n[0]+=n[31],n[31]-=o,o=n[1],n[1]+=n[30],n[30]-=o,o=n[16],n[16]+=n[15],n[15]-=o,o=n[17],n[17]+=n[14],n[14]-=o,o=n[8],n[8]+=n[23],n[23]-=o,o=n[9],n[9]+=n[22],n[22]-=o,o=n[24],n[24]+=n[7],n[7]-=o,o=n[25],n[25]+=n[6],n[6]-=o,o=n[4],n[4]+=n[27],n[27]-=o,o=n[5],n[5]+=n[26],n[26]-=o,o=n[20],n[20]+=n[11],n[11]-=o,o=n[21],n[21]+=n[10],n[10]-=o,o=n[12],n[12]+=n[19],n[19]-=o,o=n[13],n[13]+=n[18],n[18]-=o,o=n[28],n[28]+=n[3],n[3]-=o,o=n[29],n[29]+=n[2],n[2]-=o}function h(e,a){for(var n=0;n<3;n++){var s,r,o,i,l,_;r=(i=e[a+6]*t[Y.SHORT_TYPE][0]-e[a+15])+(s=e[a+0]*t[Y.SHORT_TYPE][2]-e[a+9]),o=i-s,l=(i=e[a+15]*t[Y.SHORT_TYPE][0]+e[a+6])+(s=e[a+9]*t[Y.SHORT_TYPE][2]+e[a+0]),_=-i+s,s=2069978111953089e-26*(e[a+3]*t[Y.SHORT_TYPE][1]-e[a+12]),i=2069978111953089e-26*(e[a+12]*t[Y.SHORT_TYPE][1]+e[a+3]),e[a+0]=190752519173728e-25*r+s,e[a+15]=190752519173728e-25*-l+i,o=.8660254037844387*o*1907525191737281e-26,l=.5*l*1907525191737281e-26+i,e[a+3]=o-l,e[a+6]=o+l,r=.5*r*1907525191737281e-26-s,_=.8660254037844387*_*1907525191737281e-26,e[a+9]=r+_,e[a+12]=r-_,a++}}function u(e,t,a){var s,r,o,i,l,_,c,f,h,u,m,p,b,d,v,g,S,w;o=a[17]-a[9],l=a[15]-a[11],_=a[14]-a[12],c=a[0]+a[8],f=a[1]+a[7],h=a[2]+a[6],u=a[3]+a[5],e[t+17]=c+h-u-(f-a[4]),r=(c+h-u)*n[19]+(f-a[4]),s=(o-l-_)*n[18],e[t+5]=s+r,e[t+6]=s-r,i=(a[16]-a[10])*n[18],f=f*n[19]+a[4],s=o*n[12]+i+l*n[13]+_*n[14],r=-c*n[16]+f-h*n[17]+u*n[15],e[t+1]=s+r,e[t+2]=s-r,s=o*n[13]-i-l*n[14]+_*n[12],r=-c*n[17]+f-h*n[15]+u*n[16],e[t+9]=s+r,e[t+10]=s-r,s=o*n[14]-i+l*n[12]-_*n[13],r=c*n[15]-f+h*n[16]-u*n[17],e[t+13]=s+r,e[t+14]=s-r,m=a[8]-a[0],b=a[6]-a[2],d=a[5]-a[3],v=a[17]+a[9],g=a[16]+a[10],S=a[15]+a[11],w=a[14]+a[12],e[t+0]=v+S+w+(g+a[13]),s=(v+S+w)*n[19]-(g+a[13]),r=(m-b+d)*n[18],e[t+11]=s+r,e[t+12]=s-r,p=(a[7]-a[1])*n[18],g=a[13]-g*n[19],s=v*n[15]-g+S*n[16]+w*n[17],r=m*n[14]+p+b*n[12]+d*n[13],e[t+3]=s+r,e[t+4]=s-r,s=-v*n[17]+g-S*n[15]-w*n[16],r=m*n[13]+p-b*n[14]-d*n[12],e[t+7]=s+r,e[t+8]=s-r,s=-v*n[16]+g-S*n[17]-w*n[15],r=m*n[12]-p+b*n[13]-d*n[14],e[t+15]=s+r,e[t+16]=s-r}this.mdct_sub48=function(e,n,f){for(var m=n,p=286,b=0;b<e.channels_out;b++){for(var d=0;d<e.mode_gr;d++){for(var v,g=e.l3_side.tt[d][b],S=g.xr,w=0,R=e.sb_sample[b][1-d],M=0,A=0;A<9;A++)for(l(m,p,R[M]),l(m,p+32,R[M+1]),M+=2,p+=64,v=1;v<32;v+=2)R[M-1][v]*=-1;for(v=0;v<32;v++,w+=18){var k=g.block_type,y=e.sb_sample[b][d],T=e.sb_sample[b][1-d];if(0!=g.mixed_block_flag&&v<2&&(k=0),e.amp_filter[v]<1e-12)_.fill(S,w+0,w+18,0);else{if(e.amp_filter[v]<1)for(A=0;A<18;A++)T[A][i[v]]*=e.amp_filter[v];if(k==Y.SHORT_TYPE){for(A=-3;A<0;A++){var B=t[Y.SHORT_TYPE][A+3];S[w+3*A+9]=y[9+A][i[v]]*B-y[8-A][i[v]],S[w+3*A+18]=y[14-A][i[v]]*B+y[15+A][i[v]],S[w+3*A+10]=y[15+A][i[v]]*B-y[14-A][i[v]],S[w+3*A+19]=T[2-A][i[v]]*B+T[3+A][i[v]],S[w+3*A+11]=T[3+A][i[v]]*B-T[2-A][i[v]],S[w+3*A+20]=T[8-A][i[v]]*B+T[9+A][i[v]]}h(S,w)}else{var x=s(18);for(A=-9;A<0;A++){var E,P;E=t[k][A+27]*T[A+9][i[v]]+t[k][A+36]*T[8-A][i[v]],P=t[k][A+9]*y[A+9][i[v]]-t[k][A+18]*y[8-A][i[v]],x[A+9]=E-P*a[3+A+9],x[A+18]=E*a[3+A+9]+P}u(S,w,x)}}if(k!=Y.SHORT_TYPE&&0!=v)for(A=7;A>=0;--A){var I,L;I=S[w+A]*r[20+A]+S[w+-1-A]*o[28+A],L=S[w+A]*o[28+A]-S[w+-1-A]*r[20+A],S[w+-1-A]=I,S[w+A]=L}}}if(m=f,p=286,1==e.mode_gr)for(var C=0;C<18;C++)c.arraycopy(e.sb_sample[b][1][C],0,e.sb_sample[b][0][C],0,32)}}}function N(){this.thm=new W,this.en=new W}function Y(){var e=Y.FFTOFFSET,t=Y.MPG_MD_MS_LR,a=null;this.psy=null;var o=null,i=null,_=null;this.setModules=function(e,t,n,s){a=e,this.psy=t,o=t,i=s,_=n};var f=new V;this.lame_encode_mp3_frame=function(h,u,b,d,v,g){var S,w=l([2,2]);w[0][0]=new N,w[0][1]=new N,w[1][0]=new N,w[1][1]=new N;var R,M=l([2,2]);M[0][0]=new N,M[0][1]=new N,M[1][0]=new N,M[1][1]=new N;var A,k,y,T=[null,null],B=h.internal_flags,x=r([2,4]),E=[.5,.5],P=[[0,0],[0,0]],I=[[0,0],[0,0]];if(T[0]=u,T[1]=b,0==B.lame_encode_frame_init&&function(e,t){var a,n,r=e.internal_flags;if(0==r.lame_encode_frame_init){var o,i,l=s(2014),_=s(2014);for(r.lame_encode_frame_init=1,o=0,i=0;o<286+576*(1+r.mode_gr);++o)o<576*r.mode_gr?(l[o]=0,2==r.channels_out&&(_[o]=0)):(l[o]=t[0][i],2==r.channels_out&&(_[o]=t[1][i]),++i);for(n=0;n<r.mode_gr;n++)for(a=0;a<r.channels_out;a++)r.l3_side.tt[n][a].block_type=Y.SHORT_TYPE;f.mdct_sub48(r,l,_)}}(h,T),B.padding=0,(B.slot_lag-=B.frac_SpF)<0&&(B.slot_lag+=h.out_samplerate,B.padding=1),0!=B.psymodel){var L=[null,null],C=0,D=n(2);for(y=0;y<B.mode_gr;y++){for(k=0;k<B.channels_out;k++)L[k]=T[k],C=576+576*y-Y.FFTOFFSET;if(0!=(h.VBR==m.vbr_mtrh||h.VBR==m.vbr_mt?o.L3psycho_anal_vbr(h,L,C,y,w,M,P[y],I[y],x[y],D):o.L3psycho_anal_ns(h,L,C,y,w,M,P[y],I[y],x[y],D)))return-4;for(h.mode==p.JOINT_STEREO&&(E[y]=x[y][2]+x[y][3],E[y]>0&&(E[y]=x[y][3]/E[y])),k=0;k<B.channels_out;k++){var H=B.l3_side.tt[y][k];H.block_type=D[k],H.mixed_block_flag=0}}}else for(y=0;y<B.mode_gr;y++)for(k=0;k<B.channels_out;k++)B.l3_side.tt[y][k].block_type=Y.NORM_TYPE,B.l3_side.tt[y][k].mixed_block_flag=0,I[y][k]=P[y][k]=700;if(function(e){var t,a;if(0!=e.ATH.useAdjust)if(a=e.loudness_sq[0][0],t=e.loudness_sq[1][0],2==e.channels_out?(a+=e.loudness_sq[0][1],t+=e.loudness_sq[1][1]):(a+=a,t+=t),2==e.mode_gr&&(a=Math.max(a,t)),a*=.5,(a*=e.ATH.aaSensitivityP)>.03125)e.ATH.adjust>=1?e.ATH.adjust=1:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=1;else{var n=31.98*a+625e-6;e.ATH.adjust>=n?(e.ATH.adjust*=.075*n+.925,e.ATH.adjust<n&&(e.ATH.adjust=n)):e.ATH.adjustLimit>=n?e.ATH.adjust=n:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=n}else e.ATH.adjust=1}(B),f.mdct_sub48(B,T[0],T[1]),B.mode_ext=Y.MPG_MD_LR_LR,h.force_ms)B.mode_ext=Y.MPG_MD_MS_LR;else if(h.mode==p.JOINT_STEREO){var O=0,V=0;for(y=0;y<B.mode_gr;y++)for(k=0;k<B.channels_out;k++)O+=I[y][k],V+=P[y][k];if(O<=1*V){var j=B.l3_side.tt[0],q=B.l3_side.tt[B.mode_gr-1];j[0].block_type==j[1].block_type&&q[0].block_type==q[1].block_type&&(B.mode_ext=Y.MPG_MD_MS_LR)}}if(B.mode_ext==t?(R=M,A=I):(R=w,A=P),h.analysis&&null!=B.pinfo)for(y=0;y<B.mode_gr;y++)for(k=0;k<B.channels_out;k++)B.pinfo.ms_ratio[y]=B.ms_ratio[y],B.pinfo.ms_ener_ratio[y]=E[y],B.pinfo.blocktype[y][k]=B.l3_side.tt[y][k].block_type,B.pinfo.pe[y][k]=A[y][k],c.arraycopy(B.l3_side.tt[y][k].xr,0,B.pinfo.xr[y][k],0,576),B.mode_ext==t&&(B.pinfo.ers[y][k]=B.pinfo.ers[y][k+2],c.arraycopy(B.pinfo.energy[y][k+2],0,B.pinfo.energy[y][k],0,B.pinfo.energy[y][k].length));if(h.VBR==m.vbr_off||h.VBR==m.vbr_abr){var W,X;for(W=0;W<18;W++)B.nsPsy.pefirbuf[W]=B.nsPsy.pefirbuf[W+1];for(X=0,y=0;y<B.mode_gr;y++)for(k=0;k<B.channels_out;k++)X+=A[y][k];for(B.nsPsy.pefirbuf[18]=X,X=B.nsPsy.pefirbuf[9],W=0;W<9;W++)X+=(B.nsPsy.pefirbuf[W]+B.nsPsy.pefirbuf[18-W])*Y.fircoef[W];for(X=3350*B.mode_gr*B.channels_out/X,y=0;y<B.mode_gr;y++)for(k=0;k<B.channels_out;k++)A[y][k]*=X}if(B.iteration_loop.iteration_loop(h,A,E,R),a.format_bitstream(h),S=a.copy_buffer(B,d,v,g,1),h.bWriteVbrTag&&i.addVbrFrame(h),h.analysis&&null!=B.pinfo){for(k=0;k<B.channels_out;k++){var F;for(F=0;F<e;F++)B.pinfo.pcmdata[k][F]=B.pinfo.pcmdata[k][F+h.framesize];for(F=e;F<1600;F++)B.pinfo.pcmdata[k][F]=T[k][F-e]}_.set_frame_pinfo(h,R)}return function(e){var t,a;for(e.bitrate_stereoMode_Hist[e.bitrate_index][4]++,e.bitrate_stereoMode_Hist[15][4]++,2==e.channels_out&&(e.bitrate_stereoMode_Hist[e.bitrate_index][e.mode_ext]++,e.bitrate_stereoMode_Hist[15][e.mode_ext]++),t=0;t<e.mode_gr;++t)for(a=0;a<e.channels_out;++a){var n=0|e.l3_side.tt[t][a].block_type;0!=e.l3_side.tt[t][a].mixed_block_flag&&(n=4),e.bitrate_blockType_Hist[e.bitrate_index][n]++,e.bitrate_blockType_Hist[e.bitrate_index][5]++,e.bitrate_blockType_Hist[15][n]++,e.bitrate_blockType_Hist[15][5]++}}(B),S}}function j(){this.sum=0,this.seen=0,this.want=0,this.pos=0,this.size=0,this.bag=null,this.nVbrNumFrames=0,this.nBytesWritten=0,this.TotalFrameSize=0}function q(){this.tt=[[null,null],[null,null]],this.main_data_begin=0,this.private_bits=0,this.resvDrain_pre=0,this.resvDrain_post=0,this.scfsi=[n(4),n(4)];for(var e=0;e<2;e++)for(var t=0;t<2;t++)this.tt[e][t]=new D}function W(){this.l=s(Y.SBMAX_l),this.s=r([Y.SBMAX_s,3]);var e=this;this.assign=function(t){c.arraycopy(t.l,0,e.l,0,Y.SBMAX_l);for(var a=0;a<Y.SBMAX_s;a++)for(var n=0;n<3;n++)e.s[a][n]=t.s[a][n]}}function X(){this.last_en_subshort=r([4,9]),this.lastAttacks=n(4),this.pefirbuf=s(19),this.longfact=s(Y.SBMAX_l),this.shortfact=s(Y.SBMAX_s),this.attackthre=0,this.attackthre_s=0}function F(){function e(){this.write_timing=0,this.ptr=0,this.buf=a(40)}this.Class_ID=0,this.lame_encode_frame_init=0,this.iteration_init_init=0,this.fill_buffer_resample_init=0,this.mfbuf=r([2,F.MFSIZE]),this.mode_gr=0,this.channels_in=0,this.channels_out=0,this.resample_ratio=0,this.mf_samples_to_encode=0,this.mf_size=0,this.VBR_min_bitrate=0,this.VBR_max_bitrate=0,this.bitrate_index=0,this.samplerate_index=0,this.mode_ext=0,this.lowpass1=0,this.lowpass2=0,this.highpass1=0,this.highpass2=0,this.noise_shaping=0,this.noise_shaping_amp=0,this.substep_shaping=0,this.psymodel=0,this.noise_shaping_stop=0,this.subblock_gain=0,this.use_best_huffman=0,this.full_outer_loop=0,this.l3_side=new q,this.ms_ratio=s(2),this.padding=0,this.frac_SpF=0,this.slot_lag=0,this.tag_spec=null,this.nMusicCRC=0,this.OldValue=n(2),this.CurrentStep=n(2),this.masking_lower=0,this.bv_scf=n(576),this.pseudohalf=n(H.SFBMAX),this.sfb21_extra=!1,this.inbuf_old=new Array(2),this.blackfilt=new Array(2*F.BPC+1),this.itime=new Float64Array(2),this.sideinfo_len=0,this.sb_sample=r([2,2,18,Y.SBLIMIT]),this.amp_filter=s(32),this.header=new Array(F.MAX_HEADER_BUF),this.h_ptr=0,this.w_ptr=0,this.ancillary_flag=0,this.ResvSize=0,this.ResvMax=0,this.scalefac_band=new I,this.minval_l=s(Y.CBANDS),this.minval_s=s(Y.CBANDS),this.nb_1=r([4,Y.CBANDS]),this.nb_2=r([4,Y.CBANDS]),this.nb_s1=r([4,Y.CBANDS]),this.nb_s2=r([4,Y.CBANDS]),this.s3_ss=null,this.s3_ll=null,this.decay=0,this.thm=new Array(4),this.en=new Array(4),this.tot_ener=s(4),this.loudness_sq=r([2,2]),this.loudness_sq_save=s(2),this.mld_l=s(Y.SBMAX_l),this.mld_s=s(Y.SBMAX_s),this.bm_l=n(Y.SBMAX_l),this.bo_l=n(Y.SBMAX_l),this.bm_s=n(Y.SBMAX_s),this.bo_s=n(Y.SBMAX_s),this.npart_l=0,this.npart_s=0,this.s3ind=o([Y.CBANDS,2]),this.s3ind_s=o([Y.CBANDS,2]),this.numlines_s=n(Y.CBANDS),this.numlines_l=n(Y.CBANDS),this.rnumlines_l=s(Y.CBANDS),this.mld_cb_l=s(Y.CBANDS),this.mld_cb_s=s(Y.CBANDS),this.numlines_s_num1=0,this.numlines_l_num1=0,this.pe=s(4),this.ms_ratio_s_old=0,this.ms_ratio_l_old=0,this.ms_ener_ratio_old=0,this.blocktype_old=n(2),this.nsPsy=new X,this.VBR_seek_table=new j,this.ATH=null,this.PSY=null,this.nogap_total=0,this.nogap_current=0,this.decode_on_the_fly=!0,this.findReplayGain=!0,this.findPeakSample=!0,this.PeakSample=0,this.RadioGain=0,this.AudiophileGain=0,this.rgdata=null,this.noclipGainChange=0,this.noclipScale=0,this.bitrate_stereoMode_Hist=o([16,5]),this.bitrate_blockType_Hist=o([16,6]),this.pinfo=null,this.hip=null,this.in_buffer_nsamples=0,this.in_buffer_0=null,this.in_buffer_1=null,this.iteration_loop=null;for(var t=0;t<this.en.length;t++)this.en[t]=new W;for(t=0;t<this.thm.length;t++)this.thm[t]=new W;for(t=0;t<this.header.length;t++)this.header[t]=new e}function z(){var e=s(Y.BLKSIZE),t=s(Y.BLKSIZE_s/2),a=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475];function n(e,t,n){var s,r,o,i=0,l=t+(n<<=1);s=4;do{var _,c,h,u,m,p,b;b=s>>1,p=(m=s<<1)+(u=s),s=m<<1,o=(r=t)+b;do{M=e[r+0]-e[r+u],R=e[r+0]+e[r+u],T=e[r+m]-e[r+p],k=e[r+m]+e[r+p],e[r+m]=R-k,e[r+0]=R+k,e[r+p]=M-T,e[r+u]=M+T,M=e[o+0]-e[o+u],R=e[o+0]+e[o+u],T=f.SQRT2*e[o+p],k=f.SQRT2*e[o+m],e[o+m]=R-k,e[o+0]=R+k,e[o+p]=M-T,e[o+u]=M+T,o+=s,r+=s}while(r<l);for(c=a[i+0],_=a[i+1],h=1;h<b;h++){var d,v;d=1-2*_*_,v=2*_*c,r=t+h,o=t+u-h;do{var g,S,w,R,M,A,k,y,T,B;S=v*e[r+u]-d*e[o+u],g=d*e[r+u]+v*e[o+u],M=e[r+0]-g,R=e[r+0]+g,A=e[o+0]-S,w=e[o+0]+S,S=v*e[r+p]-d*e[o+p],g=d*e[r+p]+v*e[o+p],T=e[r+m]-g,k=e[r+m]+g,B=e[o+m]-S,y=e[o+m]+S,S=_*k-c*B,g=c*k+_*B,e[r+m]=R-g,e[r+0]=R+g,e[o+p]=A-S,e[o+u]=A+S,S=c*y-_*T,g=_*y+c*T,e[o+m]=w-g,e[o+0]=w+g,e[r+p]=M-S,e[r+u]=M+S,o+=s,r+=s}while(r<l);c=(d=c)*a[i+0]-_*a[i+1],_=d*a[i+1]+_*a[i+0]}i+=2}while(s<n)}var r=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(e,a,s,o,i){for(var l=0;l<3;l++){var _=Y.BLKSIZE_s/2,c=65535&192*(l+1),f=Y.BLKSIZE_s/8-1;do{var h,u,m,p,b,d=255&r[f<<2];u=(h=t[d]*o[s][i+d+c])-(b=t[127-d]*o[s][i+d+c+128]),h+=b,p=(m=t[d+64]*o[s][i+d+c+64])-(b=t[63-d]*o[s][i+d+c+192]),m+=b,_-=4,a[l][_+0]=h+m,a[l][_+2]=h-m,a[l][_+1]=u+p,a[l][_+3]=u-p,u=(h=t[d+1]*o[s][i+d+c+1])-(b=t[126-d]*o[s][i+d+c+129]),h+=b,p=(m=t[d+65]*o[s][i+d+c+65])-(b=t[62-d]*o[s][i+d+c+193]),m+=b,a[l][_+Y.BLKSIZE_s/2+0]=h+m,a[l][_+Y.BLKSIZE_s/2+2]=h-m,a[l][_+Y.BLKSIZE_s/2+1]=u+p,a[l][_+Y.BLKSIZE_s/2+3]=u-p}while(--f>=0);n(a[l],_,Y.BLKSIZE_s/2)}},this.fft_long=function(t,a,s,o,i){var l=Y.BLKSIZE/8-1,_=Y.BLKSIZE/2;do{var c,f,h,u,m,p=255&r[l];f=(c=e[p]*o[s][i+p])-(m=e[p+512]*o[s][i+p+512]),c+=m,u=(h=e[p+256]*o[s][i+p+256])-(m=e[p+768]*o[s][i+p+768]),h+=m,a[(_-=4)+0]=c+h,a[_+2]=c-h,a[_+1]=f+u,a[_+3]=f-u,f=(c=e[p+1]*o[s][i+p+1])-(m=e[p+513]*o[s][i+p+513]),c+=m,u=(h=e[p+257]*o[s][i+p+257])-(m=e[p+769]*o[s][i+p+769]),h+=m,a[_+Y.BLKSIZE/2+0]=c+h,a[_+Y.BLKSIZE/2+2]=c-h,a[_+Y.BLKSIZE/2+1]=f+u,a[_+Y.BLKSIZE/2+3]=f-u}while(--l>=0);n(a,_,Y.BLKSIZE/2)},this.init_fft=function(a){for(var n=0;n<Y.BLKSIZE;n++)e[n]=.42-.5*Math.cos(2*Math.PI*(n+.5)/Y.BLKSIZE)+.08*Math.cos(4*Math.PI*(n+.5)/Y.BLKSIZE);for(n=0;n<Y.BLKSIZE_s/2;n++)t[n]=.5*(1-Math.cos(2*Math.PI*(n+.5)/Y.BLKSIZE_s))}}function U(){var e=new z,t=2.302585092994046,a=1/217621504/(Y.BLKSIZE/2),o=.3,i=21,l=.2302585093;function c(e){return e}function b(e,t){for(var n=0,s=0;s<Y.BLKSIZE/2;++s)n+=e[s]*t.ATH.eql_w[s];return n*=a}function d(t,a,n,s,r,o,i,l,_,h,u){var m=t.internal_flags;if(_<2)e.fft_long(m,s[r],_,h,u),e.fft_short(m,o[i],_,h,u);else if(2==_){for(var p=Y.BLKSIZE-1;p>=0;--p){var d=s[r+0][p],v=s[r+1][p];s[r+0][p]=(d+v)*f.SQRT2*.5,s[r+1][p]=(d-v)*f.SQRT2*.5}for(var g=2;g>=0;--g)for(p=Y.BLKSIZE_s-1;p>=0;--p){d=o[i+0][g][p],v=o[i+1][g][p];o[i+0][g][p]=(d+v)*f.SQRT2*.5,o[i+1][g][p]=(d-v)*f.SQRT2*.5}}a[0]=s[r+0][0],a[0]*=a[0];for(p=Y.BLKSIZE/2-1;p>=0;--p){var S=s[r+0][Y.BLKSIZE/2-p],w=s[r+0][Y.BLKSIZE/2+p];a[Y.BLKSIZE/2-p]=c(.5*(S*S+w*w))}for(g=2;g>=0;--g){n[g][0]=o[i+0][g][0],n[g][0]*=n[g][0];for(p=Y.BLKSIZE_s/2-1;p>=0;--p){S=o[i+0][g][Y.BLKSIZE_s/2-p],w=o[i+0][g][Y.BLKSIZE_s/2+p];n[g][Y.BLKSIZE_s/2-p]=c(.5*(S*S+w*w))}}var R=0;for(p=11;p<Y.HBLKSIZE;p++)R+=a[p];if(m.tot_ener[_]=R,t.analysis){for(p=0;p<Y.HBLKSIZE;p++)m.pinfo.energy[l][_][p]=m.pinfo.energy_save[_][p],m.pinfo.energy_save[_][p]=a[p];m.pinfo.pe[l][_]=m.pe[_]}2==t.athaa_loudapprox&&_<2&&(m.loudness_sq[l][_]=m.loudness_sq_save[_],m.loudness_sq_save[_]=b(a,m))}var v,g,S,w=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749];var R=[3.3246*3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],M=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],A=[5.5396212496,2.29259*2.29259,4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276];function k(e,t,a,n,s,r){var o;if(t>e){if(!(t<e*g))return e+t;o=t/e}else{if(e>=t*g)return e+t;o=e/t}if(e+=t,n+3<=6){if(o>=v)return e;var i=0|f.FAST_LOG10_X(o,16);return e*M[i]}var l,_;i=0|f.FAST_LOG10_X(o,16);return t=s.ATH.cb_l[a]*s.ATH.adjust,e<S*t?e>t?(l=1,i<=13&&(l=A[i]),_=f.FAST_LOG10_X(e/t,10/15),e*((R[i]-l)*_+l)):i>13?e:e*A[i]:e*R[i]}var y=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1];function T(e,t,a){var n;if(e<0&&(e=0),t<0&&(t=0),e<=0)return t;if(t<=0)return e;if(n=t>e?t/e:e/t,-2<=a&&a<=2){if(n>=v)return e+t;var s=0|f.FAST_LOG10_X(n,16);return(e+t)*y[s]}return n<g?e+t:(e<t&&(e=t),e)}function B(e,t,a,n,s){var r,o,i=0,l=0;for(r=o=0;r<Y.SBMAX_s;++o,++r){for(var _=e.bo_s[r],c=e.npart_s,f=_<c?_:c;o<f;)i+=t[o],l+=a[o],o++;if(e.en[n].s[r][s]=i,e.thm[n].s[r][s]=l,o>=c){++r;break}var h=e.PSY.bo_s_weight[r],u=1-h;i=h*t[o],l=h*a[o],e.en[n].s[r][s]+=i,e.thm[n].s[r][s]+=l,i=u*t[o],l=u*a[o]}for(;r<Y.SBMAX_s;++r)e.en[n].s[r][s]=0,e.thm[n].s[r][s]=0}function x(e,t,a,n){var s,r,o=0,i=0;for(s=r=0;s<Y.SBMAX_l;++r,++s){for(var l=e.bo_l[s],_=e.npart_l,c=l<_?l:_;r<c;)o+=t[r],i+=a[r],r++;if(e.en[n].l[s]=o,e.thm[n].l[s]=i,r>=_){++s;break}var f=e.PSY.bo_l_weight[s],h=1-f;o=f*t[r],i=f*a[r],e.en[n].l[s]+=o,e.thm[n].l[s]+=i,o=h*t[r],i=h*a[r]}for(;s<Y.SBMAX_l;++s)e.en[n].l[s]=0,e.thm[n].l[s]=0}function E(e,t,a,n,s,r){var o,i,l=e.internal_flags;for(i=o=0;i<l.npart_s;++i){for(var _=0,c=l.numlines_s[i],f=0;f<c;++f,++o){_+=t[r][o]}a[i]=_}for(o=i=0;i<l.npart_s;i++){var h=l.s3ind_s[i][0],u=l.s3_ss[o++]*a[h];for(++h;h<=l.s3ind_s[i][1];)u+=l.s3_ss[o]*a[h],++o,++h;var m=2*l.nb_s1[s][i];if(n[i]=Math.min(u,m),l.blocktype_old[1&s]==Y.SHORT_TYPE){m=16*l.nb_s2[s][i];var p=n[i];n[i]=Math.min(m,p)}l.nb_s2[s][i]=l.nb_s1[s][i],l.nb_s1[s][i]=u}for(;i<=Y.CBANDS;++i)a[i]=0,n[i]=0}function P(e,t,a){return a>=1?e:a<=0?t:t>0?Math.pow(e/t,a)*t:0}var I=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130];function L(e,a){for(var n=309.07,s=0;s<Y.SBMAX_s-1;s++)for(var r=0;r<3;r++){var o=e.thm.s[s][r];if(o>0){var i=o*a,l=e.en.s[s][r];l>i&&(n+=l>1e10*i?I[s]*(10*t):I[s]*f.FAST_LOG10(l/i))}}return n}var C=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1];function D(e,a){for(var n=281.0575,s=0;s<Y.SBMAX_l-1;s++){var r=e.thm.l[s];if(r>0){var o=r*a,i=e.en.l[s];i>o&&(n+=i>1e10*o?C[s]*(10*t):C[s]*f.FAST_LOG10(i/o))}}return n}function H(e,t,a,n,s){var r,o;for(r=o=0;r<e.npart_l;++r){var i,l=0,_=0;for(i=0;i<e.numlines_l[r];++i,++o){var c=t[o];l+=c,_<c&&(_=c)}a[r]=l,n[r]=_,s[r]=l*e.rnumlines_l[r]}}function O(e,t,a,n){var s=w.length-1,r=0,o=a[r]+a[r+1];o>0?((i=t[r])<t[r+1]&&(i=t[r+1]),(l=0|(o=20*(2*i-o)/(o*(e.numlines_l[r]+e.numlines_l[r+1]-1))))>s&&(l=s),n[r]=l):n[r]=0;for(r=1;r<e.npart_l-1;r++){var i,l;if((o=a[r-1]+a[r]+a[r+1])>0)(i=t[r-1])<t[r]&&(i=t[r]),i<t[r+1]&&(i=t[r+1]),(l=0|(o=20*(3*i-o)/(o*(e.numlines_l[r-1]+e.numlines_l[r]+e.numlines_l[r+1]-1))))>s&&(l=s),n[r]=l;else n[r]=0}(o=a[r-1]+a[r])>0?((i=t[r-1])<t[r]&&(i=t[r]),(l=0|(o=20*(2*i-o)/(o*(e.numlines_l[r-1]+e.numlines_l[r]-1))))>s&&(l=s),n[r]=l):n[r]=0}var V=[-1730326e-23,-.01703172,-1349528e-23,.0418072,-673278e-22,-.0876324,-30835e-21,.1863476,-1104424e-22,-.627638];function N(t,a,n,s,r,o,i,l){var _=t.internal_flags;if(s<2)e.fft_long(_,i[l],s,a,n);else if(2==s)for(var h=Y.BLKSIZE-1;h>=0;--h){var u=i[l+0][h],m=i[l+1][h];i[l+0][h]=(u+m)*f.SQRT2*.5,i[l+1][h]=(u-m)*f.SQRT2*.5}o[0]=i[l+0][0],o[0]*=o[0];for(h=Y.BLKSIZE/2-1;h>=0;--h){var p=i[l+0][Y.BLKSIZE/2-h],b=i[l+0][Y.BLKSIZE/2+h];o[Y.BLKSIZE/2-h]=c(.5*(p*p+b*b))}var d=0;for(h=11;h<Y.HBLKSIZE;h++)d+=o[h];if(_.tot_ener[s]=d,t.analysis){for(h=0;h<Y.HBLKSIZE;h++)_.pinfo.energy[r][s][h]=_.pinfo.energy_save[s][h],_.pinfo.energy_save[s][h]=o[h];_.pinfo.pe[r][s]=_.pe[s]}}function j(t,a,n,s,r,o,i,l){var _=t.internal_flags;if(0==r&&s<2&&e.fft_short(_,i[l],s,a,n),2==s)for(var h=Y.BLKSIZE_s-1;h>=0;--h){var u=i[l+0][r][h],m=i[l+1][r][h];i[l+0][r][h]=(u+m)*f.SQRT2*.5,i[l+1][r][h]=(u-m)*f.SQRT2*.5}o[r][0]=i[l+0][r][0],o[r][0]*=o[r][0];for(h=Y.BLKSIZE_s/2-1;h>=0;--h){var p=i[l+0][r][Y.BLKSIZE_s/2-h],b=i[l+0][r][Y.BLKSIZE_s/2+h];o[r][Y.BLKSIZE_s/2-h]=c(.5*(p*p+b*b))}}function q(e,t,a,n){var s=e.internal_flags;2==e.athaa_loudapprox&&a<2&&(s.loudness_sq[t][a]=s.loudness_sq_save[a],s.loudness_sq_save[a]=b(n,s))}this.L3psycho_anal_ns=function(e,t,a,l,c,f,u,b,v,g){var S,R,M,A,y,T,I,C,N,j,q=e.internal_flags,W=r([2,Y.BLKSIZE]),X=r([2,3,Y.BLKSIZE_s]),F=s(Y.CBANDS+1),z=s(Y.CBANDS+1),U=s(Y.CBANDS+2),K=n(2),Z=n(2),G=r([2,576]),Q=n(Y.CBANDS+2),$=n(Y.CBANDS+2);for(_.fill($,0),S=q.channels_out,e.mode==p.JOINT_STEREO&&(S=4),N=e.VBR==m.vbr_off?0==q.ResvMax?0:q.ResvSize/q.ResvMax*.5:e.VBR==m.vbr_rh||e.VBR==m.vbr_mtrh||e.VBR==m.vbr_mt?.6:1,R=0;R<q.channels_out;R++){var J=t[R],ee=a+576-350-i+192;for(A=0;A<576;A++){var te,ae;for(te=J[ee+A+10],ae=0,y=0;y<9;y+=2)te+=V[y]*(J[ee+A+y]+J[ee+A+i-y]),ae+=V[y+1]*(J[ee+A+y+1]+J[ee+A+i-y-1]);G[R][A]=te+ae}c[l][R].en.assign(q.en[R]),c[l][R].thm.assign(q.thm[R]),S>2&&(f[l][R].en.assign(q.en[R+2]),f[l][R].thm.assign(q.thm[R+2]))}for(R=0;R<S;R++){var ne,se=s(12),re=[0,0,0,0],oe=s(12),ie=1,le=s(Y.CBANDS),_e=s(Y.CBANDS),ce=[0,0,0,0],fe=s(Y.HBLKSIZE),he=r([3,Y.HBLKSIZE_s]);for(A=0;A<3;A++)se[A]=q.nsPsy.last_en_subshort[R][A+6],oe[A]=se[A]/q.nsPsy.last_en_subshort[R][A+4],re[0]+=se[A];if(2==R)for(A=0;A<576;A++){var ue,me;ue=G[0][A],me=G[1][A],G[0][A]=ue+me,G[1][A]=ue-me}var pe=G[1&R],be=0;for(A=0;A<9;A++){for(var de=be+64,ve=1;be<de;be++)ve<Math.abs(pe[be])&&(ve=Math.abs(pe[be]));q.nsPsy.last_en_subshort[R][A]=se[A+3]=ve,re[1+A/3]+=ve,ve>se[A+3-2]?ve/=se[A+3-2]:ve=se[A+3-2]>10*ve?se[A+3-2]/(10*ve):0,oe[A+3]=ve}if(e.analysis){var ge=oe[0];for(A=1;A<12;A++)ge<oe[A]&&(ge=oe[A]);q.pinfo.ers[l][R]=q.pinfo.ers_save[R],q.pinfo.ers_save[R]=ge}for(ne=3==R?q.nsPsy.attackthre_s:q.nsPsy.attackthre,A=0;A<12;A++)0==ce[A/3]&&oe[A]>ne&&(ce[A/3]=A%3+1);for(A=1;A<4;A++){(re[A-1]>re[A]?re[A-1]/re[A]:re[A]/re[A-1])<1.7&&(ce[A]=0,1==A&&(ce[0]=0))}for(0!=ce[0]&&0!=q.nsPsy.lastAttacks[R]&&(ce[0]=0),3!=q.nsPsy.lastAttacks[R]&&ce[0]+ce[1]+ce[2]+ce[3]==0||(ie=0,0!=ce[1]&&0!=ce[0]&&(ce[1]=0),0!=ce[2]&&0!=ce[1]&&(ce[2]=0),0!=ce[3]&&0!=ce[2]&&(ce[3]=0)),R<2?Z[R]=ie:0==ie&&(Z[0]=Z[1]=0),v[R]=q.tot_ener[R],d(e,fe,he,W,1&R,X,1&R,l,R,t,a),H(q,fe,F,le,_e),O(q,le,_e,Q),C=0;C<3;C++){var Se,we;for(E(e,he,z,U,R,C),B(q,z,U,R,C),I=0;I<Y.SBMAX_s;I++){if(we=q.thm[R].s[I][C],we*=.8,ce[C]>=2||1==ce[C+1]){var Re=0!=C?C-1:2;ve=P(q.thm[R].s[I][Re],we,.6*N);we=Math.min(we,ve)}if(1==ce[C]){Re=0!=C?C-1:2,ve=P(q.thm[R].s[I][Re],we,o*N);we=Math.min(we,ve)}else if(0!=C&&3==ce[C-1]||0==C&&3==q.nsPsy.lastAttacks[R]){Re=2!=C?C+1:0,ve=P(q.thm[R].s[I][Re],we,o*N);we=Math.min(we,ve)}Se=se[3*C+3]+se[3*C+4]+se[3*C+5],6*se[3*C+5]<Se&&(we*=.5,6*se[3*C+4]<Se&&(we*=.5)),q.thm[R].s[I][C]=we}}for(q.nsPsy.lastAttacks[R]=ce[2],T=0,M=0;M<q.npart_l;M++){for(var Me=q.s3ind[M][0],Ae=F[Me]*w[Q[Me]],ke=q.s3_ll[T++]*Ae;++Me<=q.s3ind[M][1];)Ae=F[Me]*w[Q[Me]],ke=k(ke,q.s3_ll[T++]*Ae,Me,Me-M,q);ke*=.158489319246111,q.blocktype_old[1&R]==Y.SHORT_TYPE?U[M]=ke:U[M]=P(Math.min(ke,Math.min(2*q.nb_1[R][M],16*q.nb_2[R][M])),ke,N),q.nb_2[R][M]=q.nb_1[R][M],q.nb_1[R][M]=ke}for(;M<=Y.CBANDS;++M)F[M]=0,U[M]=0;x(q,F,U,R)}(e.mode!=p.STEREO&&e.mode!=p.JOINT_STEREO||e.interChRatio>0&&function(e,t){var a=e.internal_flags;if(a.channels_out>1){for(var n=0;n<Y.SBMAX_l;n++){var s=a.thm[0].l[n],r=a.thm[1].l[n];a.thm[0].l[n]+=r*t,a.thm[1].l[n]+=s*t}for(n=0;n<Y.SBMAX_s;n++)for(var o=0;o<3;o++)s=a.thm[0].s[n][o],r=a.thm[1].s[n][o],a.thm[0].s[n][o]+=r*t,a.thm[1].s[n][o]+=s*t}}(e,e.interChRatio),e.mode==p.JOINT_STEREO)&&(!function(e){for(var t=0;t<Y.SBMAX_l;t++)if(!(e.thm[0].l[t]>1.58*e.thm[1].l[t]||e.thm[1].l[t]>1.58*e.thm[0].l[t])){var a=e.mld_l[t]*e.en[3].l[t],n=Math.max(e.thm[2].l[t],Math.min(e.thm[3].l[t],a));a=e.mld_l[t]*e.en[2].l[t];var s=Math.max(e.thm[3].l[t],Math.min(e.thm[2].l[t],a));e.thm[2].l[t]=n,e.thm[3].l[t]=s}for(t=0;t<Y.SBMAX_s;t++)for(var r=0;r<3;r++)e.thm[0].s[t][r]>1.58*e.thm[1].s[t][r]||e.thm[1].s[t][r]>1.58*e.thm[0].s[t][r]||(a=e.mld_s[t]*e.en[3].s[t][r],n=Math.max(e.thm[2].s[t][r],Math.min(e.thm[3].s[t][r],a)),a=e.mld_s[t]*e.en[2].s[t][r],s=Math.max(e.thm[3].s[t][r],Math.min(e.thm[2].s[t][r],a)),e.thm[2].s[t][r]=n,e.thm[3].s[t][r]=s)}(q),j=e.msfix,Math.abs(j)>0&&function(e,t,a){var n=t,s=Math.pow(10,a);t*=2,n*=2;for(var r=0;r<Y.SBMAX_l;r++)c=e.ATH.cb_l[e.bm_l[r]]*s,(i=Math.min(Math.max(e.thm[0].l[r],c),Math.max(e.thm[1].l[r],c)))*t<(l=Math.max(e.thm[2].l[r],c))+(_=Math.max(e.thm[3].l[r],c))&&(l*=f=i*n/(l+_),_*=f),e.thm[2].l[r]=Math.min(l,e.thm[2].l[r]),e.thm[3].l[r]=Math.min(_,e.thm[3].l[r]);for(s*=Y.BLKSIZE_s/Y.BLKSIZE,r=0;r<Y.SBMAX_s;r++)for(var o=0;o<3;o++){var i,l,_,c,f;c=e.ATH.cb_s[e.bm_s[r]]*s,(i=Math.min(Math.max(e.thm[0].s[r][o],c),Math.max(e.thm[1].s[r][o],c)))*t<(l=Math.max(e.thm[2].s[r][o],c))+(_=Math.max(e.thm[3].s[r][o],c))&&(l*=f=i*t/(l+_),_*=f),e.thm[2].s[r][o]=Math.min(e.thm[2].s[r][o],l),e.thm[3].s[r][o]=Math.min(e.thm[3].s[r][o],_)}}(q,j,e.ATHlower*q.ATH.adjust));for(function(e,t,a,n){var s=e.internal_flags;e.short_blocks!=h.short_block_coupled||0!=t[0]&&0!=t[1]||(t[0]=t[1]=0);for(var r=0;r<s.channels_out;r++)n[r]=Y.NORM_TYPE,e.short_blocks==h.short_block_dispensed&&(t[r]=1),e.short_blocks==h.short_block_forced&&(t[r]=0),0!=t[r]?s.blocktype_old[r]==Y.SHORT_TYPE&&(n[r]=Y.STOP_TYPE):(n[r]=Y.SHORT_TYPE,s.blocktype_old[r]==Y.NORM_TYPE&&(s.blocktype_old[r]=Y.START_TYPE),s.blocktype_old[r]==Y.STOP_TYPE&&(s.blocktype_old[r]=Y.SHORT_TYPE)),a[r]=s.blocktype_old[r],s.blocktype_old[r]=n[r]}(e,Z,g,K),R=0;R<S;R++){var ye,Te,Be,xe=0;R>1?(ye=b,xe=-2,Te=Y.NORM_TYPE,g[0]!=Y.SHORT_TYPE&&g[1]!=Y.SHORT_TYPE||(Te=Y.SHORT_TYPE),Be=f[l][R-2]):(ye=u,xe=0,Te=g[R],Be=c[l][R]),Te==Y.SHORT_TYPE?ye[xe+R]=L(Be,q.masking_lower):ye[xe+R]=D(Be,q.masking_lower),e.analysis&&(q.pinfo.pe[l][R]=ye[xe+R])}return 0};var W=[-1730326e-23,-.01703172,-1349528e-23,.0418072,-673278e-22,-.0876324,-30835e-21,.1863476,-1104424e-22,-.627638];function X(e,t,a){if(0==a)for(var n=0;n<e.npart_s;n++)e.nb_s2[t][n]=e.nb_s1[t][n],e.nb_s1[t][n]=0}function F(e,t){for(var a=0;a<e.npart_l;a++)e.nb_2[t][a]=e.nb_1[t][a],e.nb_1[t][a]=0}function U(e,t,a,n,r,o){var i,l,_,c=e.internal_flags,f=new float[Y.CBANDS],h=s(Y.CBANDS),u=new int[Y.CBANDS];for(_=l=0;_<c.npart_s;++_){var m=0,p=0,b=c.numlines_s[_];for(i=0;i<b;++i,++l){var d=t[o][l];m+=d,p<d&&(p=d)}a[_]=m,f[_]=p,h[_]=m/b}for(;_<Y.CBANDS;++_)f[_]=0,h[_]=0;for(function(e,t,a,n){var s=w.length-1,r=0,o=a[r]+a[r+1];for(o>0?((i=t[r])<t[r+1]&&(i=t[r+1]),(l=0|(o=20*(2*i-o)/(o*(e.numlines_s[r]+e.numlines_s[r+1]-1))))>s&&(l=s),n[r]=l):n[r]=0,r=1;r<e.npart_s-1;r++){var i,l;(o=a[r-1]+a[r]+a[r+1])>0?((i=t[r-1])<t[r]&&(i=t[r]),i<t[r+1]&&(i=t[r+1]),(l=0|(o=20*(3*i-o)/(o*(e.numlines_s[r-1]+e.numlines_s[r]+e.numlines_s[r+1]-1))))>s&&(l=s),n[r]=l):n[r]=0}(o=a[r-1]+a[r])>0?((i=t[r-1])<t[r]&&(i=t[r]),(l=0|(o=20*(2*i-o)/(o*(e.numlines_s[r-1]+e.numlines_s[r]-1))))>s&&(l=s),n[r]=l):n[r]=0}(c,f,h,u),l=_=0;_<c.npart_s;_++){var v,g,S,R,M,A=c.s3ind_s[_][0],k=c.s3ind_s[_][1];for(v=u[A],g=1,R=c.s3_ss[l]*a[A]*w[u[A]],++l,++A;A<=k;)v+=u[A],g+=1,R=T(R,S=c.s3_ss[l]*a[A]*w[u[A]],A-_),++l,++A;R*=M=.5*w[v=(1+2*v)/(2*g)],n[_]=R,c.nb_s2[r][_]=c.nb_s1[r][_],c.nb_s1[r][_]=R,S=f[_],S*=c.minval_s[_],S*=M,n[_]>S&&(n[_]=S),c.masking_lower>1&&(n[_]*=c.masking_lower),n[_]>a[_]&&(n[_]=a[_]),c.masking_lower<1&&(n[_]*=c.masking_lower)}for(;_<Y.CBANDS;++_)a[_]=0,n[_]=0}function K(e,t,a,r,i){var l,_=s(Y.CBANDS),c=s(Y.CBANDS),f=n(Y.CBANDS+2);H(e,t,a,_,c),O(e,_,c,f);var h=0;for(l=0;l<e.npart_l;l++){var u,m,p,b=e.s3ind[l][0],d=e.s3ind[l][1],v=0,g=0;for(v=f[b],g+=1,m=e.s3_ll[h]*a[b]*w[f[b]],++h,++b;b<=d;)v+=f[b],g+=1,m=T(m,u=e.s3_ll[h]*a[b]*w[f[b]],b-l),++h,++b;if(m*=p=.5*w[v=(1+2*v)/(2*g)],e.blocktype_old[1&i]==Y.SHORT_TYPE){var S=2*e.nb_1[i][l];r[l]=S>0?Math.min(m,S):Math.min(m,a[l]*o)}else{var R=16*e.nb_2[i][l],M=2*e.nb_1[i][l];R<=0&&(R=m),M<=0&&(M=m),S=e.blocktype_old[1&i]==Y.NORM_TYPE?Math.min(M,R):M,r[l]=Math.min(m,S)}e.nb_2[i][l]=e.nb_1[i][l],e.nb_1[i][l]=m,u=_[l],u*=e.minval_l[l],u*=p,r[l]>u&&(r[l]=u),e.masking_lower>1&&(r[l]*=e.masking_lower),r[l]>a[l]&&(r[l]=a[l]),e.masking_lower<1&&(r[l]*=e.masking_lower)}for(;l<Y.CBANDS;++l)a[l]=0,r[l]=0}function Z(e,t,a,n,s,r,o){for(var i,l,_=2*r,c=r>0?Math.pow(10,s):1,f=0;f<o;++f){var h=e[2][f],u=e[3][f],m=t[0][f],p=t[1][f],b=t[2][f],d=t[3][f];if(m<=1.58*p&&p<=1.58*m){var v=a[f]*u,g=a[f]*h;l=Math.max(b,Math.min(d,v)),i=Math.max(d,Math.min(b,g))}else l=b,i=d;if(r>0){var S,w,R=n[f]*c;if(S=Math.min(Math.max(m,R),Math.max(p,R)),(w=(b=Math.max(l,R))+(d=Math.max(i,R)))>0&&S*_<w){var M=S*_/w;b*=M,d*=M}l=Math.min(b,l),i=Math.min(d,i)}l>h&&(l=h),i>u&&(i=u),t[2][f]=l,t[3][f]=i}}function G(e,t){var a;return(a=e>=0?27*-e:e*t)<=-72?0:Math.exp(a*l)}function Q(e){var t,a,n=0;for(n=0;G(n,e)>1e-20;n-=1);for(s=n,r=0;Math.abs(r-s)>1e-12;)G(n=(r+s)/2,e)>0?r=n:s=n;t=s;var s,r;n=0;for(n=0;G(n,e)>1e-20;n+=1);for(s=0,r=n;Math.abs(r-s)>1e-12;)G(n=(r+s)/2,e)>0?s=n:r=n;a=r;var o,i=0,l=1e3;for(o=0;o<=l;++o){i+=G(n=t+o*(a-t)/l,e)}return(l+1)/(i*(a-t))}function $(e){var t,a,n,s;return t=e,a=(t*=t>=0?3:1.5)>=.5&&t<=2.5?8*((s=t-.5)*s-2*s):0,(n=15.811389+7.5*(t+=.474)-17.5*Math.sqrt(1+t*t))<=-60?0:(t=Math.exp((a+n)*l),t/=.6609193)}function J(e){return e<0&&(e=0),e*=.001,13*Math.atan(.76*e)+3.5*Math.atan(e*e/56.25)}function ee(e,t,a,r,o,i,l,_,c,f,h,u){var m,p=s(Y.CBANDS+1),b=_/(u>15?1152:384),d=n(Y.HBLKSIZE);_/=c;var v=0,g=0;for(m=0;m<Y.CBANDS;m++){var S;for(x=J(_*v),p[m]=_*v,S=v;J(_*S)-x<.34&&S<=c/2;S++);for(e[m]=S-v,g=m+1;v<S;)d[v++]=m;if(v>c/2){v=c/2,++m;break}}p[m]=_*v;for(var w=0;w<u;w++){var R,M,A,k,y;A=f[w],k=f[w+1],(R=0|Math.floor(.5+h*(A-.5)))<0&&(R=0),(M=0|Math.floor(.5+h*(k-.5)))>c/2&&(M=c/2),a[w]=(d[R]+d[M])/2,t[w]=d[M];var T=b*k;l[w]=(T-p[t[w]])/(p[t[w]+1]-p[t[w]]),l[w]<0?l[w]=0:l[w]>1&&(l[w]=1),y=J(_*f[w]*h),y=Math.min(y,15.5)/15.5,i[w]=Math.pow(10,1.25*(1-Math.cos(Math.PI*y))-2.5)}v=0;for(var B=0;B<g;B++){var x,E,P=e[B];x=J(_*v),E=J(_*(v+P-1)),r[B]=.5*(x+E),x=J(_*(v-.5)),E=J(_*(v+P-.5)),o[B]=E-x,v+=P}return g}function te(e,t,a,n,o,i){var l,_=r([Y.CBANDS,Y.CBANDS]),c=0;if(i)for(var f=0;f<t;f++)for(l=0;l<t;l++){var h=$(a[f]-a[l])*n[l];_[f][l]=h*o[f]}else for(l=0;l<t;l++){var u=15+Math.min(21/a[l],12),m=Q(u);for(f=0;f<t;f++){h=m*G(a[f]-a[l],u)*n[l];_[f][l]=h*o[f]}}for(f=0;f<t;f++){for(l=0;l<t&&!(_[f][l]>0);l++);for(e[f][0]=l,l=t-1;l>0&&!(_[f][l]>0);l--);e[f][1]=l,c+=e[f][1]-e[f][0]+1}var p=s(c),b=0;for(f=0;f<t;f++)for(l=e[f][0];l<=e[f][1];l++)p[b++]=_[f][l];return p}function ae(e){var t=J(e);return t=Math.min(t,15.5)/15.5,Math.pow(10,1.25*(1-Math.cos(Math.PI*t))-2.5)}function ne(e,t){return e<-.3&&(e=3410),e/=1e3,e=Math.max(.1,e),3.64*Math.pow(e,-.8)-6.8*Math.exp(-.6*Math.pow(e-3.4,2))+6*Math.exp(-.15*Math.pow(e-8.7,2))+.001*(.6+.04*t)*Math.pow(e,4)}this.L3psycho_anal_vbr=function(e,t,a,o,l,_,c,f,u,m){var b=e.internal_flags,d=s(Y.HBLKSIZE),v=r([3,Y.HBLKSIZE_s]),g=r([2,Y.BLKSIZE]),S=r([2,3,Y.BLKSIZE_s]),w=r([4,Y.CBANDS]),R=r([4,Y.CBANDS]),M=r([4,3]),A=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]],k=n(2),y=e.mode==p.JOINT_STEREO?4:b.channels_out;!function(e,t,a,n,o,l,_,c,f,h){for(var u=r([2,576]),m=e.internal_flags,b=m.channels_out,d=e.mode==p.JOINT_STEREO?4:b,v=0;v<b;v++){firbuf=t[v];for(var g=a+576-350-i+192,S=0;S<576;S++){var w,R;w=firbuf[g+S+10],R=0;for(var M=0;M<9;M+=2)w+=W[M]*(firbuf[g+S+M]+firbuf[g+S+i-M]),R+=W[M+1]*(firbuf[g+S+M+1]+firbuf[g+S+i-M-1]);u[v][S]=w+R}o[n][v].en.assign(m.en[v]),o[n][v].thm.assign(m.thm[v]),d>2&&(l[n][v].en.assign(m.en[v+2]),l[n][v].thm.assign(m.thm[v+2]))}for(v=0;v<d;v++){var A=s(12),k=s(12),y=[0,0,0,0],T=u[1&v],B=0,x=3==v?m.nsPsy.attackthre_s:m.nsPsy.attackthre,E=1;if(2==v)for(S=0,M=576;M>0;++S,--M){var P=u[0][S],I=u[1][S];u[0][S]=P+I,u[1][S]=P-I}for(S=0;S<3;S++)k[S]=m.nsPsy.last_en_subshort[v][S+6],A[S]=k[S]/m.nsPsy.last_en_subshort[v][S+4],y[0]+=k[S];for(S=0;S<9;S++){for(var L=B+64,C=1;B<L;B++)C<Math.abs(T[B])&&(C=Math.abs(T[B]));m.nsPsy.last_en_subshort[v][S]=k[S+3]=C,y[1+S/3]+=C,C>k[S+3-2]?C/=k[S+3-2]:C=k[S+3-2]>10*C?k[S+3-2]/(10*C):0,A[S+3]=C}for(S=0;S<3;++S){var D=k[3*S+3]+k[3*S+4]+k[3*S+5],H=1;6*k[3*S+5]<D&&(H*=.5,6*k[3*S+4]<D&&(H*=.5)),c[v][S]=H}if(e.analysis){var O=A[0];for(S=1;S<12;S++)O<A[S]&&(O=A[S]);m.pinfo.ers[n][v]=m.pinfo.ers_save[v],m.pinfo.ers_save[v]=O}for(S=0;S<12;S++)0==f[v][S/3]&&A[S]>x&&(f[v][S/3]=S%3+1);for(S=1;S<4;S++){var V=y[S-1],N=y[S];Math.max(V,N)<4e4&&V<1.7*N&&N<1.7*V&&(1==S&&f[v][0]<=f[v][S]&&(f[v][0]=0),f[v][S]=0)}f[v][0]<=m.nsPsy.lastAttacks[v]&&(f[v][0]=0),3!=m.nsPsy.lastAttacks[v]&&f[v][0]+f[v][1]+f[v][2]+f[v][3]==0||(E=0,0!=f[v][1]&&0!=f[v][0]&&(f[v][1]=0),0!=f[v][2]&&0!=f[v][1]&&(f[v][2]=0),0!=f[v][3]&&0!=f[v][2]&&(f[v][3]=0)),v<2?h[v]=E:0==E&&(h[0]=h[1]=0),_[v]=m.tot_ener[v]}}(e,t,a,o,l,_,u,M,A,k),function(e,t){var a=e.internal_flags;e.short_blocks!=h.short_block_coupled||0!=t[0]&&0!=t[1]||(t[0]=t[1]=0);for(var n=0;n<a.channels_out;n++)e.short_blocks==h.short_block_dispensed&&(t[n]=1),e.short_blocks==h.short_block_forced&&(t[n]=0)}(e,k);for(var T=0;T<y;T++){N(e,t,a,T,o,d,g,I=1&T),q(e,o,T,d),0!=k[I]?K(b,d,w[T],R[T],T):F(b,T)}k[0]+k[1]==2&&e.mode==p.JOINT_STEREO&&Z(w,R,b.mld_cb_l,b.ATH.cb_l,e.ATHlower*b.ATH.adjust,e.msfix,b.npart_l);for(T=0;T<y;T++){0!=k[I=1&T]&&x(b,w[T],R[T],T)}for(var E=0;E<3;E++){for(T=0;T<y;++T){0!=k[I=1&T]?X(b,T,E):(j(e,t,a,T,E,v,S,I),U(e,v,w[T],R[T],T,E))}k[0]+k[1]==0&&e.mode==p.JOINT_STEREO&&Z(w,R,b.mld_cb_s,b.ATH.cb_s,e.ATHlower*b.ATH.adjust,e.msfix,b.npart_s);for(T=0;T<y;++T){0==k[I=1&T]&&B(b,w[T],R[T],T,E)}}for(T=0;T<y;T++){var I;if(0==k[I=1&T])for(var C=0;C<Y.SBMAX_s;C++){var H=s(3);for(E=0;E<3;E++){var O=b.thm[T].s[C][E];if(O*=.8,A[T][E]>=2||1==A[T][E+1]){var V=0!=E?E-1:2,z=P(b.thm[T].s[C][V],O,.36);O=Math.min(O,z)}else if(1==A[T][E]){V=0!=E?E-1:2,z=P(b.thm[T].s[C][V],O,.18);O=Math.min(O,z)}else if(0!=E&&3==A[T][E-1]||0==E&&3==b.nsPsy.lastAttacks[T]){V=2!=E?E+1:0,z=P(b.thm[T].s[C][V],O,.18);O=Math.min(O,z)}O*=M[T][E],H[E]=O}for(E=0;E<3;E++)b.thm[T].s[C][E]=H[E]}}for(T=0;T<y;T++)b.nsPsy.lastAttacks[T]=A[T][2];!function(e,t,a){for(var n=e.internal_flags,s=0;s<n.channels_out;s++){var r=Y.NORM_TYPE;0!=t[s]?n.blocktype_old[s]==Y.SHORT_TYPE&&(r=Y.STOP_TYPE):(r=Y.SHORT_TYPE,n.blocktype_old[s]==Y.NORM_TYPE&&(n.blocktype_old[s]=Y.START_TYPE),n.blocktype_old[s]==Y.STOP_TYPE&&(n.blocktype_old[s]=Y.SHORT_TYPE)),a[s]=n.blocktype_old[s],n.blocktype_old[s]=r}}(e,k,m);for(T=0;T<y;T++){var G,Q,$,J;T>1?(G=f,Q=-2,$=Y.NORM_TYPE,m[0]!=Y.SHORT_TYPE&&m[1]!=Y.SHORT_TYPE||($=Y.SHORT_TYPE),J=_[o][T-2]):(G=c,Q=0,$=m[T],J=l[o][T]),$==Y.SHORT_TYPE?G[Q+T]=L(J,b.masking_lower):G[Q+T]=D(J,b.masking_lower),e.analysis&&(b.pinfo.pe[o][T]=G[Q+T])}return 0},this.psymodel_init=function(a){var n,r=a.internal_flags,o=!0,i=13,l=24,_=0,c=0,f=-8.25,h=-4.5,p=s(Y.CBANDS),b=s(Y.CBANDS),d=s(Y.CBANDS),w=a.out_samplerate;switch(a.experimentalZ){default:case 0:o=!0;break;case 1:o=a.VBR!=m.vbr_mtrh&&a.VBR!=m.vbr_mt;break;case 2:o=!1;break;case 3:i=8,_=-1.75,c=-.0125,f=-8.25,h=-2.25}for(r.ms_ener_ratio_old=.25,r.blocktype_old[0]=r.blocktype_old[1]=Y.NORM_TYPE,n=0;n<4;++n){for(var R=0;R<Y.CBANDS;++R)r.nb_1[n][R]=1e20,r.nb_2[n][R]=1e20,r.nb_s1[n][R]=r.nb_s2[n][R]=1;for(var M=0;M<Y.SBMAX_l;M++)r.en[n].l[M]=1e20,r.thm[n].l[M]=1e20;for(R=0;R<3;++R){for(M=0;M<Y.SBMAX_s;M++)r.en[n].s[M][R]=1e20,r.thm[n].s[M][R]=1e20;r.nsPsy.lastAttacks[n]=0}for(R=0;R<9;R++)r.nsPsy.last_en_subshort[n][R]=10}for(r.loudness_sq_save[0]=r.loudness_sq_save[1]=0,r.npart_l=ee(r.numlines_l,r.bo_l,r.bm_l,p,b,r.mld_l,r.PSY.bo_l_weight,w,Y.BLKSIZE,r.scalefac_band.l,Y.BLKSIZE/1152,Y.SBMAX_l),n=0;n<r.npart_l;n++){var A=_;p[n]>=i&&(A=c*(p[n]-i)/(l-i)+_*(l-p[n])/(l-i)),d[n]=Math.pow(10,A/10),r.numlines_l[n]>0?r.rnumlines_l[n]=1/r.numlines_l[n]:r.rnumlines_l[n]=0}r.s3_ll=te(r.s3ind,r.npart_l,p,b,d,o);var k;R=0;for(n=0;n<r.npart_l;n++){B=u.MAX_VALUE;for(var y=0;y<r.numlines_l[n];y++,R++){var T=w*R/(1e3*Y.BLKSIZE);x=this.ATHformula(1e3*T,a)-20,x=Math.pow(10,.1*x),B>(x*=r.numlines_l[n])&&(B=x)}r.ATH.cb_l[n]=B,(B=20*p[n]/10-20)>6&&(B=100),B<-15&&(B=-15),B-=8,r.minval_l[n]=Math.pow(10,B/10)*r.numlines_l[n]}for(r.npart_s=ee(r.numlines_s,r.bo_s,r.bm_s,p,b,r.mld_s,r.PSY.bo_s_weight,w,Y.BLKSIZE_s,r.scalefac_band.s,Y.BLKSIZE_s/384,Y.SBMAX_s),R=0,n=0;n<r.npart_s;n++){var B;A=f;p[n]>=i&&(A=h*(p[n]-i)/(l-i)+f*(l-p[n])/(l-i)),d[n]=Math.pow(10,A/10),B=u.MAX_VALUE;for(y=0;y<r.numlines_s[n];y++,R++){var x;T=w*R/(1e3*Y.BLKSIZE_s);x=this.ATHformula(1e3*T,a)-20,x=Math.pow(10,.1*x),B>(x*=r.numlines_s[n])&&(B=x)}r.ATH.cb_s[n]=B,B=7*p[n]/12-7,p[n]>12&&(B*=1+3.1*Math.log(1+B)),p[n]<12&&(B*=1+2.3*Math.log(1-B)),B<-15&&(B=-15),B-=8,r.minval_s[n]=Math.pow(10,B/10)*r.numlines_s[n]}r.s3_ss=te(r.s3ind_s,r.npart_s,p,b,d,o),v=Math.pow(10,9/16),g=Math.pow(10,1.5),S=Math.pow(10,1.5),e.init_fft(r),r.decay=Math.exp(-1*t/(.01*w/192)),k=3.5,2&a.exp_nspsytune&&(k=1),Math.abs(a.msfix)>0&&(k=a.msfix),a.msfix=k;for(var E=0;E<r.npart_l;E++)r.s3ind[E][1]>r.npart_l-1&&(r.s3ind[E][1]=r.npart_l-1);var P=576*r.mode_gr/w;if(r.ATH.decay=Math.pow(10,-1.2*P),r.ATH.adjust=.01,r.ATH.adjustLimit=1,-1!=a.ATHtype){var I=a.out_samplerate/Y.BLKSIZE,L=0;for(T=0,n=0;n<Y.BLKSIZE/2;++n)T+=I,r.ATH.eql_w[n]=1/Math.pow(10,this.ATHformula(T,a)/10),L+=r.ATH.eql_w[n];for(L=1/L,n=Y.BLKSIZE/2;--n>=0;)r.ATH.eql_w[n]*=L}for(E=R=0;E<r.npart_s;++E)for(n=0;n<r.numlines_s[E];++n)++R;for(E=R=0;E<r.npart_l;++E)for(n=0;n<r.numlines_l[E];++n)++R;for(R=0,n=0;n<r.npart_l;n++){T=w*(R+r.numlines_l[n]/2)/(1*Y.BLKSIZE);r.mld_cb_l[n]=ae(T),R+=r.numlines_l[n]}for(;n<Y.CBANDS;++n)r.mld_cb_l[n]=1;for(R=0,n=0;n<r.npart_s;n++){T=w*(R+r.numlines_s[n]/2)/(1*Y.BLKSIZE_s);r.mld_cb_s[n]=ae(T),R+=r.numlines_s[n]}for(;n<Y.CBANDS;++n)r.mld_cb_s[n]=1;return 0},this.ATHformula=function(e,t){var a;switch(t.ATHtype){case 0:a=ne(e,9);break;case 1:a=ne(e,-1);break;case 2:default:a=ne(e,0);break;case 3:a=ne(e,1)+6;break;case 4:a=ne(e,t.ATHcurve)}return a}}function K(){var e=this;K.V9=410,K.V8=420,K.V7=430,K.V6=440,K.V5=450,K.V4=460,K.V3=470,K.V2=480,K.V1=490,K.V0=500,K.R3MIX=1e3,K.STANDARD=1001,K.EXTREME=1002,K.INSANE=1003,K.STANDARD_FAST=1004,K.EXTREME_FAST=1005,K.MEDIUM=1006,K.MEDIUM_FAST=1007;var t,a,n,r,l;K.LAME_MAXMP3BUFFER=147456;var _,f,u,b=new U;function d(){this.mask_adjust=0,this.mask_adjust_short=0,this.bo_l_weight=s(Y.SBMAX_l),this.bo_s_weight=s(Y.SBMAX_s)}function g(){this.lowerlimit=0}function S(e,t){this.lowpass=t}this.enc=new Y,this.setModules=function(e,s,o,i,c,h,m,p,d){t=e,a=s,n=o,r=i,l=c,_=h,f=p,u=d,this.enc.setModules(a,b,r,_)};var w=4294479419;function M(e){return e>1?0:e<=0?1:Math.cos(Math.PI/2*e)}function k(e,t){switch(e){case 44100:return t.version=1,0;case 48e3:return t.version=1,1;case 32e3:return t.version=1,2;case 22050:case 11025:return t.version=0,0;case 24e3:case 12e3:return t.version=0,1;case 16e3:case 8e3:return t.version=0,2;default:return t.version=0,-1}}function y(e,t,a){a<16e3&&(t=2);for(var n=A.bitrate_table[t][1],s=2;s<=14;s++)A.bitrate_table[t][s]>0&&Math.abs(A.bitrate_table[t][s]-e)<Math.abs(n-e)&&(n=A.bitrate_table[t][s]);return n}function T(e,t,a){a<16e3&&(t=2);for(var n=0;n<=14;n++)if(A.bitrate_table[t][n]>0&&A.bitrate_table[t][n]==e)return n;return-1}function I(t,a){var n=[new S(8,2e3),new S(16,3700),new S(24,3900),new S(32,5500),new S(40,7e3),new S(48,7500),new S(56,1e4),new S(64,11e3),new S(80,13500),new S(96,15100),new S(112,15600),new S(128,17e3),new S(160,17500),new S(192,18600),new S(224,19400),new S(256,19700),new S(320,20500)],s=e.nearestBitrateFullIndex(a);t.lowerlimit=n[s].lowpass}function L(e){var t=Y.BLKSIZE+e.framesize-Y.FFTOFFSET;return t=Math.max(t,512+e.framesize-32)}function C(t,a,n,s,r,o){var i=e.enc.lame_encode_mp3_frame(t,a,n,s,r,o);return t.frameNum++,i}function D(){this.n_in=0,this.n_out=0}function H(){this.num_used=0}function O(e,t){return 0!=t?O(t,e%t):e}function V(e,t,a){var n=Math.PI*t;(e/=a)<0&&(e=0),e>1&&(e=1);var s=e-.5,r=.42-.5*Math.cos(2*e*Math.PI)+.08*Math.cos(4*e*Math.PI);return Math.abs(s)<1e-9?n/Math.PI:r*Math.sin(a*n*s)/(Math.PI*a*s)}function N(e,t,a,n,r,o,i,l,_){var c,f,h=e.internal_flags,u=0,m=e.out_samplerate/O(e.out_samplerate,e.in_samplerate);m>F.BPC&&(m=F.BPC);var p=Math.abs(h.resample_ratio-Math.floor(.5+h.resample_ratio))<1e-4?1:0,b=1/h.resample_ratio;b>1&&(b=1);var d=31;0==d%2&&--d;var v=(d+=p)+1;if(0==h.fill_buffer_resample_init){for(h.inbuf_old[0]=s(v),h.inbuf_old[1]=s(v),c=0;c<=2*m;++c)h.blackfilt[c]=s(v);for(h.itime[0]=0,h.itime[1]=0,u=0;u<=2*m;u++){var g=0,S=(u-m)/(2*m);for(c=0;c<=d;c++)g+=h.blackfilt[u][c]=V(c-S,b,d);for(c=0;c<=d;c++)h.blackfilt[u][c]/=g}h.fill_buffer_resample_init=1}var w=h.inbuf_old[_];for(f=0;f<n;f++){var R,M;if(R=f*h.resample_ratio,d+(u=0|Math.floor(R-h.itime[_]))-d/2>=i)break;S=R-h.itime[_]-(u+d%2*.5);M=0|Math.floor(2*S*m+m+.5);var A=0;for(c=0;c<=d;++c){var k=c+u-d/2;A+=(k<0?w[v+k]:r[o+k])*h.blackfilt[M][c]}t[a+f]=A}if(l.num_used=Math.min(i,d+u-d/2),h.itime[_]+=l.num_used-f*h.resample_ratio,l.num_used>=v)for(c=0;c<v;c++)w[c]=r[o+l.num_used+c-v];else{var y=v-l.num_used;for(c=0;c<y;++c)w[c]=w[c+l.num_used];for(u=0;c<v;++c,++u)w[c]=r[o+u]}return f}function j(e,t,a,n,s,r){var o=e.internal_flags;if(o.resample_ratio<.9999||o.resample_ratio>1.0001)for(var i=0;i<o.channels_out;i++){var l=new H;r.n_out=N(e,t[i],o.mf_size,e.framesize,a[i],n,s,l,i),r.n_in=l.num_used}else{r.n_out=Math.min(e.framesize,s),r.n_in=r.n_out;for(var _=0;_<r.n_out;++_)t[0][o.mf_size+_]=a[0][n+_],2==o.channels_out&&(t[1][o.mf_size+_]=a[1][n+_])}}this.lame_init=function(){var e=new x;return function(e){var t;e.class_id=w,t=e.internal_flags=new F,e.mode=p.NOT_SET,e.original=1,e.in_samplerate=44100,e.num_channels=2,e.num_samples=-1,e.bWriteVbrTag=!0,e.quality=-1,e.short_blocks=null,t.subblock_gain=-1,e.lowpassfreq=0,e.highpassfreq=0,e.lowpasswidth=-1,e.highpasswidth=-1,e.VBR=m.vbr_off,e.VBR_q=4,e.ATHcurve=-1,e.VBR_mean_bitrate_kbps=128,e.VBR_min_bitrate_kbps=0,e.VBR_max_bitrate_kbps=0,e.VBR_hard_min=0,t.VBR_min_bitrate=1,t.VBR_max_bitrate=13,e.quant_comp=-1,e.quant_comp_short=-1,e.msfix=-1,t.resample_ratio=1,t.OldValue[0]=180,t.OldValue[1]=180,t.CurrentStep[0]=4,t.CurrentStep[1]=4,t.masking_lower=1,t.nsPsy.attackthre=-1,t.nsPsy.attackthre_s=-1,e.scale=-1,e.athaa_type=-1,e.ATHtype=-1,e.athaa_loudapprox=-1,e.athaa_sensitivity=0,e.useTemporal=null,e.interChRatio=-1,t.mf_samples_to_encode=Y.ENCDELAY+Y.POSTDELAY,e.encoder_padding=0,t.mf_size=Y.ENCDELAY-Y.MDCTDELAY,e.findReplayGain=!1,e.decode_on_the_fly=!1,t.decode_on_the_fly=!1,t.findReplayGain=!1,t.findPeakSample=!1,t.RadioGain=0,t.AudiophileGain=0,t.noclipGainChange=0,t.noclipScale=-1,e.preset=0,e.write_id3tag_automatic=!0}(e),e.lame_allocated_gfp=1,e},this.nearestBitrateFullIndex=function(e){var t=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],a=0,n=0,s=0,r=0;r=t[16],s=16,n=t[16],a=16;for(var o=0;o<16;o++)if(Math.max(e,t[o+1])!=e){r=t[o+1],s=o+1,n=t[o],a=o;break}return r-e>e-n?a:s},this.lame_init_params=function(e){var s,i,S,x=e.internal_flags;if(x.Class_ID=0,null==x.ATH&&(x.ATH=new B),null==x.PSY&&(x.PSY=new d),null==x.rgdata&&(x.rgdata=new P),x.channels_in=e.num_channels,1==x.channels_in&&(e.mode=p.MONO),x.channels_out=e.mode==p.MONO?1:2,x.mode_ext=Y.MPG_MD_MS_LR,e.mode==p.MONO&&(e.force_ms=!1),e.VBR==m.vbr_off&&128!=e.VBR_mean_bitrate_kbps&&0==e.brate&&(e.brate=e.VBR_mean_bitrate_kbps),e.VBR==m.vbr_off||e.VBR==m.vbr_mtrh||e.VBR==m.vbr_mt||(e.free_format=!1),e.VBR==m.vbr_off&&0==e.brate&&R.EQ(e.compression_ratio,0)&&(e.compression_ratio=11.025),e.VBR==m.vbr_off&&e.compression_ratio>0&&(0==e.out_samplerate&&(e.out_samplerate=map2MP3Frequency(int(.97*e.in_samplerate))),e.brate=0|16*e.out_samplerate*x.channels_out/(1e3*e.compression_ratio),x.samplerate_index=k(e.out_samplerate,e),e.free_format||(e.brate=y(e.brate,e.version,e.out_samplerate))),0!=e.out_samplerate&&(e.out_samplerate<16e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,64)):e.out_samplerate<32e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,160)):(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,32),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320))),0==e.lowpassfreq){var L=16e3;switch(e.VBR){case m.vbr_off:I(C=new g,e.brate),L=C.lowerlimit;break;case m.vbr_abr:var C;I(C=new g,e.VBR_mean_bitrate_kbps),L=C.lowerlimit;break;case m.vbr_rh:var D=[19500,19e3,18600,18e3,17500,16e3,15600,14900,12500,1e4,3950];if(0<=e.VBR_q&&e.VBR_q<=9){var H=D[e.VBR_q],O=D[e.VBR_q+1],V=e.VBR_q_frac;L=linear_int(H,O,V)}else L=19500;break;default:D=[19500,19e3,18500,18e3,17500,16500,15500,14500,12500,9500,3950];if(0<=e.VBR_q&&e.VBR_q<=9){H=D[e.VBR_q],O=D[e.VBR_q+1],V=e.VBR_q_frac;L=linear_int(H,O,V)}else L=19500}e.mode!=p.MONO||e.VBR!=m.vbr_off&&e.VBR!=m.vbr_abr||(L*=1.5),e.lowpassfreq=0|L}if(0==e.out_samplerate&&(2*e.lowpassfreq>e.in_samplerate&&(e.lowpassfreq=e.in_samplerate/2),e.out_samplerate=(s=0|e.lowpassfreq,i=e.in_samplerate,S=44100,i>=48e3?S=48e3:i>=44100?S=44100:i>=32e3?S=32e3:i>=24e3?S=24e3:i>=22050?S=22050:i>=16e3?S=16e3:i>=12e3?S=12e3:i>=11025?S=11025:i>=8e3&&(S=8e3),-1==s?S:(s<=15960&&(S=44100),s<=15250&&(S=32e3),s<=11220&&(S=24e3),s<=9970&&(S=22050),s<=7230&&(S=16e3),s<=5420&&(S=12e3),s<=4510&&(S=11025),s<=3970&&(S=8e3),i<S?i>44100?48e3:i>32e3?44100:i>24e3?32e3:i>22050?24e3:i>16e3?22050:i>12e3?16e3:i>11025?12e3:i>8e3?11025:8e3:S))),e.lowpassfreq=Math.min(20500,e.lowpassfreq),e.lowpassfreq=Math.min(e.out_samplerate/2,e.lowpassfreq),e.VBR==m.vbr_off&&(e.compression_ratio=16*e.out_samplerate*x.channels_out/(1e3*e.brate)),e.VBR==m.vbr_abr&&(e.compression_ratio=16*e.out_samplerate*x.channels_out/(1e3*e.VBR_mean_bitrate_kbps)),e.bWriteVbrTag||(e.findReplayGain=!1,e.decode_on_the_fly=!1,x.findPeakSample=!1),x.findReplayGain=e.findReplayGain,x.decode_on_the_fly=e.decode_on_the_fly,x.decode_on_the_fly&&(x.findPeakSample=!0),x.findReplayGain&&t.InitGainAnalysis(x.rgdata,e.out_samplerate)==v.INIT_GAIN_ANALYSIS_ERROR)return e.internal_flags=null,-6;switch(x.decode_on_the_fly&&!e.decode_only&&(null!=x.hip&&u.hip_decode_exit(x.hip),x.hip=u.hip_decode_init()),x.mode_gr=e.out_samplerate<=24e3?1:2,e.framesize=576*x.mode_gr,e.encoder_delay=Y.ENCDELAY,x.resample_ratio=e.in_samplerate/e.out_samplerate,e.VBR){case m.vbr_mt:case m.vbr_rh:case m.vbr_mtrh:e.compression_ratio=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5][e.VBR_q];break;case m.vbr_abr:e.compression_ratio=16*e.out_samplerate*x.channels_out/(1e3*e.VBR_mean_bitrate_kbps);break;default:e.compression_ratio=16*e.out_samplerate*x.channels_out/(1e3*e.brate)}if(e.mode==p.NOT_SET&&(e.mode=p.JOINT_STEREO),e.highpassfreq>0?(x.highpass1=2*e.highpassfreq,e.highpasswidth>=0?x.highpass2=2*(e.highpassfreq+e.highpasswidth):x.highpass2=2*e.highpassfreq,x.highpass1/=e.out_samplerate,x.highpass2/=e.out_samplerate):(x.highpass1=0,x.highpass2=0),e.lowpassfreq>0?(x.lowpass2=2*e.lowpassfreq,e.lowpasswidth>=0?(x.lowpass1=2*(e.lowpassfreq-e.lowpasswidth),x.lowpass1<0&&(x.lowpass1=0)):x.lowpass1=2*e.lowpassfreq,x.lowpass1/=e.out_samplerate,x.lowpass2/=e.out_samplerate):(x.lowpass1=0,x.lowpass2=0),function(e){var t=e.internal_flags,a=32,n=-1;if(t.lowpass1>0){for(var s=999,r=0;r<=31;r++)(_=r/31)>=t.lowpass2&&(a=Math.min(a,r)),t.lowpass1<_&&_<t.lowpass2&&(s=Math.min(s,r));t.lowpass1=999==s?(a-.75)/31:(s-.75)/31,t.lowpass2=a/31}if(t.highpass2>0&&t.highpass2<.75/31*.9&&(t.highpass1=0,t.highpass2=0,c.err.println("Warning: highpass filter disabled.  highpass frequency too small\n")),t.highpass2>0){var o=-1;for(r=0;r<=31;r++)(_=r/31)<=t.highpass1&&(n=Math.max(n,r)),t.highpass1<_&&_<t.highpass2&&(o=Math.max(o,r));t.highpass1=n/31,t.highpass2=-1==o?(n+.75)/31:(o+.75)/31}for(r=0;r<32;r++){var i,l,_=r/31;i=t.highpass2>t.highpass1?M((t.highpass2-_)/(t.highpass2-t.highpass1+1e-20)):1,l=t.lowpass2>t.lowpass1?M((_-t.lowpass1)/(t.lowpass2-t.lowpass1+1e-20)):1,t.amp_filter[r]=i*l}}(e),x.samplerate_index=k(e.out_samplerate,e),x.samplerate_index<0)return e.internal_flags=null,-1;if(e.VBR==m.vbr_off){if(e.free_format)x.bitrate_index=0;else if(e.brate=y(e.brate,e.version,e.out_samplerate),x.bitrate_index=T(e.brate,e.version,e.out_samplerate),x.bitrate_index<=0)return e.internal_flags=null,-1}else x.bitrate_index=1;e.analysis&&(e.bWriteVbrTag=!1),null!=x.pinfo&&(e.bWriteVbrTag=!1),a.init_bit_stream_w(x);for(var N,j=x.samplerate_index+3*e.version+6*(e.out_samplerate<16e3?1:0),q=0;q<Y.SBMAX_l+1;q++)x.scalefac_band.l[q]=r.sfBandIndex[j].l[q];for(q=0;q<Y.PSFB21+1;q++){var W=(x.scalefac_band.l[22]-x.scalefac_band.l[21])/Y.PSFB21,X=x.scalefac_band.l[21]+q*W;x.scalefac_band.psfb21[q]=X}x.scalefac_band.psfb21[Y.PSFB21]=576;for(q=0;q<Y.SBMAX_s+1;q++)x.scalefac_band.s[q]=r.sfBandIndex[j].s[q];for(q=0;q<Y.PSFB12+1;q++){W=(x.scalefac_band.s[13]-x.scalefac_band.s[12])/Y.PSFB12,X=x.scalefac_band.s[12]+q*W;x.scalefac_band.psfb12[q]=X}for(x.scalefac_band.psfb12[Y.PSFB12]=192,1==e.version?x.sideinfo_len=1==x.channels_out?21:36:x.sideinfo_len=1==x.channels_out?13:21,e.error_protection&&(x.sideinfo_len+=2),function(e){var t=e.internal_flags;e.frameNum=0,e.write_id3tag_automatic&&f.id3tag_write_v2(e),t.bitrate_stereoMode_Hist=o([16,5]),t.bitrate_blockType_Hist=o([16,6]),t.PeakSample=0,e.bWriteVbrTag&&_.InitVbrTag(e)}(e),x.Class_ID=w,N=0;N<19;N++)x.nsPsy.pefirbuf[N]=700*x.mode_gr*x.channels_out;switch(-1==e.ATHtype&&(e.ATHtype=4),e.VBR){case m.vbr_mt:e.VBR=m.vbr_mtrh;case m.vbr_mtrh:null==e.useTemporal&&(e.useTemporal=!1),n.apply_preset(e,500-10*e.VBR_q,0),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),e.quality<5&&(e.quality=0),e.quality>5&&(e.quality=5),x.PSY.mask_adjust=e.maskingadjust,x.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?x.sfb21_extra=!1:x.sfb21_extra=e.out_samplerate>44e3,x.iteration_loop=new VBRNewIterationLoop(l);break;case m.vbr_rh:n.apply_preset(e,500-10*e.VBR_q,0),x.PSY.mask_adjust=e.maskingadjust,x.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?x.sfb21_extra=!1:x.sfb21_extra=e.out_samplerate>44e3,e.quality>6&&(e.quality=6),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),x.iteration_loop=new VBROldIterationLoop(l);break;default:var F;x.sfb21_extra=!1,e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),(F=e.VBR)==m.vbr_off&&(e.VBR_mean_bitrate_kbps=e.brate),n.apply_preset(e,e.VBR_mean_bitrate_kbps,0),e.VBR=F,x.PSY.mask_adjust=e.maskingadjust,x.PSY.mask_adjust_short=e.maskingadjust_short,F==m.vbr_off?x.iteration_loop=new E(l):x.iteration_loop=new ABRIterationLoop(l)}if(e.VBR!=m.vbr_off){if(x.VBR_min_bitrate=1,x.VBR_max_bitrate=14,e.out_samplerate<16e3&&(x.VBR_max_bitrate=8),0!=e.VBR_min_bitrate_kbps&&(e.VBR_min_bitrate_kbps=y(e.VBR_min_bitrate_kbps,e.version,e.out_samplerate),x.VBR_min_bitrate=T(e.VBR_min_bitrate_kbps,e.version,e.out_samplerate),x.VBR_min_bitrate<0))return-1;if(0!=e.VBR_max_bitrate_kbps&&(e.VBR_max_bitrate_kbps=y(e.VBR_max_bitrate_kbps,e.version,e.out_samplerate),x.VBR_max_bitrate=T(e.VBR_max_bitrate_kbps,e.version,e.out_samplerate),x.VBR_max_bitrate<0))return-1;e.VBR_min_bitrate_kbps=A.bitrate_table[e.version][x.VBR_min_bitrate],e.VBR_max_bitrate_kbps=A.bitrate_table[e.version][x.VBR_max_bitrate],e.VBR_mean_bitrate_kbps=Math.min(A.bitrate_table[e.version][x.VBR_max_bitrate],e.VBR_mean_bitrate_kbps),e.VBR_mean_bitrate_kbps=Math.max(A.bitrate_table[e.version][x.VBR_min_bitrate],e.VBR_mean_bitrate_kbps)}return e.tune&&(x.PSY.mask_adjust+=e.tune_value_a,x.PSY.mask_adjust_short+=e.tune_value_a),function(e){var t=e.internal_flags;switch(e.quality){default:case 9:t.psymodel=0,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 8:e.quality=7;case 7:t.psymodel=1,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 6:case 5:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=0,t.full_outer_loop=0;break;case 4:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 3:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 2:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 1:case 0:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=2,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0}}(e),e.athaa_type<0?x.ATH.useAdjust=3:x.ATH.useAdjust=e.athaa_type,x.ATH.aaSensitivityP=Math.pow(10,e.athaa_sensitivity/-10),null==e.short_blocks&&(e.short_blocks=h.short_block_allowed),e.short_blocks!=h.short_block_allowed||e.mode!=p.JOINT_STEREO&&e.mode!=p.STEREO||(e.short_blocks=h.short_block_coupled),e.quant_comp<0&&(e.quant_comp=1),e.quant_comp_short<0&&(e.quant_comp_short=0),e.msfix<0&&(e.msfix=0),e.exp_nspsytune=1|e.exp_nspsytune,e.internal_flags.nsPsy.attackthre<0&&(e.internal_flags.nsPsy.attackthre=U.NSATTACKTHRE),e.internal_flags.nsPsy.attackthre_s<0&&(e.internal_flags.nsPsy.attackthre_s=U.NSATTACKTHRE_S),e.scale<0&&(e.scale=1),e.ATHtype<0&&(e.ATHtype=4),e.ATHcurve<0&&(e.ATHcurve=4),e.athaa_loudapprox<0&&(e.athaa_loudapprox=2),e.interChRatio<0&&(e.interChRatio=0),null==e.useTemporal&&(e.useTemporal=!0),x.slot_lag=x.frac_SpF=0,e.VBR==m.vbr_off&&(x.slot_lag=x.frac_SpF=72e3*(e.version+1)*e.brate%e.out_samplerate|0),r.iteration_init(e),b.psymodel_init(e),0},this.lame_encode_flush=function(e,t,n,s){var r,o,l,_,c=e.internal_flags,h=i([2,1152]),u=0,m=c.mf_samples_to_encode-Y.POSTDELAY,p=L(e);if(c.mf_samples_to_encode<1)return 0;for(r=0,e.in_samplerate!=e.out_samplerate&&(m+=16*e.out_samplerate/e.in_samplerate),(l=e.framesize-m%e.framesize)<576&&(l+=e.framesize),e.encoder_padding=l,_=(m+l)/e.framesize;_>0&&u>=0;){var b=p-c.mf_size,d=e.frameNum;b*=e.in_samplerate,(b/=e.out_samplerate)>1152&&(b=1152),b<1&&(b=1),o=s-r,0==s&&(o=0),n+=u=this.lame_encode_buffer(e,h[0],h[1],b,t,n,o),r+=u,_-=d!=e.frameNum?1:0}if(c.mf_samples_to_encode=0,u<0)return u;if(o=s-r,0==s&&(o=0),a.flush_bitstream(e),(u=a.copy_buffer(c,t,n,o,1))<0)return u;if(n+=u,o=s-(r+=u),0==s&&(o=0),e.write_id3tag_automatic){if(f.id3tag_write_v1(e),(u=a.copy_buffer(c,t,n,o,0))<0)return u;r+=u}return r},this.lame_encode_buffer=function(e,n,r,o,i,l,_){var c=e.internal_flags,f=[null,null];if(c.Class_ID!=w)return-3;if(0==o)return 0;!function(e,t){(null==e.in_buffer_0||e.in_buffer_nsamples<t)&&(e.in_buffer_0=s(t),e.in_buffer_1=s(t),e.in_buffer_nsamples=t)}(c,o),f[0]=c.in_buffer_0,f[1]=c.in_buffer_1;for(var h=0;h<o;h++)f[0][h]=n[h],c.channels_in>1&&(f[1][h]=r[h]);return function(e,n,s,r,o,i,l){var _,c,f,h,u,m=e.internal_flags,p=0,b=[null,null],d=[null,null];if(m.Class_ID!=w)return-3;if(0==r)return 0;if(u=a.copy_buffer(m,o,i,l,0),u<0)return u;if(i+=u,p+=u,d[0]=n,d[1]=s,R.NEQ(e.scale,0)&&R.NEQ(e.scale,1))for(c=0;c<r;++c)d[0][c]*=e.scale,2==m.channels_out&&(d[1][c]*=e.scale);if(R.NEQ(e.scale_left,0)&&R.NEQ(e.scale_left,1))for(c=0;c<r;++c)d[0][c]*=e.scale_left;if(R.NEQ(e.scale_right,0)&&R.NEQ(e.scale_right,1))for(c=0;c<r;++c)d[1][c]*=e.scale_right;if(2==e.num_channels&&1==m.channels_out)for(c=0;c<r;++c)d[0][c]=.5*(d[0][c]+d[1][c]),d[1][c]=0;h=L(e),b[0]=m.mfbuf[0],b[1]=m.mfbuf[1];var g=0;for(;r>0;){var S=[null,null],M=0,A=0;S[0]=d[0],S[1]=d[1];var k=new D;if(j(e,b,S,g,r,k),M=k.n_in,A=k.n_out,m.findReplayGain&&!m.decode_on_the_fly&&t.AnalyzeSamples(m.rgdata,b[0],m.mf_size,b[1],m.mf_size,A,m.channels_out)==v.GAIN_ANALYSIS_ERROR)return-6;if(r-=M,g+=M,m.channels_out,m.mf_size+=A,m.mf_samples_to_encode<1&&(m.mf_samples_to_encode=Y.ENCDELAY+Y.POSTDELAY),m.mf_samples_to_encode+=A,m.mf_size>=h){var y=l-p;if(0==l&&(y=0),(_=C(e,b[0],b[1],o,i,y))<0)return _;for(i+=_,p+=_,m.mf_size-=e.framesize,m.mf_samples_to_encode-=e.framesize,f=0;f<m.channels_out;f++)for(c=0;c<m.mf_size;c++)b[f][c]=b[f][c+e.framesize]}}return p}(e,f[0],f[1],o,i,l,_)}}function Z(){this.setModules=function(e,t){}}function G(){this.setModules=function(e,t,a){}}function Q(){}function $(){this.setModules=function(e,t){}}H.SFBMAX=3*Y.SBMAX_s,Y.ENCDELAY=576,Y.POSTDELAY=1152,Y.MDCTDELAY=48,Y.FFTOFFSET=224+Y.MDCTDELAY,Y.DECDELAY=528,Y.SBLIMIT=32,Y.CBANDS=64,Y.SBPSY_l=21,Y.SBPSY_s=12,Y.SBMAX_l=22,Y.SBMAX_s=13,Y.PSFB21=6,Y.PSFB12=6,Y.BLKSIZE=1024,Y.HBLKSIZE=Y.BLKSIZE/2+1,Y.BLKSIZE_s=256,Y.HBLKSIZE_s=Y.BLKSIZE_s/2+1,Y.NORM_TYPE=0,Y.START_TYPE=1,Y.SHORT_TYPE=2,Y.STOP_TYPE=3,Y.MPG_MD_LR_LR=0,Y.MPG_MD_LR_I=1,Y.MPG_MD_MS_LR=2,Y.MPG_MD_MS_I=3,Y.fircoef=[-.1039435,-.1892065,5*-.0432472,-.155915,3898045e-23,.0467745*5,.50455,.756825,.187098*5],F.MFSIZE=3456+Y.ENCDELAY-Y.MDCTDELAY,F.MAX_HEADER_BUF=256,F.MAX_BITS_PER_CHANNEL=4095,F.MAX_BITS_PER_GRANULE=7680,F.BPC=320,H.SFBMAX=3*Y.SBMAX_s,e.Mp3Encoder=function(e,t,n){3!=arguments.length&&(console.error("WARN: Mp3Encoder(channels, samplerate, kbps) not specified"),e=1,t=44100,n=128);var s=new K,r=new Z,o=new v,i=new R,l=new g,_=new L,c=new O,f=new w,h=new b,u=new $,m=new S,M=new d,A=new G,k=new Q;s.setModules(o,i,l,_,c,f,h,u,k),i.setModules(o,k,h,f),u.setModules(i,h),l.setModules(s),c.setModules(i,m,_,M),_.setModules(M,m,s.enc.psy),m.setModules(i),M.setModules(_),f.setModules(s,i,h),r.setModules(A,k),A.setModules(h,u,l);var y=s.lame_init();y.num_channels=e,y.in_samplerate=t,y.out_samplerate=t,y.brate=n,y.mode=p.STEREO,y.quality=3,y.bWriteVbrTag=!1,y.disable_reservoir=!0,y.write_id3tag_automatic=!1,s.lame_init_params(y);var T=1152,B=0|1.25*T+7200,x=a(B);this.encodeBuffer=function(t,n){1==e&&(n=t),t.length>T&&(T=t.length,x=a(B=0|1.25*T+7200));var r=s.lame_encode_buffer(y,t,n,t.length,x,0,B);return new Int8Array(x.subarray(0,r))},this.flush=function(){var e=s.lame_encode_flush(y,x,0,B);return new Int8Array(x.subarray(0,e))}}}e(),Recorder.lamejs=e}();const F=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{j as A,q as E,B as _};
