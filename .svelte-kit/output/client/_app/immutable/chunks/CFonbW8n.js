import{p as Te,a as k,c as Me,o as je,s as b,m as R,i as $e,t as x,e as u,f as P,h as y,g as r,r as p,I as Fe,j as $,k as L,n as K,u as Ae,v as w,w as Z,x as Ie,z as F,J as ae,A as oe}from"./CLjOhJ05.js";import{A as Re}from"./DLWfJtl1.js";import{p as se}from"./BDlA38tG.js";import{L as Le}from"./8Cr1QNV0.js";const ie={a:["\xE6","e\u026A","\u0251","\u0259"],e:["e","i","\u0259"],i:["\u026A","a\u026A","i"],o:["\u0252","o\u028A","\u0254","\u0259"],u:["\u028C","u","\u028A","ju"],y:["a\u026A","\u026A","j"],b:["b"],c:["k","s"],d:["d"],f:["f"],g:["g","d\u0292"],h:["h"],j:["d\u0292"],k:["k"],l:["l"],m:["m"],n:["n"],p:["p"],q:["kw"],r:["r"],s:["s","z"],t:["t"],v:["v"],w:["w"],x:["ks"],z:["z"]},re={hello:["h","\u0259","l","o\u028A"],world:["w","\u025Cr","l","d"],apple:["\xE6","p","\u0259l"],banana:["b","\u0259","n","\xE6","n","\u0259"],cat:["k","\xE6","t"],dog:["d","\u0254","g"],book:["b","\u028A","k"],water:["w","\u0254","t","\u0259r"],house:["h","a\u028A","s"],school:["s","k","u","l"],computer:["k","\u0259m","p","ju","t","\u0259r"],phone:["f","o\u028A","n"],table:["t","e\u026A","b","\u0259l"],chair:["t\u0283","\u025Br"],window:["w","\u026A","n","d","o\u028A"],door:["d","\u0254r"],car:["k","\u0251r"],tree:["t","r","i"],flower:["f","l","a\u028A","\u0259r"],sun:["s","\u028C","n"],moon:["m","u","n"],star:["s","t","\u0251r"],good:["g","\u028A","d"],bad:["b","\xE6","d"],big:["b","\u026A","g"],small:["s","m","\u0254","l"],red:["r","\u025B","d"],blue:["b","l","u"],green:["g","r","i","n"],yellow:["j","\u025B","l","o\u028A"],black:["b","l","\xE6","k"],white:["w","a\u026A","t"]},q={b:"p",p:"p\u02B0",m:"m",f:"f",d:"t",t:"t\u02B0",n:"n",l:"l",g:"k",k:"k\u02B0",h:"x",j:"t\u0255",q:"t\u0255\u02B0",x:"\u0255",zh:"\u0288\u0282",ch:"\u0288\u0282\u02B0",sh:"\u0282",r:"\u0290",z:"ts",c:"ts\u02B0",s:"s",a:"a",o:"o",e:"\u0264",i:"i",u:"u",\u00FC:"y",ai:"a\u026A",ei:"e\u026A",ao:"a\u028A",ou:"o\u028A",an:"an",en:"\u0259n",ang:"a\u014B",eng:"\u0259\u014B",er:"\u0259r",ia:"ia",ie:"i\u025B",iao:"ia\u028A",iu:"io\u028A",ian:"ian",in:"in",iang:"ia\u014B",ing:"i\u014B",ua:"ua",uo:"uo",uai:"ua\u026A",ui:"ue\u026A",uan:"uan",un:"u\u0259n",uang:"ua\u014B",ong:"u\u014B",\u00FCe:"y\u025B",\u00FCan:"yan",\u00FCn:"yn"},le={i:{type:"vowel",height:"high",backness:"front"},\u026A:{type:"vowel",height:"high",backness:"front"},e:{type:"vowel",height:"mid",backness:"front"},\u025B:{type:"vowel",height:"mid",backness:"front"},\u00E6:{type:"vowel",height:"low",backness:"front"},\u0251:{type:"vowel",height:"low",backness:"back"},\u0254:{type:"vowel",height:"mid",backness:"back"},o:{type:"vowel",height:"mid",backness:"back"},u:{type:"vowel",height:"high",backness:"back"},\u028A:{type:"vowel",height:"high",backness:"back"},\u028C:{type:"vowel",height:"mid",backness:"central"},\u0259:{type:"vowel",height:"mid",backness:"central"},p:{type:"consonant",manner:"stop",place:"bilabial",voiced:!1},b:{type:"consonant",manner:"stop",place:"bilabial",voiced:!0},t:{type:"consonant",manner:"stop",place:"alveolar",voiced:!1},d:{type:"consonant",manner:"stop",place:"alveolar",voiced:!0},k:{type:"consonant",manner:"stop",place:"velar",voiced:!1},g:{type:"consonant",manner:"stop",place:"velar",voiced:!0},f:{type:"consonant",manner:"fricative",place:"labiodental",voiced:!1},v:{type:"consonant",manner:"fricative",place:"labiodental",voiced:!0},s:{type:"consonant",manner:"fricative",place:"alveolar",voiced:!1},z:{type:"consonant",manner:"fricative",place:"alveolar",voiced:!0},m:{type:"consonant",manner:"nasal",place:"bilabial",voiced:!0},n:{type:"consonant",manner:"nasal",place:"alveolar",voiced:!0},l:{type:"consonant",manner:"liquid",place:"alveolar",voiced:!0},r:{type:"consonant",manner:"liquid",place:"alveolar",voiced:!0},w:{type:"consonant",manner:"glide",place:"bilabial",voiced:!0},j:{type:"consonant",manner:"glide",place:"palatal",voiced:!0},h:{type:"consonant",manner:"fricative",place:"glottal",voiced:!1}};class ce{constructor(){this.cache=new Map}detectLanguage(e){const t=/[\u4e00-\u9fff]/.test(e),n=/[a-zA-Z]/.test(e);return t&&n?"mixed":t?"zh":"en"}smartPhonemeConversion(e){const t=this.detectLanguage(e);if(t==="zh")return this.chineseToPhonemes(e);if(t==="en")return this.englishToPhonemes(e);{const n=[],o=/[\u4e00-\u9fff]+/g,a=/[a-zA-Z]+/g;let i;for(;(i=o.exec(e))!==null;){const d=this.chineseToPhonemes(i[0]);n.push(...d)}for(o.lastIndex=0;(i=a.exec(e))!==null;){const d=this.englishToPhonemes(i[0]);n.push(...d)}return n}}enhancedMatch(e,t,n="en",o=.6){if(this.directTextMatch(e,t))return{matched:!0,confidence:.95,method:"direct",details:{asrResult:e,targetWord:t}};const a=this.smartPhonemeConversion(e),i=n==="zh"?this.chineseToPhonemes(t):this.englishToPhonemes(t),d=this.calculatePhonemeSimilarity(a,i),h=this.calculateContainsSimilarity(a,i),v=this.calculateFuzzyContainsSimilarity(a,i),c=Math.max(d,h,v);let m="exact";return c===h&&(m="contains"),c===v&&(m="fuzzy_contains"),{matched:c>=o,confidence:c,method:"phoneme",details:{asrResult:e,targetWord:t,asrPhonemes:a,targetPhonemes:i,similarity:c,exactSimilarity:d,containsSimilarity:h,fuzzyContainsSimilarity:v,matchMethod:m}}}directTextMatch(e,t){const n=o=>o.toLowerCase().trim().replace(/[^\w\u4e00-\u9fff]/g,"");return n(e)===n(t)}calculatePhonemeSimilarity(e,t){if(!e.length&&!t.length)return 1;if(!e.length||!t.length)return 0;const n=Array(e.length+1).fill().map(()=>Array(t.length+1).fill(0));for(let a=0;a<=e.length;a++)n[a][0]=a;for(let a=0;a<=t.length;a++)n[0][a]=a;for(let a=1;a<=e.length;a++)for(let i=1;i<=t.length;i++){const d=this.phonemeSimilarityCost(e[a-1],t[i-1]);n[a][i]=Math.min(n[a-1][i]+1,n[a][i-1]+1,n[a-1][i-1]+d)}const o=Math.max(e.length,t.length);return 1-n[e.length][t.length]/o}calculateContainsSimilarity(e,t){if(!e.length||!t.length)return 0;for(let n=0;n<=e.length-t.length;n++){let o=0;for(let a=0;a<t.length;a++)e[n+a]===t[a]&&o++;if(o===t.length)return Math.min(.95,t.length/e.length+.5)}return 0}calculateFuzzyContainsSimilarity(e,t){if(!e.length||!t.length)return 0;let n=0;for(let o=0;o<=e.length-t.length;o++){let a=0,i=0;for(let h=0;h<t.length;h++){const v=e[o+h],c=t[h];if(v===c)a++,i++;else{const m=this.phonemeSimilarityCost(v,c);m<.5&&(i+=1-m)}}const d=i/t.length;if(a>=.6*t.length){const h=Math.min(.2,t.length/e.length);n=Math.max(n,d+h)}else n=Math.max(n,d)}return Math.min(.9,n)}phonemeSimilarityCost(e,t){if(e===t)return 0;const n=le[e],o=le[t];return!n||!o?new Le(e,t).distance/Math.max(e.length,t.length):1-this.calculateFeatureSimilarity(n,o)}calculateFeatureSimilarity(e,t){let n=0,o=0;return e.type===t.type&&(n+=.4),o+=.4,e.type==="vowel"&&t.type==="vowel"?(e.height===t.height&&(n+=.3),e.backness===t.backness&&(n+=.3),o+=.6):e.type==="consonant"&&t.type==="consonant"&&(e.manner===t.manner&&(n+=.2),e.place===t.place&&(n+=.2),e.voiced===t.voiced&&(n+=.2),o+=.6),o>0?n/o:0}englishToPhonemes(e){const t=e.toLowerCase().trim();if(this.cache.has(t))return this.cache.get(t);let n=[];return n=re[t]?[...re[t]]:this.fallbackEnglishPhonemes(t),this.cache.set(t,n),n}chineseToPhonemes(e){const t=`zh_${e}`;if(this.cache.has(t))return this.cache.get(t);try{const n=se(e,{style:se.STYLE_TONE2,heteronym:!1}),o=[];for(const a of n)if(a&&a[0]){const i=this.pinyinToPhonemes(a[0]);o.push(...i)}return this.cache.set(t,o),o}catch(n){return[]}}pinyinToPhonemes(e){const t=e.replace(/[0-9]/g,""),{initial:n,final:o}=this.separateInitialFinal(t),a=[];if(n&&q[n]&&a.push(q[n]),o&&q[o])a.push(q[o]);else if(o){const i=this.decomposeFinal(o);for(const d of i)q[d]&&a.push(q[d])}return a}fallbackEnglishPhonemes(e){const t=[];for(let n=0;n<e.length;n++){const o=e[n];ie[o]?t.push(ie[o][0]):/[a-z]/.test(o)&&t.push(o)}return t}separateInitialFinal(e){const t=["zh","ch","sh","b","p","m","f","d","t","n","l","g","k","h","j","q","x","r","z","c","s","y","w"];for(const n of t)if(e.startsWith(n))return{initial:n,final:e.substring(n.length)};return{initial:"",final:e}}decomposeFinal(e){const t={iao:["i","ao"],ian:["i","an"],iang:["i","ang"],uai:["u","ai"],uan:["u","an"],uang:["u","ang"]};return t[e]?t[e]:[e]}}var qe=x('<div class="text-xs text-gray-500 mt-1 svelte-1g95opm"> </div>'),Ee=x('<div class="target-word-display mb-4 p-3 bg-blue-50 rounded-lg svelte-1g95opm"><div class="text-sm text-gray-600 mb-1 svelte-1g95opm">\u76EE\u6807\u5355\u8BCD:</div> <div class="text-lg font-semibold text-blue-800 svelte-1g95opm"> </div> <!></div>'),Ve=ae('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-1g95opm"><rect x="6" y="6" width="12" height="12" rx="2" fill="currentColor" class="svelte-1g95opm"></rect></svg> \u505C\u6B62\u5F55\u97F3',1),We=ae('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-1g95opm"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2" class="svelte-1g95opm"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1g95opm"></path><path d="M12 18V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1g95opm"></path><path d="M8 22H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-1g95opm"></path></svg> \u5F00\u59CB\u5F55\u97F3',1),Be=x('<div class="recording-status svelte-1g95opm"><div class="recording-indicator svelte-1g95opm"></div> <span class="svelte-1g95opm">\u6B63\u5728\u5F55\u97F3...</span></div>'),Ze=x('<div class="svelte-1g95opm"> </div>'),_e=x('<div class="svelte-1g95opm"> </div>'),He=x('<div class="analysis-details text-xs text-gray-600 mt-2 p-2 bg-gray-100 rounded svelte-1g95opm"><div class="mb-1 svelte-1g95opm"> </div> <!> <!></div>'),De=x('<div><div class="mb-2 svelte-1g95opm"><div class="text-sm text-gray-600 svelte-1g95opm">\u8BC6\u522B\u7ED3\u679C:</div> <div class="text-lg font-medium svelte-1g95opm"> </div></div> <div class="mb-2 svelte-1g95opm"><div> </div></div> <!></div>'),Je=x('<div class="enhanced-asr-container svelte-1g95opm"><!> <div class="recording-controls mb-4 svelte-1g95opm"><button><!></button> <!></div> <div class="hidden-asr svelte-1g95opm" style="display: none;"><!></div> <!></div>');function Ne(Q,e){Te(e,!1);let t=k(e,"targetWord",8,""),n=k(e,"language",8,"en"),o=k(e,"duration",8,30),a=k(e,"direction",8,"top");k(e,"showTextPanel",8,!0),k(e,"phonemeThreshold",8,.6),k(e,"directScoreThreshold",8,60),k(e,"phonemeScoreThreshold",8,40);let i=R(),d=R(),h=R(!1),v=R(""),c=R(null),m=R(null);const A=Me();async function _(){r(i)&&(b(c,null),b(m,null),b(v,""),await r(i).startRecording())}async function H(){r(i)&&await r(i).stopRecording()}async function U(){if(!r(i))return!1;try{return await r(i).preInitialize()}catch(s){return!1}}function he(){return r(c)?r(c).matched?`\u2713 \u5339\u914D\u6210\u529F (${(100*r(c).confidence).toFixed(1)}%)`:`\u2717 \u5339\u914D\u5931\u8D25 (${(100*r(c).confidence).toFixed(1)}%)`:""}async function X(){return r(i)&&r(i).getAudioStream?await r(i).getAudioStream():null}function de(){return r(c)?r(c).matched?"text-green-600":"text-red-600":""}function me(){if(!r(m))return"";const{method:s,asrPhonemes:l,targetPhonemes:g,similarity:z}=r(m);return s==="direct"?"\u76F4\u63A5\u6587\u672C\u5339\u914D":s==="phoneme"?`\u97F3\u7D20\u5339\u914D: [${(l==null?void 0:l.join(", "))||""}] vs [${(g==null?void 0:g.join(", "))||""}] (${(100*z).toFixed(1)}%)`:""}je(()=>{try{b(d,new ce)}catch(s){}}),$e();var D=Je(),ee=u(D),ge=s=>{var l=Ee(),g=y(u(l),2),z=u(g,!0);p(g);var B=y(g,2),O=C=>{var S=qe(),Y=u(S);p(S),$(E=>F(Y,`\u97F3\u7D20: [${E!=null?E:""}]`),[()=>r(d)?r(d).englishToPhonemes(t()).join(", "):""],L),w(C,S)};P(B,C=>{n()==="en"&&C(O)}),p(l),$(()=>F(z,t())),w(s,l)};P(ee,s=>{t()&&s(ge)});var J=y(ee,2),I=u(J);let te;var ue=u(I),pe=s=>{var l=Ve();oe(),w(s,l)},ve=s=>{var l=We();oe(),w(s,l)};P(ue,s=>{r(h)?s(pe):s(ve,!1)}),p(I);var fe=y(I,2),ye=s=>{var l=Be();w(s,l)};P(fe,s=>{r(h)&&s(ye)}),p(J);var N=y(J,2),be=u(N);Fe(Re(be,{get duration(){return o()},get direction(){return a()},showTextPanel:!1,$$events:{start:function(){b(h,!0),A("start")},stop:function(s){b(h,!1);const{content:l,status:g}=s.detail;A("stop",{content:l,status:g})},textInput:function(s){const{content:l,status:g}=s.detail;b(v,l),A("textInput",{content:l,status:g,matchResult:null,analysisDetails:null})},textCompleted:async function(s){const{content:l,status:g}=s.detail;A("textCompleted",{content:l,status:g})},error:function(s){b(h,!1),A("error",s.detail)},close:function(s){b(h,!1),A("close",s.detail)}},$$legacy:!0}),s=>b(i,s),()=>r(i)),p(N);var we=y(N,2),ke=s=>{var l=De();let g;var z=u(l),B=y(u(z),2),O=u(B,!0);p(B),p(z);var C=y(z,2),S=u(C),Y=u(S,!0);p(S),p(C);var E=y(C,2),xe=V=>{var T=He(),W=u(T),Pe=u(W);p(W);var ne=y(W,2),ze=f=>{var M=Ze(),G=u(M);p(M),$(j=>F(G,`\u8BC6\u522B\u97F3\u7D20: [${j!=null?j:""}]`),[()=>r(m).asrPhonemes.join(", ")],L),w(f,M)};P(ne,f=>{r(m).asrPhonemes&&f(ze)});var Ce=y(ne,2),Se=f=>{var M=_e(),G=u(M);p(M),$(j=>F(G,`\u76EE\u6807\u97F3\u7D20: [${j!=null?j:""}]`),[()=>r(m).targetPhonemes.join(", ")],L),w(f,M)};P(Ce,f=>{r(m).targetPhonemes&&f(Se)}),p(T),$(f=>F(Pe,`\u5206\u6790\u65B9\u6CD5: ${f!=null?f:""}`),[me],L),w(V,T)};P(E,V=>{r(m)&&r(m).method==="phoneme"&&V(xe)}),p(l),$((V,T,W)=>{g=K(l,1,"match-result-display mt-4 p-3 rounded-lg border-2 svelte-1g95opm",null,g,V),F(O,r(v)),K(S,1,`text-sm font-semibold ${T!=null?T:""}`,"svelte-1g95opm"),F(Y,W)},[()=>({"border-green-300":r(c).matched,"border-red-300":!r(c).matched,"bg-green-50":r(c).matched,"bg-red-50":!r(c).matched}),de,he],L),w(s,l)};return P(we,s=>{r(c)&&r(v)&&s(ke)}),p(D),$(s=>{te=K(I,1,"record-button svelte-1g95opm",null,te,s),I.disabled=!r(i)},[()=>({recording:r(h)})],L),Ae("click",I,function(...s){var l;(l=r(h)?H:_)==null||l.apply(this,s)}),w(Q,D),Z(e,"startRecording",_),Z(e,"stopRecording",H),Z(e,"preInitialize",U),Z(e,"getAudioStream",X),Ie({startRecording:_,stopRecording:H,preInitialize:U,getAudioStream:X})}export{Ne as E,ce as P};
