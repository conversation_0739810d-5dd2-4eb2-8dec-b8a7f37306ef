/*
object-assign
(c) <PERSON><PERSON> Sorhus
@license MIT
*/
var r,t;function e(){if(t)return r;t=1;var e=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;return r=function(){try{if(!Object.assign)return!1;var r=new String("abc");if(r[5]="de","5"===Object.getOwnPropertyNames(r)[0])return!1;for(var t={},e=0;e<10;e++)t["_"+String.fromCharCode(e)]=e;if("**********"!==Object.getOwnPropertyNames(t).map(function(r){return t[r]}).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach(function(r){n[r]=r}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(o){return!1}}()?Object.assign:function(r,t){for(var a,c,i=function(r){if(null==r)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(r)}(r),f=1;f<arguments.length;f++){for(var u in a=Object(arguments[f]))n.call(a,u)&&(i[u]=a[u]);if(e){c=e(a);for(var b=0;b<c.length;b++)o.call(a,c[b])&&(i[c[b]]=a[c[b]])}}return i},r}export{e as r};
