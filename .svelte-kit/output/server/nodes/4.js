import * as universal from '../entries/pages/enhanced-voice-test/_page.js';

export const index = 4;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/enhanced-voice-test/_page.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/enhanced-voice-test/+page.js";
export const imports = ["_app/immutable/nodes/4.CDtl3CMq.js","_app/immutable/chunks/B1xmz3ZD.js","_app/immutable/chunks/roc3oT9G.js","_app/immutable/chunks/t5uZ5HS4.js","_app/immutable/chunks/Do6EixEe.js","_app/immutable/chunks/DuTLMzPI.js","_app/immutable/chunks/BDlA38tG.js","_app/immutable/chunks/-7hijANM.js","_app/immutable/chunks/D9dOiq-I.js","_app/immutable/chunks/8Cr1QNV0.js"];
export const stylesheets = ["_app/immutable/assets/components.FFs_rFr3.css","_app/immutable/assets/4.CTXAomtk.css"];
export const fonts = [];
