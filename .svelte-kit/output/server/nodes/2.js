import * as universal from '../entries/pages/_page.js';

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_page.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/+page.js";
export const imports = ["_app/immutable/nodes/2.BMtldCj7.js","_app/immutable/chunks/CLjOhJ05.js","_app/immutable/chunks/roc3oT9G.js","_app/immutable/chunks/t5uZ5HS4.js","_app/immutable/chunks/zG-a9-JQ.js","_app/immutable/chunks/DHII-Mtc.js","_app/immutable/chunks/DLWfJtl1.js","_app/immutable/chunks/CBOc1lqT.js","_app/immutable/chunks/BIy05ocl.js","_app/immutable/chunks/-7hijANM.js"];
export const stylesheets = ["_app/immutable/assets/components.CnuqNEqZ.css"];
export const fonts = [];
