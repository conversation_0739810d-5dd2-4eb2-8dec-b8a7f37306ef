import * as universal from '../entries/pages/_page.js';

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_page.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/+page.js";
export const imports = ["_app/immutable/nodes/2.BlkP04aO.js","_app/immutable/chunks/B1xmz3ZD.js","_app/immutable/chunks/roc3oT9G.js","_app/immutable/chunks/t5uZ5HS4.js","_app/immutable/chunks/BoaXofz5.js","_app/immutable/chunks/CwJ_S9-I.js","_app/immutable/chunks/DuTLMzPI.js","_app/immutable/chunks/gyA2PHTf.js","_app/immutable/chunks/DD5itpR6.js","_app/immutable/chunks/-7hijANM.js"];
export const stylesheets = ["_app/immutable/assets/components.FFs_rFr3.css"];
export const fonts = [];
