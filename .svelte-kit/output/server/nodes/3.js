import * as universal from '../entries/pages/enhanced-speed-reading/_page.js';

export const index = 3;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/enhanced-speed-reading/_page.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/enhanced-speed-reading/+page.js";
export const imports = ["_app/immutable/nodes/3.Bx0c29h5.js","_app/immutable/chunks/CLjOhJ05.js","_app/immutable/chunks/roc3oT9G.js","_app/immutable/chunks/t5uZ5HS4.js","_app/immutable/chunks/CFonbW8n.js","_app/immutable/chunks/DLWfJtl1.js","_app/immutable/chunks/BDlA38tG.js","_app/immutable/chunks/-7hijANM.js","_app/immutable/chunks/D9dOiq-I.js","_app/immutable/chunks/8Cr1QNV0.js"];
export const stylesheets = ["_app/immutable/assets/components.CnuqNEqZ.css","_app/immutable/assets/3.C_FQCnS0.css"];
export const fonts = [];
