export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "speed-reading/_app",
	assets: new Set([]),
	mimeTypes: {},
	_: {
		client: {start:"_app/immutable/entry/start.DTQVZ6fM.js",app:"_app/immutable/entry/app.DS6vTmfX.js",imports:["_app/immutable/entry/start.DTQVZ6fM.js","_app/immutable/chunks/B1xmz3ZD.js","_app/immutable/chunks/roc3oT9G.js","_app/immutable/chunks/t5uZ5HS4.js","_app/immutable/entry/app.DS6vTmfX.js","_app/immutable/chunks/DuTLMzPI.js","_app/immutable/chunks/B1xmz3ZD.js","_app/immutable/chunks/roc3oT9G.js","_app/immutable/chunks/t5uZ5HS4.js"],stylesheets:["_app/immutable/assets/components.FFs_rFr3.css"],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 2 },
				endpoint: null
			},
			{
				id: "/enhanced-speed-reading",
				pattern: /^\/enhanced-speed-reading\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 3 },
				endpoint: null
			},
			{
				id: "/enhanced-voice-test",
				pattern: /^\/enhanced-voice-test\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 4 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
