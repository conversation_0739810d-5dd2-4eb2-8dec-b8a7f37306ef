import{e as L,s as n,a as p,h as A,b as m,c as v,u as $,p as w,f as b,i as f,j as D,k as j,l as k,m as N,n as O,o as F,q as G}from"../../chunks/vendor_svelte.js";import{E as H}from"../../chunks/ErrorBoundary.js";import{n as J,s as M,i as S,u as B,a as C,p as I,m as z}from"../../chunks/lib.js";import"../../chunks/components.js";import"clsx";function K(e,s){b();let t=s.item,a=f(s.level,0),c=f(s.isExpanded,!1),i=f(s.isSelected,!1),d=f(s.isLastItem,!1);const o=.5+1*a;e.out+=`<li class="menu-item relative"><div${p(`flex items-center pr-3 py-2 text-white hover:bg-blue-700 dark:hover:bg-gray-700 rounded-md cursor-pointer transition-colors ${v(i?"bg-blue-700 dark:bg-gray-700":"")}`)}${D(`padding-left: ${v(o)}rem;`)}>`,a>0?(e.out+="<!--[-->",e.out+=`<span class="level-indicator mr-1 svelte-1qk395a">${m(d?"\u2514\u2500":"\u251C\u2500")}</span>`):e.out+="<!--[!-->",e.out+=`<!--]--> <span class="flex-grow truncate">${m(t.name)}</span> `,t.children&&t.children.length>0?(e.out+="<!--[-->",e.out+=`<span${p(`transform transition-transform duration-200 ${v(c?"rotate-180":"")}`)}><i class="fas fa-chevron-down text-xs"></i></span>`):e.out+="<!--[!-->",e.out+="<!--]--></div> ",t.children&&c?(e.out+="<!--[-->",e.out+=`<ul class="menu-sub space-y-1 mt-1 text-sm relative svelte-1qk395a"><div${p(`vertical-line ${v(d?"shorter-line":"")}`,"svelte-1qk395a")}></div> <!---->`,j(e,s,"default",{}),e.out+="<!----></ul>"):e.out+="<!--[!-->",e.out+="<!--]--></li>",k(s,{item:t,level:a,isExpanded:c,isSelected:i,isLastItem:d}),w()}function q(e,s){var t;b();let a=f(s.items,()=>[],!0),c=f(s.level,0),i=f(s.expandedState,()=>({}),!0);function d(l){return n(t??={},"$selectedMenuItem",M)&&l&&n(t??={},"$selectedMenuItem",M).id===l.id}const o=L(a||[]);e.out+='<ul class="menu-list space-y-1 text-sm"><!--[-->';for(let l=0,g=o.length;l<g;l++){let r=o[l];r?(e.out+="<!--[-->",K(e,{item:r,level:c,isExpanded:i[r.id]||!1,isSelected:d(r),isLastItem:l===a.length-1,children:u=>{r.children&&r.children.length>0&&i[r.id]?(u.out+="<!--[-->",q(u,{items:r.children,level:c+1,expandedState:i}),u.out+="<!---->"):u.out+="<!--[!-->",u.out+="<!--]-->"},$$slots:{default:!0}})):e.out+="<!--[!-->",e.out+="<!--]-->"}e.out+="<!--]--></ul>",t&&$(t),k(s,{items:a,level:c,expandedState:i}),w()}function P(e,s){var t;let a;b();let c=f(s.title,"\u8D76\u8003\u5C0F\u72B6\u5B66\u4E60\u673A\u529F\u80FD\u6A21\u5757\u4ECB\u7ECD"),i={},d=[];a=n(t??={},"$partnerName",I)?`[${n(t??={},"$partnerName",I)}]\u529F\u80FD\u6A21\u5757\u4ECB\u7ECD`:c,d="".trim()===""?n(t??={},"$menuItems",z):function r(u,y){return u?u.map(x=>{const E=x.name&&x.name.toLowerCase().includes(y);let h=[];return x.children&&x.children.length>0&&(h=r(x.children,y)),E||h.length>0?(h.length>0&&y!==""&&(i[x.id]=!0),{...x,children:h.length>0?h:x.children}):null}).filter(Boolean):[]}(n(t??={},"$menuItems",z),"".toLowerCase());let o,l=!0;function g(r){r.out+=`<div${p(`fixed inset-y-0 left-0 w-4/5 max-w-xs transform ${v(n(t??={},"$isDrawerOpen",S)?"translate-x-0":"-translate-x-full")} transition-transform duration-300 ease-in-out z-50 flex flex-col`)}><div class="flex-1 flex flex-col bg-gradient-to-b from-blue-800 to-blue-900 dark:from-gray-800 dark:to-gray-900 shadow-lg overflow-hidden h-full"><div class="flex items-center justify-between p-4 border-b border-blue-700 dark:border-gray-700"><div class="flex items-center"><div class="text-xl font-bold text-white">${m(a)}</div></div> <button class="p-2 rounded-full text-white hover:bg-blue-700 dark:hover:bg-gray-700 transition-colors"><i class="fas fa-arrow-left"></i></button></div> <div class="px-3 py-2 border-b border-blue-700 dark:border-gray-700"><div class="relative w-full"><input type="text"${F("value","")} placeholder="\u641C\u7D22\u76EE\u5F55..." class="w-full py-1 px-3 pr-8 bg-blue-700 dark:bg-gray-700 text-white rounded-md placeholder-blue-300 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-400 dark:focus:ring-gray-400 text-sm"> `,r.out+="<!--[!-->",r.out+='<div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-300 dark:text-gray-400"><i class="fas fa-search text-xs"></i></div>',r.out+='<!--]--></div></div> <div class="flex justify-between px-3 py-2 border-b border-blue-700 dark:border-gray-700"><button class="text-xs text-white opacity-80 hover:opacity-100 transition-opacity">\u5C55\u5F00\u5168\u90E8</button> <button class="text-xs text-white opacity-80 hover:opacity-100 transition-opacity">\u6298\u53E0\u5168\u90E8</button></div> <div class="flex-1 overflow-y-auto py-4 px-3 pb-0 drawer-content svelte-1xe7niy">',q(r,{items:d,get expandedState(){return i},set expandedState(u){i=u,l=!1}}),r.out+='<!----> <div class="h-4"></div></div></div> <div class="bg-gradient-to-b from-blue-700 to-blue-900 dark:from-gray-700 dark:to-gray-900 py-2 border-t border-blue-700 dark:border-gray-700 shadow-inner w-full"><div class="flex justify-center">',n(t??={},"$user",B).uid?(r.out+="<!--[-->",r.out+=`<span${p(`px-3 py-1 text-sm ${v(n(t??={},"$isAdmin",C)?"bg-red-500":"bg-blue-500")} text-white rounded-full shadow-md flex items-center`)}><i${p(`fas ${v(n(t??={},"$isAdmin",C)?"fa-shield-alt":"fa-user")} mr-1`)}></i> ${m(n(t??={},"$user",B).nickname||"\u7528\u6237")}</span>`):(r.out+="<!--[!-->",r.out+='<button class="px-3 py-1 text-sm bg-green-500 text-white rounded-full shadow-md flex items-center hover:bg-green-600 transition-colors"><i class="fas fa-sign-in-alt mr-1"></i> \u767B\u5F55</button>'),r.out+="<!--]--></div></div></div> ",n(t??={},"$isDrawerOpen",S)?(r.out+="<!--[-->",r.out+='<div class="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"></div>'):r.out+="<!--[!-->",r.out+="<!--]-->"}do l=!0,o=N(e),g(o);while(!l);O(e,o),t&&$(t),k(s,{title:c}),w()}function Q(e,s){b(),G(e,t=>{t.title="<title>\u5C0F\u72B6\u5143\u5B66\u4E60\u673A</title>"}),H(e,{children:t=>{t.out+="<!---->",j(t,s,"default",{}),t.out+="<!---->"},$$slots:{default:!0}}),e.out+="<!----> ",P(e,{}),e.out+="<!----> ",function(t){var a;function c(o){switch(o){case"success":return"bg-green-500";case"error":return"bg-red-500";case"warning":return"bg-yellow-500";default:return"bg-blue-500"}}function i(o){switch(o){case"success":return`<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>`;case"error":return`<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>`;case"warning":return`<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>`;default:return`<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>`}}b();const d=L(n(a??={},"$notifications",J));t.out+='<div class="fixed top-4 right-4 z-50 flex flex-col gap-2 w-80"><!--[-->';for(let o=0,l=d.length;o<l;o++){let g=d[o];t.out+=`<div${p(`notification-item px-4 py-3 rounded-lg shadow-lg text-white flex items-start gap-3 ${v(c(g.type))}`,"svelte-awgio6")}><div class="notification-icon shrink-0">${A(i(g.type))}</div> <div class="notification-content grow"><div class="message">${m(g.message)}</div></div> <button class="notification-close shrink-0 text-white hover:text-gray-200" aria-label="\u5173\u95ED\u901A\u77E5"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button></div>`}t.out+="<!--]--></div>",a&&$(a),w()}(e),e.out+="<!---->",w()}export{Q as default};
