import{e as S,s as d,a as p,h as D,b,c as h,u as k,p as w,f as v,i as f,j as T,k as L,l as $,m as z,n as O,o as q,q as B}from"../../chunks/vendor_svelte.js";import{E as N}from"../../chunks/ErrorBoundary.js";import{n as G,s as M,i as U,u as C,a as j,p as P,m as R}from"../../chunks/lib.js";import"../../chunks/components.js";import"clsx";function F(t,r){v();let e=r.item,o=f(r.level,0),s=f(r.isExpanded,!1),a=f(r.isSelected,!1),l=f(r.isLastItem,!1);const i=.5+1*o;t.out+=`<li class="menu-item relative"><div${p(`flex items-center pr-3 py-2 text-white hover:bg-blue-700 dark:hover:bg-gray-700 rounded-md cursor-pointer transition-colors ${h(a?"bg-blue-700 dark:bg-gray-700":"")}`)}${T(`padding-left: ${h(i)}rem;`)}>`,o>0?(t.out+="<!--[-->",t.out+=`<span class="level-indicator mr-1 svelte-1qk395a">${b(l?"\u2514\u2500":"\u251C\u2500")}</span>`):t.out+="<!--[!-->",t.out+=`<!--]--> <span class="flex-grow truncate">${b(e.name)}</span> `,e.children&&e.children.length>0?(t.out+="<!--[-->",t.out+=`<span${p(`transform transition-transform duration-200 ${h(s?"rotate-180":"")}`)}><i class="fas fa-chevron-down text-xs"></i></span>`):t.out+="<!--[!-->",t.out+="<!--]--></div> ",e.children&&s?(t.out+="<!--[-->",t.out+=`<ul class="menu-sub space-y-1 mt-1 text-sm relative svelte-1qk395a"><div${p(`vertical-line ${h(l?"shorter-line":"")}`,"svelte-1qk395a")}></div> <!---->`,L(t,r,"default",{}),t.out+="<!----></ul>"):t.out+="<!--[!-->",t.out+="<!--]--></li>",$(r,{item:e,level:o,isExpanded:s,isSelected:a,isLastItem:l}),w()}function E(t,r){var e;v();let o=f(r.items,()=>[],!0),s=f(r.level,0),a=f(r.expandedState,()=>({}),!0);function l(c){return d(e??={},"$selectedMenuItem",M)&&c&&d(e??={},"$selectedMenuItem",M).id===c.id}const i=S(o||[]);t.out+='<ul class="menu-list space-y-1 text-sm"><!--[-->';for(let c=0,m=i.length;c<m;c++){let n=i[c];n?(t.out+="<!--[-->",F(t,{item:n,level:s,isExpanded:a[n.id]||!1,isSelected:l(n),isLastItem:c===o.length-1,children:u=>{n.children&&n.children.length>0&&a[n.id]?(u.out+="<!--[-->",E(u,{items:n.children,level:s+1,expandedState:a}),u.out+="<!---->"):u.out+="<!--[!-->",u.out+="<!--]-->"},$$slots:{default:!0}})):t.out+="<!--[!-->",t.out+="<!--]-->"}t.out+="<!--]--></ul>",e&&k(e),$(r,{items:o,level:s,expandedState:a}),w()}function H(t,r){var e;let o;v();let s=f(r.title,"\u8D76\u8003\u5C0F\u72B6\u5B66\u4E60\u673A\u529F\u80FD\u6A21\u5757\u4ECB\u7ECD"),a={},l=[];o=d(e??={},"$partnerName",P)?`[${d(e??={},"$partnerName",P)}]\u529F\u80FD\u6A21\u5757\u4ECB\u7ECD`:s,l="".trim()===""?d(e??={},"$menuItems",R):function n(u,y){return u?u.map(x=>{const A=x.name&&x.name.toLowerCase().includes(y);let g=[];return x.children&&x.children.length>0&&(g=n(x.children,y)),A||g.length>0?(g.length>0&&y!==""&&(a[x.id]=!0),{...x,children:g.length>0?g:x.children}):null}).filter(Boolean):[]}(d(e??={},"$menuItems",R),"".toLowerCase());let i,c=!0;function m(n){n.out+=`<div${p(`fixed inset-y-0 left-0 w-4/5 max-w-xs transform ${h(d(e??={},"$isDrawerOpen",U)?"translate-x-0":"-translate-x-full")} transition-transform duration-300 ease-in-out z-50 flex flex-col`)}><div class="flex-1 flex flex-col bg-gradient-to-b from-blue-800 to-blue-900 dark:from-gray-800 dark:to-gray-900 shadow-lg overflow-hidden h-full"><div class="flex items-center justify-between p-4 border-b border-blue-700 dark:border-gray-700"><div class="flex items-center"><div class="text-xl font-bold text-white">${b(o)}</div></div> <button class="p-2 rounded-full text-white hover:bg-blue-700 dark:hover:bg-gray-700 transition-colors" aria-label="\u5173\u95ED\u5BFC\u822A\u83DC\u5355"><i class="fas fa-arrow-left"></i></button></div> <div class="px-3 py-2 border-b border-blue-700 dark:border-gray-700"><div class="relative w-full"><input type="text"${q("value","")} placeholder="\u641C\u7D22\u76EE\u5F55..." class="w-full py-1 px-3 pr-8 bg-blue-700 dark:bg-gray-700 text-white rounded-md placeholder-blue-300 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-400 dark:focus:ring-gray-400 text-sm"> `,n.out+="<!--[!-->",n.out+='<div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-300 dark:text-gray-400"><i class="fas fa-search text-xs"></i></div>',n.out+='<!--]--></div></div> <div class="flex justify-between px-3 py-2 border-b border-blue-700 dark:border-gray-700"><button class="text-xs text-white opacity-80 hover:opacity-100 transition-opacity" aria-label="\u5C55\u5F00\u6240\u6709\u83DC\u5355\u9879">\u5C55\u5F00\u5168\u90E8</button> <button class="text-xs text-white opacity-80 hover:opacity-100 transition-opacity" aria-label="\u6298\u53E0\u6240\u6709\u83DC\u5355\u9879">\u6298\u53E0\u5168\u90E8</button></div> <div class="flex-1 overflow-y-auto py-4 px-3 pb-0 drawer-content svelte-1xe7niy">',E(n,{items:l,get expandedState(){return a},set expandedState(u){a=u,c=!1}}),n.out+='<!----> <div class="h-4"></div></div></div> <div class="bg-gradient-to-b from-blue-700 to-blue-900 dark:from-gray-700 dark:to-gray-900 py-2 border-t border-blue-700 dark:border-gray-700 shadow-inner w-full"><div class="flex justify-center">',d(e??={},"$user",C).uid?(n.out+="<!--[-->",n.out+=`<span${p(`px-3 py-1 text-sm ${h(d(e??={},"$isAdmin",j)?"bg-red-500":"bg-blue-500")} text-white rounded-full shadow-md flex items-center`)}><i${p(`fas ${h(d(e??={},"$isAdmin",j)?"fa-shield-alt":"fa-user")} mr-1`)}></i> ${b(d(e??={},"$user",C).nickname||"\u7528\u6237")}</span>`):(n.out+="<!--[!-->",n.out+='<button class="px-3 py-1 text-sm bg-green-500 text-white rounded-full shadow-md flex items-center hover:bg-green-600 transition-colors" aria-label="\u8DF3\u8F6C\u5230\u767B\u5F55\u9875\u9762"><i class="fas fa-sign-in-alt mr-1"></i> \u767B\u5F55</button>'),n.out+="<!--]--></div></div></div> ",d(e??={},"$isDrawerOpen",U)?(n.out+="<!--[-->",n.out+='<div class="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"></div>'):n.out+="<!--[!-->",n.out+="<!--]-->"}do c=!0,i=z(t),m(i);while(!c);O(t,i),e&&k(e),$(r,{title:s}),w()}function I(){if(typeof window>"u")return[];const t=[];window.MediaRecorder||t.push("MediaRecorder API \u4E0D\u652F\u6301\uFF0C\u8BED\u97F3\u5F55\u5236\u529F\u80FD\u53EF\u80FD\u65E0\u6CD5\u4F7F\u7528"),navigator.mediaDevices&&navigator.mediaDevices.getUserMedia||t.push("Camera/Microphone API \u4E0D\u652F\u6301\uFF0C\u6444\u50CF\u5934\u548C\u9EA6\u514B\u98CE\u529F\u80FD\u53EF\u80FD\u65E0\u6CD5\u4F7F\u7528"),window.WebSocket||t.push("WebSocket \u4E0D\u652F\u6301\uFF0C\u5B9E\u65F6\u901A\u4FE1\u529F\u80FD\u65E0\u6CD5\u4F7F\u7528"),window.Worker||t.push("Web Workers \u4E0D\u652F\u6301\uFF0C\u540E\u53F0\u5904\u7406\u529F\u80FD\u53D7\u9650");try{new Function("const test = () => {};")()}catch{t.push("\u7BAD\u5934\u51FD\u6570\u4E0D\u652F\u6301\uFF0C\u9700\u8981\u4EE3\u7801\u8F6C\u8BD1")}return t.length>0?t:[]}if(typeof window<"u"){if(!window.URL||!window.URL.prototype.searchParams){window.URLSearchParams||(window.URLSearchParams=class{constructor(r){if(this.params=new Map,r){const e=(r.startsWith("?")?r.slice(1):r).split("&");for(let o=0;o<e.length;o++){const s=e[o];if(s){const a=s.indexOf("=");let l,i;if(a>=0?(l=s.substring(0,a),i=s.substring(a+1)):(l=s,i=""),l)try{this.params.set(decodeURIComponent(l),decodeURIComponent(i||""))}catch{this.params.set(l,i||"")}}}}}get(r){return this.params.get(r)||null}set(r,e){this.params.set(r,e)}has(r){return this.params.has(r)}toString(){const r=[];return this.params.forEach((e,o)=>{r.push(encodeURIComponent(o)+"="+encodeURIComponent(e))}),r.join("&")}});const t=window.URL;window.URL=class extends t{constructor(r,e){try{super(r,e)}catch(o){if(!e||r.startsWith("http"))throw o;{const s=e.endsWith("/")?e.slice(0,-1):e;super(r.startsWith("/")?s+r:s+"/"+r)}}if(!this.searchParams){const o=this.search;this.searchParams=new URLSearchParams(o)}}}}if(Promise.allSettled||(Promise.allSettled=function(t){return Promise.all(t.map(function(r){return Promise.resolve(r).then(function(e){return{status:"fulfilled",value:e}}).catch(function(e){return{status:"rejected",reason:e}})}))}),String.prototype.replaceAll||(String.prototype.replaceAll=function(t,r){return this.split(t).join(r)}),Array.prototype.at||(Array.prototype.at=function(t){const r=this.length,e=t<0?r+t:t;return e>=0&&e<r?this[e]:void 0}),Object.fromEntries||(Object.fromEntries=function(t){const r={};for(const e of t){const o=e[0],s=e[1];r[o]=s}return r}),navigator.mediaDevices===void 0&&(navigator.mediaDevices={}),navigator.mediaDevices.getUserMedia===void 0&&(navigator.mediaDevices.getUserMedia=function(t){const r=navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return r?new Promise(function(e,o){r.call(navigator,t,e,o)}):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}),window.fetch){const t=window.fetch.bind(window);window.fetch=function(r,e){const o=(e=e||{}).timeout||3e4;if(typeof AbortController<"u"){const s=new AbortController,a=setTimeout(function(){s.abort()},o),l=Object.assign({},e,{signal:s.signal});return t(r,l).then(function(i){return clearTimeout(a),i}).catch(function(i){throw clearTimeout(a),i.name==="AbortError"?new Error("Request timeout"):i})}return Promise.race([t(r,e),new Promise(function(s,a){setTimeout(function(){a(new Error("Request timeout"))},o)})])}}if(window.WebSocket){const t=window.WebSocket;window.WebSocket=class W extends t{constructor(e,o){super(e,o);const s=setTimeout(()=>{this.readyState===W.CONNECTING&&this.close()},1e4);this.addEventListener("open",function(){clearTimeout(s)}),this.addEventListener("error",function(){clearTimeout(s)})}}}window.checkCompatibility=I,I()}function J(t,r){v(),B(t,e=>{e.title="<title>\u5C0F\u72B6\u5143\u5B66\u4E60\u673A</title>"}),function(e){v(),e.out+="<!--[!-->",e.out+="<!--]-->",w()}(t),t.out+="<!----> ",N(t,{children:e=>{e.out+="<!---->",L(e,r,"default",{}),e.out+="<!---->"},$$slots:{default:!0}}),t.out+="<!----> ",H(t,{}),t.out+="<!----> ",function(e){var o;function s(i){switch(i){case"success":return"bg-green-500";case"error":return"bg-red-500";case"warning":return"bg-yellow-500";default:return"bg-blue-500"}}function a(i){switch(i){case"success":return`<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>`;case"error":return`<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>`;case"warning":return`<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>`;default:return`<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>`}}v();const l=S(d(o??={},"$notifications",G));e.out+='<div class="fixed top-4 right-4 z-50 flex flex-col gap-2 w-80"><!--[-->';for(let i=0,c=l.length;i<c;i++){let m=l[i];e.out+=`<div${p(`notification-item px-4 py-3 rounded-lg shadow-lg text-white flex items-start gap-3 ${h(s(m.type))}`,"svelte-awgio6")}><div class="notification-icon shrink-0">${D(a(m.type))}</div> <div class="notification-content grow"><div class="message">${b(m.message)}</div></div> <button class="notification-close shrink-0 text-white hover:text-gray-200" aria-label="\u5173\u95ED\u901A\u77E5"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button></div>`}e.out+="<!--]--></div>",o&&k(o),w()}(t),t.out+="<!---->",w()}export{J as default};
