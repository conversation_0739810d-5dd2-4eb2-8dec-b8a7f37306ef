import{f as w,i as r,e as j,a as z,o as q,j as B,c as y,s as o,u as _,l as P,p as k,r as A,b as v,t as C}from"../../chunks/vendor_svelte.js";import{a as N,b as D,u as F,m as G,p as J,l as K,s as I,d as O,i as Q}from"../../chunks/lib.js";import{u as i,g as X,s as Y,d as L,l as ee}from"../../chunks/services.js";import"clsx";import"../../chunks/components.js";import{E as te}from"../../chunks/ErrorBoundary.js";import"jsqr";function se(x,m){w();let c,f=r(m.showScanner,!1),M=!1,S=0;A(()=>{(function(){try{M=!1,c&&(clearInterval(c),c=null),S=0}catch(T){console.error("\u5B89\u5168\u505C\u6B62\u89C6\u9891\u6D41\u51FA\u9519:",T)}})()}),f&&setTimeout(()=>{},100),f?(x.out+="<!--[-->",x.out+='<div class="qr-scanner-container svelte-1w2zr59"><div class="qr-scanner-header svelte-1w2zr59"><button class="qr-scanner-close svelte-1w2zr59"><i class="fas fa-arrow-left svelte-1w2zr59"></i></button> <h3 class="qr-scanner-title svelte-1w2zr59">\u626B\u63CF\u4E8C\u7EF4\u7801</h3> <div class="qr-scanner-light svelte-1w2zr59"><button class="flash-toggle svelte-1w2zr59"><i class="fas fa-bolt svelte-1w2zr59"></i></button></div></div> <div class="qr-scanner-body svelte-1w2zr59">',x.out+="<!--[-->",x.out+='<div class="qr-scanner-loading svelte-1w2zr59"><div class="spinner svelte-1w2zr59"></div> <p class="svelte-1w2zr59">\u6B63\u5728\u52A0\u8F7D\u626B\u63CF\u7EC4\u4EF6...</p> <button class="retry-button svelte-1w2zr59">\u5237\u65B0\u9875\u9762</button></div>',x.out+="<!--]--></div></div>"):x.out+="<!--[!-->",x.out+="<!--]-->",P(m,{showScanner:f}),k()}function oe(x,m){var c;w();let f=r(m.type,null),M=!1,S="\u786E\u8BA4",T="",W="warning",E="\u786E\u8BA4",Z="\u53D6\u6D88",R=!0,H={title:"\u52A0\u8F7D\u4E2D...",description:"\u6B63\u5728\u52A0\u8F7D\u6A21\u5757\u6570\u636E...",carouselItems:[]},U=new Set,V={prev:{title:"",path:"",mcode:""},next:{title:"",path:"",mcode:""}};D(),A(()=>{Y()}),f!==void 0&&F&&async function(){try{let t=null;typeof window<"u"&&(t=new URLSearchParams(window.location.search).get("partnerEName")||null);const e=await X(f,t);C(G,e.menuItems),e.partnerName&&C(J,e.partnerName);const a=function(s){const l=[];return function p(u){if(u&&u.length)for(const d of u)d&&(d.children&&d.children.length!==0?p(d.children):l.push({...d}))}(s),l}(e.menuItems||[]);C(K,a),console.log("\u63D0\u53D6\u7684\u53F6\u5B50\u8282\u70B9:",a),a.length>0&&(C(I,a[0]),console.log("\u9ED8\u8BA4\u9009\u62E9\u7B2C\u4E00\u4E2A\u53F6\u5B50\u8282\u70B9:",a[0]))}catch(t){console.error("\u52A0\u8F7D\u83DC\u5355\u6570\u636E\u5931\u8D25:",t),O("\u52A0\u8F7D\u83DC\u5355\u6570\u636E\u5931\u8D25: "+t.message,"error")}}(),o(c??={},"$selectedMenuItem",I)&&o(c??={},"$selectedMenuItem",I).mcode&&(console.log("AppLayout\u76D1\u542C\u5230selectedMenuItem\u53D8\u5316:",o(c??={},"$selectedMenuItem",I)),C(Q,!1),o(c??={},"$selectedMenuItem",I).mcode),te(x,{children:t=>{t.out+='<div class="dashboard-container svelte-3pt5hs">',t.out+="<!--[!-->",t.out+="<!--]--> ",t.out+="<!--[!-->",t.out+="<!--]--> ",o(c??={},"$difyState",L).isProcessing&&!o(c??={},"$difyState",L).streamingContent?(t.out+="<!--[-->",t.out+='<div class="ai-generating-float svelte-3pt5hs"><div class="generating-indicator svelte-3pt5hs"><div class="generating-status-text svelte-3pt5hs"><span class="svelte-3pt5hs">AI\u6B63\u5728\u751F\u6210...</span></div> <div class="generating-animation svelte-3pt5hs"><span class="dot dot1 svelte-3pt5hs"></span> <span class="dot dot2 svelte-3pt5hs"></span> <span class="dot dot3 svelte-3pt5hs"></span></div></div></div>'):o(c??={},"$difyState",L).isProcessing&&o(c??={},"$difyState",L).streamingContent?(t.out+="<!--[1-->",t.out+='<div class="ai-generating-float small svelte-3pt5hs"><div class="generating-indicator small svelte-3pt5hs"><div class="generating-status-text small svelte-3pt5hs"><span class="svelte-3pt5hs">AI\u751F\u6210\u4E2D...</span></div></div></div>'):t.out+="<!--[!-->",t.out+="<!--]--> ",function(e,a){var s;let l,p,u,d;w();let n,h=r(a.visible,!1),b=r(a.onClose,()=>{});var $;A(()=>{n&&clearTimeout(n)}),l=o(s??={},"$uploadProgress",i).status==="completed"||o(s??={},"$uploadProgress",i).status==="error",p=o(s??={},"$uploadProgress",i).status==="saving"?100:o(s??={},"$uploadProgress",i).total>0?Math.floor(o(s??={},"$uploadProgress",i).current/o(s??={},"$uploadProgress",i).total*100):0,u=($=o(s??={},"$uploadProgress",i)).status==="uploading"?`\u6B63\u5728\u4E0A\u4F20\u56FE\u7247 (${$.current}/${$.total})`:$.status==="saving"?"\u6B63\u5728\u4FDD\u5B58\u6570\u636E":$.status==="completed"?"\u4E0A\u4F20\u5B8C\u6210":$.status==="completed_with_errors"?"\u4E0A\u4F20\u90E8\u5206\u5B8C\u6210":$.status==="error"?"\u4E0A\u4F20\u51FA\u73B0\u95EE\u9898":"\u51C6\u5907\u4E2D...",d=function(g){return g.status==="completed"?g.failed>0?`\u6210\u529F: ${g.success}\u5F20\uFF0C\u5931\u8D25: ${g.failed}\u5F20`:`\u5168\u90E8 ${g.success} \u5F20\u56FE\u7247\u4E0A\u4F20\u6210\u529F`:g.status==="completed_with_errors"?g.errorMessage||`\u6210\u529F: ${g.success}\u5F20\uFF0C\u5931\u8D25: ${g.failed}\u5F20`:g.status==="error"?g.errorMessage||"\u4E0A\u4F20\u8FC7\u7A0B\u4E2D\u53D1\u751F\u9519\u8BEF":g.status==="saving"?"\u6B63\u5728\u4FDD\u5B58\u66F4\u65B0\u5185\u5BB9...":""}(o(s??={},"$uploadProgress",i)),l&&h&&(n=setTimeout(()=>{b()},3e3)),h?(e.out+="<!--[-->",e.out+=`<div class="modal-backdrop svelte-9qo7tg"><div class="modal-container svelte-9qo7tg"><div class="modal-content svelte-9qo7tg"><div class="modal-header svelte-9qo7tg"><h3 class="svelte-9qo7tg">${v(u)}</h3> `,l?(e.out+="<!--[-->",e.out+='<button class="close-button svelte-9qo7tg"><i class="fas fa-times"></i></button>'):e.out+="<!--[!-->",e.out+=`<!--]--></div> <div class="modal-body svelte-9qo7tg"><div class="progress-container svelte-9qo7tg"><div class="progress-bar svelte-9qo7tg"${B(`width: ${y(p)}%;`)}></div></div> <div class="progress-text svelte-9qo7tg">`,o(s??={},"$uploadProgress",i).status==="uploading"?(e.out+="<!--[-->",e.out+=`<p class="svelte-9qo7tg">\u6B63\u5728\u5904\u7406\u7B2C ${v(o(s??={},"$uploadProgress",i).current)} \u5F20\uFF0C\u5171 ${v(o(s??={},"$uploadProgress",i).total)} \u5F20\u56FE\u7247</p> <p class="svelte-9qo7tg">\u5DF2\u6210\u529F: ${v(o(s??={},"$uploadProgress",i).success)} \u5F20</p> `,o(s??={},"$uploadProgress",i).failed>0?(e.out+="<!--[-->",e.out+=`<p class="error-text svelte-9qo7tg">\u5931\u8D25: ${v(o(s??={},"$uploadProgress",i).failed)} \u5F20</p>`):e.out+="<!--[!-->",e.out+="<!--]-->"):o(s??={},"$uploadProgress",i).status==="saving"?(e.out+="<!--[1-->",e.out+=`<p class="svelte-9qo7tg">\u6240\u6709\u56FE\u7247\u5DF2\u4E0A\u4F20\u5904\u7406\u5B8C\u6210</p> <p class="svelte-9qo7tg">\u6210\u529F: ${v(o(s??={},"$uploadProgress",i).success)} \u5F20</p> `,o(s??={},"$uploadProgress",i).failed>0?(e.out+="<!--[-->",e.out+=`<p class="error-text svelte-9qo7tg">\u5931\u8D25: ${v(o(s??={},"$uploadProgress",i).failed)} \u5F20</p>`):e.out+="<!--[!-->",e.out+='<!--]--> <p class="saving-text svelte-9qo7tg">\u6B63\u5728\u4E0A\u4F20\u6570\u636E\u5E76\u4FDD\u5B58\u66F4\u6539...</p> <div class="saving-indicator svelte-9qo7tg"><i class="fas fa-spinner fa-spin"></i></div>'):l?(e.out+="<!--[2-->",e.out+=`<p class="result-text svelte-9qo7tg">${v(d)}</p> `,o(s??={},"$uploadProgress",i).status==="completed"&&o(s??={},"$uploadProgress",i).failed===0?(e.out+="<!--[-->",e.out+='<div class="success-icon svelte-9qo7tg"><i class="fas fa-check-circle"></i></div>'):o(s??={},"$uploadProgress",i).status==="completed_with_errors"||o(s??={},"$uploadProgress",i).status==="completed"&&o(s??={},"$uploadProgress",i).failed>0?(e.out+="<!--[1-->",e.out+='<div class="warning-icon svelte-9qo7tg"><i class="fas fa-exclamation-triangle"></i></div>'):o(s??={},"$uploadProgress",i).status==="error"?(e.out+="<!--[2-->",e.out+='<div class="error-icon svelte-9qo7tg"><i class="fas fa-times-circle"></i></div>'):e.out+="<!--[!-->",e.out+="<!--]-->"):e.out+="<!--[!-->",e.out+="<!--]--></div></div> ",l?(e.out+="<!--[-->",e.out+='<div class="modal-footer svelte-9qo7tg"><button class="btn-primary svelte-9qo7tg">\u786E\u5B9A</button></div>'):e.out+="<!--[!-->",e.out+="<!--]--></div></div></div>"):e.out+="<!--[!-->",e.out+="<!--]-->",s&&_(s),P(a,{visible:h,onClose:b}),k()}(t,{visible:M,onClose:()=>M=!1}),t.out+='<!----> <div class="flex h-full svelte-3pt5hs"><div class="flex-1 flex flex-col svelte-3pt5hs">',t.out+="<!--[-->",t.out+='<div class="menu-toggle-container svelte-3pt5hs"><button id="menu-toggle" class="menu-toggle-float svelte-3pt5hs" aria-label="\u6253\u5F00\u83DC\u5355"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-3pt5hs"><path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path></svg></button> <a href="/enhanced-voice-test" class="test-entry-float svelte-3pt5hs" aria-label="\u8BED\u97F3\u8BC6\u522B\u6D4B\u8BD5"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-3pt5hs"><path d="M12 1C14.5 1 16.5 3 16.5 5.5V11.5C16.5 14 14.5 16 12 16C9.5 16 7.5 14 7.5 11.5V5.5C7.5 3 9.5 1 12 1Z" stroke="currentColor" stroke-width="2" class="svelte-3pt5hs"></path><path d="M19 10V11.5C19 15.09 16.09 18 12.5 18H11.5C7.91 18 5 15.09 5 11.5V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path><path d="M12 18V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path><path d="M8 22H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path></svg></a> <a href="/enhanced-speed-reading" class="speed-test-entry-float svelte-3pt5hs" aria-label="\u589E\u5F3A\u79D2\u8BCD\u6D4B\u8BD5"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-3pt5hs"><path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svelte-3pt5hs"></path></svg></a></div>',t.out+="<!--]--> ",se(t,{showScanner:!1}),t.out+='<!----> <main class="main-content full-height svelte-3pt5hs">',t.out+="<!--[-->",t.out+='<div class="loading-overlay svelte-3pt5hs"><div class="loading-spinner svelte-3pt5hs"><i class="fas fa-spinner fa-spin svelte-3pt5hs"></i></div></div>',t.out+='<!--]--> <div class="fullpage-card svelte-3pt5hs"><div class="card-header svelte-3pt5hs"><div class="header-left svelte-3pt5hs">',t.out+="<!--[!-->",t.out+=`<!--]--></div> <h3 class="card-title svelte-3pt5hs">${v(H.title)}</h3> <div class="header-right svelte-3pt5hs">`,o(c??={},"$isAdmin",N),t.out+="<!--[!-->",t.out+="<!--]--></div></div> ",t.out+="<!--[!-->",t.out+='<div class="card-image fullpage-image svelte-3pt5hs">',function(e,a){var s;w();let l=r(a.items,()=>[],!0),p=r(a.loadingImages,!1),u=r(a.loadedImages,()=>new Set,!0);if(e.out+='<div class="carousel svelte-1borv3o"><div class="carousel-container svelte-1borv3o">',p)e.out+="<!--[-->",e.out+='<div class="loading-container svelte-1borv3o"><div class="text-center svelte-1borv3o"><div class="loading-text svelte-1borv3o">\u63A5\u6536\u56FE\u7247\u4E2D...</div> <div class="loading-animation svelte-1borv3o"></div></div></div>';else if(l.length>0){e.out+="<!--[1-->";const d=j(l);e.out+="<!--[-->";for(let n=0,h=d.length;n<h;n++){let b=d[n];e.out+=`<div${z(`carousel-slide ${y(n===0?"active":"")}`,"svelte-1borv3o")}><img${q("src",b.image)}${q("alt",b.alt)} class="transition-opacity duration-300 svelte-1borv3o"${B(`opacity: ${y(u.has(b.image)?"1":"0")}`)}> `,u.has(b.image)?e.out+="<!--[!-->":(e.out+="<!--[-->",e.out+='<div class="image-loading-indicator svelte-1borv3o"><div class="loading-spinner svelte-1borv3o"></div></div>'),e.out+="<!--]--></div>"}e.out+="<!--]-->"}else e.out+="<!--[!-->",o(s??={},"$isAdmin",N)?(e.out+="<!--[-->",e.out+='<div class="carousel-empty svelte-1borv3o"><button class="scan-button svelte-1borv3o"><i class="fas fa-qrcode svelte-1borv3o"></i> <span class="svelte-1borv3o">\u70B9\u51FB\u53F3\u4E0A\u89D2\u7F16\u8F91\u6DFB\u52A0\u56FE\u7247</span></button></div>'):(e.out+="<!--[!-->",e.out+='<div class="carousel-empty svelte-1borv3o"><p class="svelte-1borv3o">\u6682\u65E0\u56FE\u7247</p></div>'),e.out+="<!--]-->";if(e.out+="<!--]--></div> ",l.length>1){e.out+="<!--[-->";const d=j(l);e.out+='<div class="carousel-nav svelte-1borv3o"><div class="carousel-indicators svelte-1borv3o"><!--[-->';for(let n=0,h=d.length;n<h;n++)d[n],e.out+=`<span${z(`indicator ${y(n===0?"active":"")}`,"svelte-1borv3o")}${q("data-index",n)}></span>`;e.out+="<!--]--></div></div>"}else e.out+="<!--[!-->";e.out+="<!--]--></div>",s&&_(s),P(a,{items:l,loadingImages:p,loadedImages:u}),k()}(t,{items:H.carouselItems,loadingImages:o(c??={},"$loadingImages",ee),loadedImages:U}),t.out+=`<!----></div> <div class="card-description svelte-3pt5hs"${B("padding-bottom: 0")}><div class="text-display text-[0.9rem] svelte-3pt5hs">`;{t.out+="<!--[-->";const e=j(H.description.split(`
`));t.out+="<!--[-->";for(let a=0,s=e.length;a<s;a++){let l=e[a];l.trim()?(t.out+="<!--[-->",t.out+=`<p class="svelte-3pt5hs">${v(l)}</p>`):(t.out+="<!--[!-->",t.out+='<br class="svelte-3pt5hs">'),t.out+="<!--]-->"}t.out+="<!--]-->"}t.out+="<!--]--></div></div>",t.out+="<!--]--> ",t.out+="<!--[-->",function(e,a){w();let s=r(a.prevPage,()=>({title:"",path:"",mcode:""}),!0),l=r(a.nextPage,()=>({title:"",path:"",mcode:""}),!0),p=r(a.isLoading,!1),u=r(a.isWechat,!1);e.out+=`<div${z("page-navigation svelte-30ryhq",void 0,{"wechat-fixed-nav":u})}>`,s&&s.mcode?(e.out+="<!--[-->",e.out+=`<button class="nav-button prev-page svelte-30ryhq"${q("disabled",p,!0)}><i class="fas fa-arrow-left svelte-30ryhq"></i> <div class="nav-button-content svelte-30ryhq"><span class="nav-title svelte-30ryhq">${v(s.title)}</span></div></button>`):(e.out+="<!--[!-->",e.out+=`<button class="nav-button prev-page disabled svelte-30ryhq"${q("disabled",!0,!0)}><i class="fas fa-arrow-left svelte-30ryhq"></i> <div class="nav-button-content svelte-30ryhq"><span class="nav-title svelte-30ryhq">${v(s.title)}</span></div></button>`),e.out+="<!--]--> ",l&&l.mcode?(e.out+="<!--[-->",e.out+=`<button class="nav-button next-page svelte-30ryhq"${q("disabled",p,!0)}><div class="nav-button-content svelte-30ryhq"><span class="nav-title svelte-30ryhq">${v(l.title)}</span></div> <i class="fas fa-arrow-right svelte-30ryhq"></i></button>`):(e.out+="<!--[!-->",e.out+=`<button class="nav-button next-page disabled svelte-30ryhq"${q("disabled",!0,!0)}><div class="nav-button-content svelte-30ryhq"><span class="nav-title svelte-30ryhq">${v(l.title)}</span></div> <i class="fas fa-arrow-right svelte-30ryhq"></i></button>`),e.out+="<!--]--></div>",P(a,{prevPage:s,nextPage:l,isLoading:p,isWechat:u}),k()}(t,{prevPage:V.prev,nextPage:V.next,isLoading:!0,isWechat:!1}),t.out+="<!--]--></div></main></div></div> ",function(e,a){w();let s=r(a.visible,!1),l=r(a.title,"\u786E\u8BA4"),p=r(a.message,"\u662F\u5426\u786E\u8BA4\u6B64\u64CD\u4F5C\uFF1F"),u=r(a.confirmText,"\u786E\u8BA4"),d=r(a.cancelText,"\u53D6\u6D88"),n=r(a.type,"warning"),h=r(a.showCancelButton,!0);s?(e.out+="<!--[-->",e.out+=`<div class="dialog-backdrop svelte-f2wiz1"><div class="dialog-container svelte-f2wiz1"><div${z(`dialog-header ${y(n)}`,"svelte-f2wiz1")}><h3 class="dialog-title svelte-f2wiz1">${v(l)}</h3></div> <div class="dialog-content svelte-f2wiz1"><p class="dialog-message svelte-f2wiz1">${v(p)}</p></div> <div class="dialog-footer svelte-f2wiz1">`,h?(e.out+="<!--[-->",e.out+=`<button class="dialog-btn cancel-btn svelte-f2wiz1">${v(d)}</button>`):e.out+="<!--[!-->",e.out+=`<!--]--> <button${z(`dialog-btn confirm-btn ${y(n)} ${y(h?"":"full-width")}`,"svelte-f2wiz1")}>${v(u)}</button></div></div></div>`):e.out+="<!--[!-->",e.out+="<!--]-->",P(a,{visible:s,title:l,message:p,confirmText:u,cancelText:d,type:n,showCancelButton:h}),k()}(t,{visible:!1,title:S,message:T,confirmText:E,cancelText:Z,type:W,showCancelButton:R}),t.out+="<!----></div>"},$$slots:{default:!0}}),c&&_(c),P(m,{type:f}),k()}function ae(x,m){let c;w();let f=m.data;c=f?.type,oe(x,{type:c}),P(m,{data:f}),k()}export{ae as default};
