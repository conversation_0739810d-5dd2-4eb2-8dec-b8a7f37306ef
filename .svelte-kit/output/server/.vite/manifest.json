{".svelte-kit/generated/server/internal.js": {"file": "internal.js", "name": "internal", "src": ".svelte-kit/generated/server/internal.js", "isEntry": true, "imports": ["_vendor_svelte.js"]}, "_ErrorBoundary.js": {"file": "chunks/ErrorBoundary.js", "name": "Error<PERSON>ou<PERSON><PERSON>", "imports": ["_vendor_svelte.js", "_lib.js"]}, "_components.CnuqNEqZ.css": {"file": "_app/immutable/assets/components.CnuqNEqZ.css", "src": "_components.CnuqNEqZ.css"}, "_components.js": {"file": "chunks/components.js", "name": "components", "css": ["_app/immutable/assets/components.CnuqNEqZ.css"]}, "_lib.js": {"file": "chunks/lib.js", "name": "lib", "imports": ["_vendor_svelte.js"]}, "_services.js": {"file": "chunks/services.js", "name": "services", "imports": ["_lib.js", "_vendor_svelte.js"]}, "_vendor_esm-env.js": {"file": "chunks/vendor_esm-env.js", "name": "vendor_esm-env"}, "_vendor_svelte.js": {"file": "chunks/vendor_svelte.js", "name": "vendor_svelte", "imports": ["_vendor_esm-env.js"]}, "node_modules/@sveltejs/kit/src/runtime/components/svelte-5/error.svelte": {"file": "entries/fallbacks/error.svelte.js", "name": "entries/fallbacks/error.svelte", "src": "node_modules/@sveltejs/kit/src/runtime/components/svelte-5/error.svelte", "isEntry": true, "imports": ["_vendor_svelte.js"]}, "node_modules/@sveltejs/kit/src/runtime/server/index.js": {"file": "index.js", "name": "index", "src": "node_modules/@sveltejs/kit/src/runtime/server/index.js", "isEntry": true, "imports": ["_vendor_svelte.js"]}, "src/routes/+layout.svelte": {"file": "entries/pages/_layout.svelte.js", "name": "entries/pages/_layout.svelte", "src": "src/routes/+layout.svelte", "isEntry": true, "imports": ["_vendor_svelte.js", "_ErrorBoundary.js", "_lib.js", "_components.js"], "css": ["_app/immutable/assets/_layout.DprroFZK.css"]}, "src/routes/+page.js": {"file": "entries/pages/_page.js", "name": "entries/pages/_page", "src": "src/routes/+page.js", "isEntry": true}, "src/routes/+page.svelte": {"file": "entries/pages/_page.svelte.js", "name": "entries/pages/_page.svelte", "src": "src/routes/+page.svelte", "isEntry": true, "imports": ["_vendor_svelte.js", "_lib.js", "_services.js", "_components.js", "_ErrorBoundary.js"]}, "src/routes/enhanced-speed-reading/+page.js": {"file": "entries/pages/enhanced-speed-reading/_page.js", "name": "entries/pages/enhanced-speed-reading/_page", "src": "src/routes/enhanced-speed-reading/+page.js", "isEntry": true}, "src/routes/enhanced-speed-reading/+page.svelte": {"file": "entries/pages/enhanced-speed-reading/_page.svelte.js", "name": "entries/pages/enhanced-speed-reading/_page.svelte", "src": "src/routes/enhanced-speed-reading/+page.svelte", "isEntry": true, "imports": ["_vendor_svelte.js", "_components.js"], "css": ["_app/immutable/assets/_page.C_FQCnS0.css"]}, "src/routes/enhanced-voice-test/+page.js": {"file": "entries/pages/enhanced-voice-test/_page.js", "name": "entries/pages/enhanced-voice-test/_page", "src": "src/routes/enhanced-voice-test/+page.js", "isEntry": true}, "src/routes/enhanced-voice-test/+page.svelte": {"file": "entries/pages/enhanced-voice-test/_page.svelte.js", "name": "entries/pages/enhanced-voice-test/_page.svelte", "src": "src/routes/enhanced-voice-test/+page.svelte", "isEntry": true, "imports": ["_vendor_svelte.js", "_components.js"], "css": ["_app/immutable/assets/_page.CTXAomtk.css"]}}