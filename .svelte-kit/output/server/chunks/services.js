import{c as f}from"./lib.js";import"clsx";import"moment";import{g,w as a}from"./vendor_svelte.js";const S=f.apiBaseUrl;function C(e){const{menuTitle:n,menuList:t,partnerName:s}=e;return{menuTitle:n,partnerName:s,menuItems:t.map(r=>u(r))}}function u(e){const n={id:e.zoneCode,name:e.name,link:null,mcode:e.zoneCode,children:[]};return e.children&&e.children.length>0&&(n.children=e.children.map(t=>t.zoneCode?u(t):{id:t.id.toString(),name:t.name,link:t.link,mcode:t.mcode})),n}async function w(e=null,n){const t=await async function(s=null,r=null){try{let i="mcodes";s&&(i=`${s}`);const o=await fetch(`${S}/GKZoneModules/getAppModuleListByPartner`,{method:"POST",headers:{"Content-Type":"application/json",Cookie:"student=11188475,a7e7f2ccac18db3ccf2ce39ca20a8947,nihao"},body:JSON.stringify({cp:i,partnerEName:r||void 0})});if(!o.ok)throw new Error(`\u83B7\u53D6\u83DC\u5355\u5217\u8868\u5931\u8D25: ${o.status}`);const m=await o.json();return{menuList:[{name:"\u6240\u6709\u529F\u80FD",zoneCode:"all",zone_id:0,children:m.result.data.reduce((d,c,h)=>(d.push({id:h+1,link:c.schema,mcode:c.mcode,name:c.\u540D\u79F0}),d),[])}],partnerName:m.result.partnerName}}catch(i){throw i}}(e,n);return C(t)}const I=a(!1),T=a(!1),N=a(""),y=a(new Set),k=a([]),l=a({scanStartTime:0,hasFoundImages:!1,scanFailCount:0,isRequestPending:!1,hasSuccessfulResponse:!1,scanTimer:null,scanUUID:"",scanImageName:"",isTimedOut:!1,extendedScanTimer:null});function x(){p(),p(),I.set(!1),T.set(!1),N.set(""),y.set(new Set),k.set([]),l.set({scanStartTime:0,hasFoundImages:!1,scanFailCount:0,isRequestPending:!1,hasSuccessfulResponse:!1,isTimedOut:!1,scanUUID:"",scanImageName:"",scanTimer:null,extendedScanTimer:null,additionalTimers:[]})}function p(){const e=g(l);e.scanTimer&&clearInterval(e.scanTimer),e.extendedScanTimer&&clearTimeout(e.extendedScanTimer),e.additionalTimers&&e.additionalTimers.length>0&&e.additionalTimers.forEach(n=>{n&&clearTimeout(n)}),l.update(n=>({...n,scanTimer:null,extendedScanTimer:null,additionalTimers:[]}))}const z=a({total:0,current:0,success:0,failed:0,status:"idle",errorMessage:""}),P=a({isProcessing:!1,streamingContent:"",isComplete:!1,error:null});export{P as d,w as g,T as l,x as s,z as u};
