import"clsx";import{B as Be}from"./vendor_esm-env.js";import*as te from"devalue";import{parse as pt,serialize as Qt}from"cookie";import*as Zr from"set-cookie-parser";let es,ts,ns,Zt,en,tn,rs,ss,os,je,as,is,cs,Ge,ls,us,fs,ds,ps,hs,vs,ms,gs,Ve,ys,ws,_s,bs,$s,nn,Ne,rn,xs,ks,ko=(async()=>{let J="/speed-reading",Y="https://webtoolhost.qiaoxuesi.com/speed-reading/latest";const Ke="_app",ht={base:J,assets:Y};xs=function(e){Y=ht.assets=e};const sn=["GET","POST","PUT","PATCH","DELETE","OPTIONS","HEAD"],Ss=["GET","POST","HEAD"];function vt(e,t){const n=[];let r;e.split(",").forEach((o,a)=>{const c=/([^/ \t]+)\/([^; \t]+)[ \t]*(?:;[ \t]*q=([0-9.]+))?/.exec(o);if(c){const[,i,l,f="1"]=c;n.push({type:i,subtype:l,q:+f,i:a})}}),n.sort((o,a)=>o.q!==a.q?a.q-o.q:o.subtype==="*"!=(a.subtype==="*")?o.subtype==="*"?1:-1:o.type==="*"!=(a.type==="*")?o.type==="*"?1:-1:o.i-a.i);let s=1/0;for(const o of t){const[a,c]=o.split("/"),i=n.findIndex(l=>!(l.type!==a&&l.type!=="*"||l.subtype!==c&&l.subtype!=="*"));i!==-1&&i<s&&(r=o,s=i)}return r}function on(e){return function(t,...n){const r=t.headers.get("content-type")?.split(";",1)[0].trim()??"";return n.includes(r.toLowerCase())}(e,"application/x-www-form-urlencoded","multipart/form-data","text/plain")}let mt,an=null;function Re(e,t){try{return an=e,mt?mt.run(e,t):t()}finally{an=null}}import("node:async_hooks").then(async e=>(await e.__tla,e)).then(e=>mt=new e.AsyncLocalStorage).catch(()=>{});class Le{constructor(t,n){this.status=t,this.body=typeof n=="string"?{message:n}:n||{message:`Error: ${t}`}}toString(){return JSON.stringify(this.body)}}class le{constructor(t,n){this.status=t,this.location=n}}class ye extends Error{constructor(t,n,r){super(r),this.status=t,this.text=n}}class gt{constructor(t,n){this.status=t,this.data=n}}const cn="/__data.json",yt=".html__data.json";function wt(e){return e.endsWith(".html")?e.replace(/\.html$/,yt):e.replace(/\/$/,"")+cn}const ln="/__route.js";function un(e){return e.replace(/\/$/,"")+ln}function _t(e,t){const n=JSON.stringify(e),r=new Headers(t?.headers);return r.has("content-length")||r.set("content-length",fn.encode(n).byteLength.toString()),r.has("content-type")||r.set("content-type","application/json"),new Response(n,{...t,headers:r})}const fn=new TextEncoder;function F(e,t){const n=new Headers(t?.headers);if(!n.has("content-length")){const r=fn.encode(e);return n.set("content-length",r.byteLength.toString()),new Response(r,{...t,headers:n})}return new Response(e,{...t,headers:n})}function So(e){return e}function Oe(e){return e instanceof Le||e instanceof ye?e.status:500}let bt={},dn={};ns=function(e){},Zt=function(e){bt=e},tn=function(e){dn=e};const pn={"&":"&amp;",'"':"&quot;"},hn={"&":"&amp;","<":"&lt;"},vn="[\uD800-\uDBFF](?![\uDC00-\uDFFF])|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uDC00-\uDFFF]",Es=new RegExp(`[${Object.keys(pn).join("")}]|`+vn,"g"),js=new RegExp(`[${Object.keys(hn).join("")}]|`+vn,"g");function $t(e,t){const n=t?pn:hn;return e.replace(t?Es:js,r=>r.length===2?r:n[r]??`&#${r.charCodeAt(0)};`)}function mn(e,t){return F(`${t} method not allowed`,{status:405,headers:{allow:Rs(e).join(", ")}})}function Rs(e){const t=sn.filter(n=>n in e);return("GET"in e||"HEAD"in e)&&t.push("HEAD"),t}function Xe(e,t,n){return F(e.templates.error({status:t,message:$t(n)}),{headers:{"content-type":"text/html; charset=utf-8"},status:t})}async function gn(e,t,n){var r;const s=Oe(n=n instanceof Le?n:(r=n)instanceof Error||r&&r.name&&r.message?r:new Error(JSON.stringify(r))),o=await ne(e,t,n),a=vt(e.request.headers.get("accept")||"text/html",["application/json","text/html"]);return e.isDataRequest||a==="application/json"?_t(o,{status:s}):Xe(t,s,o.message)}async function ne(e,t,n){if(n instanceof Le)return n.body;const r=Oe(n),s=function(o){return o instanceof ye?o.text:"Internal Error"}(n);return await Re(e,()=>t.hooks.handleError({error:n,event:e,status:r,message:s}))??{message:s}}function Ye(e,t){return new Response(void 0,{status:e,headers:{location:t}})}function yn(e,t){return t.path?`Data returned from \`load\` while rendering ${e.route.id} is not serializable: ${t.message} (data${t.path})`:t.path===""?`Data returned from \`load\` while rendering ${e.route.id} is not a plain object`:t.message}function wn(e){const t=[];return e.uses&&e.uses.dependencies.size>0&&t.push(`"dependencies":${JSON.stringify(Array.from(e.uses.dependencies))}`),e.uses&&e.uses.search_params.size>0&&t.push(`"search_params":${JSON.stringify(Array.from(e.uses.search_params))}`),e.uses&&e.uses.params.size>0&&t.push(`"params":${JSON.stringify(Array.from(e.uses.params))}`),e.uses?.parent&&t.push('"parent":1'),e.uses?.route&&t.push('"route":1'),e.uses?.url&&t.push('"url":1'),`"uses":{${t.join(",")}}`}function _n(e,t){return e._.prerendered_routes.has(t)||t.at(-1)==="/"&&e._.prerendered_routes.has(t.slice(0,-1))}function bn(e){return e.filter(t=>t!=null)}function $n(e){return vt(e.request.headers.get("accept")??"*/*",["application/json","text/html"])==="application/json"&&e.request.method==="POST"}function xn(e){return e instanceof gt?new Error('Cannot "throw fail()". Use "return fail()"'):e}function kn(e){return Qe({type:"redirect",status:e.status,location:e.location})}function Qe(e,t){return _t(e,t)}function Sn(e){if(e.default&&Object.keys(e).length>1)throw new Error("When using named actions, the default action cannot be used. See the docs for more info: https://svelte.dev/docs/kit/form-actions#named-actions")}async function En(e,t){const n=new URL(e.request.url);let r="default";for(const o of n.searchParams)if(o[0].startsWith("/")){if(r=o[0].slice(1),r==="default")throw new Error('Cannot use reserved action name "default"');break}const s=t[r];if(!s)throw new ye(404,"Not Found",`No action with name '${r}' found`);if(!on(e.request))throw new ye(415,"Unsupported Media Type",`Form actions expect form-encoded data \u2014 received ${e.request.headers.get("content-type")}`);return Re(e,()=>s(e))}function jn(e,t,n){const r=Object.fromEntries(Object.entries(n).map(([s,o])=>[s,o.encode]));return Rn(e,s=>te.stringify(s,r),t)}function Rn(e,t,n){try{return t(e)}catch(r){const s=r;if(e instanceof Response)throw new Error(`Data returned from action inside ${n} is not serializable. Form actions need to return plain objects or fail(). E.g. return { success: true } or return fail(400, { message: "invalid" });`);if("path"in s){let o=`Data returned from action inside ${n} is not serializable: ${s.message}`;throw s.path!==""&&(o+=` (data.${s.path})`),new Error(o)}throw s}}const On=new URL("sveltekit-internal://");function Pn(e,t){if(t[0]==="/"&&t[1]==="/")return t;let n=new URL(e,On);return n=new URL(t,n),n.protocol===On.protocol?n.pathname+n.search+n.hash:n.href}function xt(e,t){return e==="/"||t==="ignore"?e:t==="never"?e.endsWith("/")?e.slice(0,-1):e:t!=="always"||e.endsWith("/")?e:e+"/"}function Tn(e){for(const t in e)e[t]=decodeURIComponent(e[t]);return e}function Os(e,t,n,r=!1){const s=new URL(e);Object.defineProperty(s,"searchParams",{value:new Proxy(s.searchParams,{get(a,c){if(c==="get"||c==="getAll"||c==="has")return l=>(n(l),a[c](l));t();const i=Reflect.get(a,c);return typeof i=="function"?i.bind(a):i}}),enumerable:!0,configurable:!0});const o=["href","pathname","search","toString","toJSON"];r&&o.push("hash");for(const a of o)Object.defineProperty(s,a,{get:()=>(t(),e[a]),enumerable:!0,configurable:!0});return s[Symbol.for("nodejs.util.inspect.custom")]=(a,c,i)=>i(e,c),s.searchParams[Symbol.for("nodejs.util.inspect.custom")]=(a,c,i)=>i(e.searchParams,c),r||function(a){An(a),Object.defineProperty(a,"hash",{get(){throw new Error("Cannot access event.url.hash. Consider using `page.url.hash` inside a component instead")}})}(s),s}function qn(e){An(e);for(const t of["search","searchParams"])Object.defineProperty(e,t,{get(){throw new Error(`Cannot access url.${t} on a page with prerendering enabled`)}})}function An(e){e[Symbol.for("nodejs.util.inspect.custom")]=(t,n,r)=>r(new URL(e),n)}const Un="x-sveltekit-invalidated",Cn="x-sveltekit-trailing-slash";async function kt({event:e,state:t,node:n,parent:r}){if(!n?.server)return null;let s=!0;const o={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},a=n.server.load,c=n.server.trailingSlash;if(!a)return{type:"data",data:null,uses:o,slash:c};const i=Os(e.url,()=>{s&&(o.url=!0)},u=>{s&&o.search_params.add(u)});t.prerendering&&qn(i);let l=!1;const f=await Re(e,()=>a.call(null,{...e,fetch:(u,d)=>{const h=new URL(u instanceof Request?u.url:u,e.url);return Be&&l&&o.dependencies.has(h.href),e.fetch(u,d)},depends:(...u)=>{for(const d of u){const{href:h}=new URL(d,e.url);o.dependencies.add(h)}},params:new Proxy(e.params,{get:(u,d)=>(Be&&l&&typeof d=="string"&&o.params.has(d),s&&o.params.add(d),u[d])}),parent:async()=>(Be&&l&&o.parent,s&&(o.parent=!0),r()),route:new Proxy(e.route,{get:(u,d)=>(Be&&l&&typeof d=="string"&&o.route,s&&(o.route=!0),u[d])}),url:i,untrack(u){s=!1;try{return u()}finally{s=!0}}}));return l=!0,{type:"data",data:f??null,uses:o,slash:c}}async function Nn({event:e,fetched:t,node:n,parent:r,server_data_promise:s,state:o,resolve_opts:a,csr:c}){const i=await s;return n?.universal?.load?await n.universal.load.call(null,{url:e.url,params:e.params,data:i?.data??null,route:e.route,fetch:Ps(e,o,t,c,a),setHeaders:e.setHeaders,depends:()=>{},parent:r,untrack:l=>l()})??null:i?.data??null}function Ps(e,t,n,r,s){const o=async(a,c)=>{const i=a instanceof Request&&a.body?a.clone().body:null,l=a instanceof Request&&[...a.headers].length?new Headers(a.headers):c?.headers;let f=await e.fetch(a,c);const u=new URL(a instanceof Request?a.url:a,e.url),d=u.origin===e.url.origin;let h;if(d)t.prerendering&&(h={response:f,body:null},t.prerendering.dependencies.set(u.pathname,h));else if(u.protocol==="https:"||u.protocol==="http:")if((a instanceof Request?a.mode:c?.mode??"cors")==="no-cors")f=new Response("",{status:f.status,statusText:f.statusText,headers:f.headers});else{const v=f.headers.get("access-control-allow-origin");if(!v||v!==e.url.origin&&v!=="*")throw new Error(`CORS error: ${v?"Incorrect":"No"} 'Access-Control-Allow-Origin' header is present on the requested resource`)}const m=new Proxy(f,{get(v,b,y){async function O(p,$){const g=Number(v.status);if(isNaN(g))throw new Error(`response.status is not a number. value: "${v.status}" type: ${typeof v.status}`);n.push({url:d?u.href.slice(e.url.origin.length):u.href,method:e.request.method,request_body:a instanceof Request&&i?await Ts(i):c?.body,request_headers:l,response_body:p,response:v,is_b64:$})}if(b==="arrayBuffer")return async()=>{const p=await v.arrayBuffer();return h&&(h.body=new Uint8Array(p)),p instanceof ArrayBuffer&&await O(function($){if(globalThis.Buffer)return Buffer.from($).toString("base64");const g=new Uint8Array(new Uint16Array([1]).buffer)[0]>0;return btoa(new TextDecoder(g?"utf-16le":"utf-16be").decode(new Uint16Array(new Uint8Array($))))}(p),!0),p};async function N(){const p=await v.text();return p&&typeof p!="string"||await O(p,!1),h&&(h.body=p),p}return b==="text"?N:b==="json"?async()=>JSON.parse(await N()):Reflect.get(v,b,v)}});if(r){const v=f.headers.get;f.headers.get=b=>{const y=b.toLowerCase(),O=v.call(f.headers,y);if(O&&!y.startsWith("x-sveltekit-")&&!s.filterSerializedResponseHeaders(y,O))throw new Error(`Failed to get response header "${y}" \u2014 it must be included by the \`filterSerializedResponseHeaders\` option: https://svelte.dev/docs/kit/hooks#Server-hooks-handle (at ${e.route.id})`);return O}}return m};return(a,c)=>{const i=o(a,c);return i.catch(()=>{}),i}}async function Ts(e){let t="";const n=e.getReader(),r=new TextDecoder;for(;;){const{done:s,value:o}=await n.read();if(s)break;t+=r.decode(o)}return t}var Ln=Array.isArray,qs=Array.prototype.indexOf,As=Array.from,Hn=Object.defineProperty,He=Object.getOwnPropertyDescriptor,Us=Object.prototype,Cs=Array.prototype,Ns=Object.getPrototypeOf,In=Object.isExtensible;const we=()=>{};function St(e){for(var t=0;t<e.length;t++)e[t]()}fs=function(e,t,n=!1){return e===void 0?n?t():t:e};function Ls(e){return e===this.v}function Dn(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function Hs(e){return!Dn(e,this.v)}const Wn=32,zn=64,Et=128,xe=256,jt=512,oe=1024,Ie=2048,De=4096,Rt=8192,Ot=16384,Pt=1<<21,Tt=Symbol("$state"),Is=Symbol("legacy props"),Ze={},Q=Symbol();let ue=null;function Mn(e){ue=e}function Ds(e,t=!1,n){var r=ue={p:ue,c:null,d:!1,e:null,m:!1,s:e,x:null,l:null};(function(s){const o=nt(8,null,!1);pe(o,oe),o.teardown=s})(()=>{r.d=!0})}function Pe(e){if(typeof e!="object"||e===null||Tt in e)return e;const t=Ns(e);if(t!==Us&&t!==Cs)return e;var n=new Map,r=Ln(e),s=_e(0),o=L,a=c=>{var i=L;be(o);var l=c();return be(i),l};return r&&n.set("length",_e(e.length)),new Proxy(e,{defineProperty(c,i,l){"value"in l&&l.configurable!==!1&&l.enumerable!==!1&&l.writable!==!1||function(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}();var f=n.get(i);return f===void 0?(f=a(()=>_e(l.value)),n.set(i,f)):fe(f,a(()=>Pe(l.value))),!0},deleteProperty(c,i){var l=n.get(i);if(l===void 0)i in c&&n.set(i,a(()=>_e(Q)));else{if(r&&typeof i=="string"){var f=n.get("length"),u=Number(i);Number.isInteger(u)&&u<f.v&&fe(f,u)}fe(l,Q),Fn(s)}return!0},get(c,i,l){if(i===Tt)return e;var f=n.get(i),u=i in c;if(f!==void 0||u&&!He(c,i)?.writable||(f=a(()=>_e(Pe(u?c[i]:Q))),n.set(i,f)),f!==void 0){var d=qe(f);return d===Q?void 0:d}return Reflect.get(c,i,l)},getOwnPropertyDescriptor(c,i){var l=Reflect.getOwnPropertyDescriptor(c,i);if(l&&"value"in l){var f=n.get(i);f&&(l.value=qe(f))}else if(l===void 0){var u=n.get(i),d=u?.v;if(u!==void 0&&d!==Q)return{enumerable:!0,configurable:!0,value:d,writable:!0}}return l},has(c,i){if(i===Tt)return!0;var l=n.get(i),f=l!==void 0&&l.v!==Q||Reflect.has(c,i);return(l!==void 0||G!==null&&(!f||He(c,i)?.writable))&&(l===void 0&&(l=a(()=>_e(f?Pe(c[i]):Q)),n.set(i,l)),qe(l)===Q)?!1:f},set(c,i,l,f){var u=n.get(i),d=i in c;if(r&&i==="length")for(var h=l;h<u.v;h+=1){var m=n.get(h+"");m!==void 0?fe(m,Q):h in c&&(m=a(()=>_e(Q)),n.set(h+"",m))}u===void 0?d&&!He(c,i)?.writable||(fe(u=a(()=>_e(void 0)),a(()=>Pe(l))),n.set(i,u)):(d=u.v!==Q,fe(u,a(()=>Pe(l))));var v=Reflect.getOwnPropertyDescriptor(c,i);if(v?.set&&v.set.call(f,l),!d){if(r&&typeof i=="string"){var b=n.get("length"),y=Number(i);Number.isInteger(y)&&y>=b.v&&fe(b,y+1)}Fn(s)}return!0},ownKeys(c){qe(s);var i=Reflect.ownKeys(c).filter(u=>{var d=n.get(u);return d===void 0||d.v!==Q});for(var[l,f]of n)f.v===Q||l in c||i.push(l);return i},setPrototypeOf(){(function(){throw new Error("https://svelte.dev/e/state_prototype_fixed")})()}})}function Fn(e,t=1){fe(e,e.v+t)}const We=new Map;function Jn(e,t){return{f:0,v:e,reactions:null,equals:Ls,rv:0,wv:0}}function _e(e,t){const n=Jn(e);var r;return r=n,L!==null&&L.f&Pt&&(de===null?de=[r]:de.push(r)),n}function fe(e,t,n=!1){return L!==null&&!ae&&18&L.f&&!de?.includes(e)&&function(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}(),function(r,s){if(!r.equals(s)){var o=r.v;at?We.set(r,s):We.set(r,o),r.v=s,r.wv=or(),Bn(r,Ie),G===null||(G.f&oe)===0||96&G.f||(re===null?function(a){re=a}([r]):re.push(r))}return s}(e,n?Pe(t):t)}function Bn(e,t){var n=e.reactions;if(n!==null)for(var r=n.length,s=0;s<r;s++){var o=n[s],a=o.f;(a&Ie)===0&&(pe(o,t),1280&a&&(2&a?Bn(o,De):Ht(o)))}}let ke,et=!1;function tt(e){et=e}function qt(e){if(e===null)throw Ze;return ke=e}var Gn,Vn,Kn;function At(){if(Gn===void 0){Gn=window;var e=Element.prototype,t=Node.prototype,n=Text.prototype;Vn=He(t,"firstChild").get,Kn=He(t,"nextSibling").get,In(e)&&(e.__click=void 0,e.__className=void 0,e.__attributes=null,e.__style=void 0,e.__e=void 0),In(n)&&(n.__t=void 0)}}function Ut(e){return Kn.call(e)}function Xn(e){var t=e.effects;if(t!==null){e.effects=null;for(var n=0;n<t.length;n+=1)Se(t[n])}}function Yn(e){var t=function(n){var r,s=G;Te(function(o){for(var a=o.parent;a!==null;){if(!(2&a.f))return a;a=a.parent}return null}(n));try{Xn(n),r=ir(n)}finally{Te(s)}return r}(e);pe(e,!$e&&(e.f&xe)===0||e.deps===null?oe:De),e.equals(t)||(e.v=t,e.wv=or())}function nt(e,t,n,r=!0){var s=G,o={ctx:ue,deps:null,nodes_start:null,nodes_end:null,f:e|Ie,first:null,fn:t,last:null,next:null,parent:s,prev:null,teardown:null,transitions:null,wv:0};if(n)try{Lt(o),o.f|=32768}catch(c){throw Se(o),c}else t!==null&&Ht(o);if(!(n&&o.deps===null&&o.first===null&&o.nodes_start===null&&o.teardown===null&&!(1048704&o.f))&&r&&(s!==null&&function(c,i){var l=i.last;l===null?i.last=i.first=c:(l.next=c,c.prev=l,i.last=c)}(o,s),L!==null&&2&L.f)){var a=L;(a.effects??=[]).push(o)}return o}function Ws(e){const t=nt(zn,e,!0);return(n={})=>new Promise(r=>{n.outro?function(s,o){var a=[];tr(s,a,!0),function(c,i){var l=c.length;if(l>0){var f=()=>--l||i();for(var u of c)u.out(f)}else i()}(a,()=>{Se(s),o&&o()})}(t,()=>{Se(t),r(void 0)}):(Se(t),r(void 0))})}function zs(e){return nt(4,e,!1)}function Qn(e){var t=e.teardown;if(t!==null){const n=at,r=L;rr(!0),be(null);try{t.call(null)}finally{rr(n),be(r)}}}function Zn(e,t=!1){var n=e.first;for(e.first=e.last=null;n!==null;){var r=n.next;(n.f&zn)!==0?n.parent=null:Se(n,t),n=r}}function Se(e,t=!0){var n=!1;if((t||524288&e.f)&&e.nodes_start!==null){for(var r=e.nodes_start,s=e.nodes_end;r!==null;){var o=r===s?null:Ut(r);r.remove(),r=o}n=!0}Zn(e,t&&!n),ut(e,0),pe(e,Ot);var a=e.transitions;if(a!==null)for(const i of a)i.stop();Qn(e);var c=e.parent;c!==null&&c.first!==null&&er(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=null}function er(e){var t=e.parent,n=e.prev,r=e.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),t!==null&&(t.first===e&&(t.first=r),t.last===e&&(t.last=n))}function tr(e,t,n){if((e.f&Rt)===0){if(e.f^=Rt,e.transitions!==null)for(const o of e.transitions)(o.is_global||n)&&t.push(o);for(var r=e.first;r!==null;){var s=r.next;tr(r,t,!!(65536&r.f||(r.f&Wn)!==0)&&n),r=s}}}let Ct=[],Nt=[];function nr(){var e;Ct.length>0&&(e=Ct,Ct=[],St(e)),Nt.length>0&&function(){var t=Nt;Nt=[],St(t)}()}let rt=!1,st=!1,ot=null,Ee=!1,at=!1;function rr(e){at=e}let ze=[],L=null,ae=!1;function be(e){L=e}let G=null;function Te(e){G=e}let de=null,V=null,Z=0,re=null,sr=1,it=0,$e=!1;function or(){return++sr}function ct(e){var t=e.f;if((t&Ie)!==0)return!0;if((t&De)!==0){var n=e.deps,r=(t&xe)!==0;if(n!==null){var s,o,a=(t&jt)!==0,c=r&&G!==null&&!$e,i=n.length;if(a||c){var l=e,f=l.parent;for(s=0;s<i;s++)o=n[s],!a&&o?.reactions?.includes(l)||(o.reactions??=[]).push(l);a&&(l.f^=jt),c&&f!==null&&(f.f&xe)===0&&(l.f^=xe)}for(s=0;s<i;s++)if(ct(o=n[s])&&Yn(o),o.wv>e.wv)return!0}r&&(G===null||$e)||pe(e,oe)}return!1}function lt(e,t,n,r){if(rt){if(n===null&&(rt=!1),function(s){return(s.f&Ot)===0&&(s.parent===null||(s.parent.f&Et)===0)}(t))throw e}else n!==null&&(rt=!0),function(s,o){for(var a=o;a!==null;){if((a.f&Et)!==0)try{return void a.fn(s)}catch{a.f^=Et}a=a.parent}throw rt=!1,s}(e,t)}function ar(e,t,n=!0){var r=e.reactions;if(r!==null)for(var s=0;s<r.length;s++){var o=r[s];de?.includes(e)||(2&o.f?ar(o,t,!1):t===o&&(n?pe(o,Ie):(o.f&oe)!==0&&pe(o,De),Ht(o)))}}function ir(e){var t=V,n=Z,r=re,s=L,o=$e,a=de,c=ue,i=ae,l=e.f;V=null,Z=0,re=null,$e=(l&xe)!==0&&(ae||!Ee||L===null),L=96&l?null:e,de=null,Mn(e.ctx),ae=!1,it++,e.f|=Pt;try{var f=(0,e.fn)(),u=e.deps;if(V!==null){var d;if(ut(e,Z),u!==null&&Z>0)for(u.length=Z+V.length,d=0;d<V.length;d++)u[Z+d]=V[d];else e.deps=u=V;if(!$e)for(d=Z;d<u.length;d++)(u[d].reactions??=[]).push(e)}else u!==null&&Z<u.length&&(ut(e,Z),u.length=Z);if(!(re===null||ae||u===null||6146&e.f))for(d=0;d<re.length;d++)ar(re[d],e);return s!==e&&(it++,re!==null&&(r===null?r=re:r.push(...re))),f}finally{V=t,Z=n,re=r,L=s,$e=o,de=a,Mn(c),ae=i,e.f^=Pt}}function Ms(e,t){let n=t.reactions;if(n!==null){var r=qs.call(n,e);if(r!==-1){var s=n.length-1;s===0?n=t.reactions=null:(n[r]=n[s],n.pop())}}n===null&&2&t.f&&(V===null||!V.includes(t))&&(pe(t,De),768&t.f||(t.f^=jt),Xn(t),ut(t,0))}function ut(e,t){var n=e.deps;if(n!==null)for(var r=t;r<n.length;r++)Ms(e,n[r])}function Lt(e){var t=e.f;if((t&Ot)===0){pe(e,oe);var n=G,r=ue,s=Ee;G=e,Ee=!0;try{16&t?function(a){for(var c=a.first;c!==null;){var i=c.next;(c.f&Wn)===0&&Se(c),c=i}}(e):Zn(e),Qn(e);var o=ir(e);e.teardown=typeof o=="function"?o:null,e.wv=sr,e.deps}catch(a){lt(a,e,n,r||e.ctx)}finally{Ee=s,G=n}}}function Fs(){try{(function(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")})()}catch(e){if(ot===null)throw e;lt(e,ot,null)}}function cr(){var e=Ee;try{var t=0;for(Ee=!0;ze.length>0;){t++>1e3&&Fs();var n=ze,r=n.length;ze=[];for(var s=0;s<r;s++)Js(Bs(n[s]));We.clear()}}finally{st=!1,Ee=e,ot=null}}function Js(e){var t=e.length;if(t!==0)for(var n=0;n<t;n++){var r=e[n];if(!(24576&r.f))try{ct(r)&&(Lt(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?er(r):r.fn=null))}catch(s){lt(s,r,null,r.ctx)}}}function Ht(e){st||(st=!0,queueMicrotask(cr));for(var t=ot=e;t.parent!==null;){var n=(t=t.parent).f;if(96&n){if((n&oe)===0)return;t.f^=oe}}ze.push(t)}function Bs(e){for(var t=[],n=e;n!==null;){var r=n.f,s=!!(96&r);if(!(s&&(r&oe)!==0)&&(r&Rt)===0){if(4&r)t.push(n);else if(s)n.f^=oe;else{var o=L;try{L=n,ct(n)&&Lt(n)}catch(i){lt(i,n,null,n.ctx)}finally{L=o}}var a=n.first;if(a!==null){n=a;continue}}var c=n.parent;for(n=n.next;n===null&&c!==null;)n=c.next,c=c.parent}return t}function qe(e){var t=!!(2&e.f);if(L===null||ae){if(t&&e.deps===null&&e.effects===null){var n=e,r=n.parent;r!==null&&(r.f&xe)===0&&(n.f^=xe)}}else if(!de?.includes(e)){var s=L.deps;e.rv<it&&(e.rv=it,V===null&&s!==null&&s[Z]===e?Z++:V===null?V=[e]:$e&&V.includes(e)||V.push(e))}return t&&ct(n=e)&&Yn(n),at&&We.has(e)?We.get(e):e.v}const Gs=-7169;function pe(e,t){e.f=e.f&Gs|t}const Vs=["touchstart","touchmove"];function Ks(e){return Vs.includes(e)}const Xs=new Set,lr=new Set;function ft(e){var t=this,n=t.ownerDocument,r=e.type,s=e.composedPath?.()||[],o=s[0]||e.target,a=0,c=e.__root;if(c){var i=s.indexOf(c);if(i!==-1&&(t===document||t===window))return void(e.__root=t);var l=s.indexOf(t);if(l===-1)return;i<=l&&(a=i)}if((o=s[a]||e.target)!==t){Hn(e,"currentTarget",{configurable:!0,get:()=>o||n});var f=L,u=G;be(null),Te(null);try{for(var d,h=[];o!==null;){var m=o.assignedSlot||o.parentNode||o.host||null;try{var v=o["__"+r];if(v!=null&&(!o.disabled||e.target===o))if(Ln(v)){var[b,...y]=v;b.apply(o,[e,...y])}else v.call(o,e)}catch(O){d?h.push(O):d=O}if(e.cancelBubble||m===t||m===null)break;o=m}if(d){for(let O of h)queueMicrotask(()=>{throw O});throw d}}finally{e.__root=t,delete e.currentTarget,be(f),Te(u)}}}function ur(e,t){return fr(e,t)}function Ys(e,t){At(),t.intro=t.intro??!1;const n=t.target,r=et,s=ke;try{for(var o=(a=n,Vn.call(a));o&&(o.nodeType!==8||o.data!=="[");)o=Ut(o);if(!o)throw Ze;tt(!0),qt(o),qt(Ut(ke));const c=fr(e,{...t,anchor:o});if(ke===null||ke.nodeType!==8||ke.data!=="]")throw Ze;return tt(!1),c}catch(c){if(c===Ze)return t.recover===!1&&function(){throw new Error("https://svelte.dev/e/hydration_failed")}(),At(),function(i){i.textContent=""}(n),tt(!1),ur(e,t);throw c}finally{tt(r),qt(s)}var a}const Ae=new Map;function fr(e,{target:t,anchor:n,props:r={},events:s,context:o,intro:a=!0}){At();var c=new Set,i=u=>{for(var d=0;d<u.length;d++){var h=u[d];if(!c.has(h)){c.add(h);var m=Ks(h);t.addEventListener(h,ft,{passive:m});var v=Ae.get(h);v===void 0?(document.addEventListener(h,ft,{passive:m}),Ae.set(h,1)):Ae.set(h,v+1)}}};i(As(Xs)),lr.add(i);var l=void 0,f=Ws(()=>{var u=n??t.appendChild(function(d=""){return document.createTextNode(d)}());return function(d,h=!0){nt(40,d,!0,h)}(()=>{o&&(Ds({}),ue.c=o),s&&(r.$$events=s),et&&function(d,h){var m=G;m.nodes_start===null&&(m.nodes_start=d,m.nodes_end=h)}(u,null),l=e(u,r)||{},et&&(G.nodes_end=ke),o&&function(){const d=ue;if(d!==null){const y=d.e;if(y!==null){var h=G,m=L;d.e=null;try{for(var v=0;v<y.length;v++){var b=y[v];Te(b.effect),be(b.reaction),zs(b.fn)}}finally{Te(h),be(m)}}ue=d.p,d.m=!0}}()}),()=>{for(var d of c){t.removeEventListener(d,ft);var h=Ae.get(d);--h===0?(document.removeEventListener(d,ft),Ae.delete(d)):Ae.set(d,h)}lr.delete(i),u!==n&&u.parentNode?.removeChild(u)}});return It.set(l,f),l}let It=new WeakMap;const Qs=/[&"<]/g,Zs=/[&<]/g;je=function(e,t){const n=String(e??""),r=t?Qs:Zs;r.lastIndex=0;let s="",o=0;for(;r.test(n);){const a=r.lastIndex-1,c=n[a];s+=n.substring(o,a)+(c==="&"?"&amp;":c==='"'?"&quot;":"&lt;"),o=a+1}return s+n.substring(o)};const dr={translate:new Map([[!0,"yes"],[!1,"no"]])};gs=function(e,t,n=!1){if(t==null||!t&&n)return"";const r=e in dr&&dr[e].get(t)||t;return` ${e}${n?"":`="${je(r,!0)}"`}`};const pr=[...` 	
\r\f\xA0\v\uFEFF`];class eo{#e;#t;constructor(t){var n=new Map,r=(o,a)=>{var c=function(i,l=!1){const f=Jn(i);return l||(f.equals=Hs),f}(a);return n.set(o,c),c};const s=new Proxy({...t.props||{},$$events:{}},{get:(o,a)=>qe(n.get(a)??r(a,Reflect.get(o,a))),has:(o,a)=>a===Is||(qe(n.get(a)??r(a,Reflect.get(o,a))),Reflect.has(o,a)),set:(o,a,c)=>(fe(n.get(a)??r(a,c),c),Reflect.set(o,a,c))});this.#t=(t.hydrate?Ys:ur)(t.component,{target:t.target,anchor:t.anchor,props:s,context:t.context,intro:t.intro??!1,recover:t.recover}),t?.props?.$$host&&t.sync!==!1||function(){for(nr();ze.length>0;)st=!0,cr(),nr()}(),this.#e=s.$$events;for(const o of Object.keys(this.#t))o!=="$set"&&o!=="$destroy"&&o!=="$on"&&Hn(this,o,{get(){return this.#t[o]},set(a){this.#t[o]=a},enumerable:!0});this.#t.$set=o=>{Object.assign(s,o)},this.#t.$destroy=()=>{(function(o,a){const c=It.get(o);c?(It.delete(o),c(a)):Promise.resolve()})(this.#t)}}$set(t){this.#t.$set(t)}$on(t,n){this.#e[t]=this.#e[t]||[];const r=(...s)=>n.call(this,...s);return this.#e[t].push(r),()=>{this.#e[t]=this.#e[t].filter(s=>s!==r)}}$destroy(){this.#t.$destroy()}}function Dt(e,t,n){if(e==null)return t(void 0),n&&n(void 0),we;const r=function(s){var o=ae;try{return ae=!0,s()}finally{ae=o}}(()=>e.subscribe(t,n));return r.unsubscribe?()=>r.unsubscribe():r}const Ue=[];function hr(e,t){return{subscribe:Ne(e,t).subscribe}}Ne=function(e,t=we){let n=null;const r=new Set;function s(a){if(Dn(e,a)&&(e=a,n)){const c=!Ue.length;for(const i of r)i[1](),Ue.push(i,e);if(c){for(let i=0;i<Ue.length;i+=2)Ue[i][0](Ue[i+1]);Ue.length=0}}}function o(a){s(a(e))}return{set:s,update:o,subscribe:function(a,c=we){const i=[a,c];return r.add(i),r.size===1&&(n=t(s,o)||we),a(e),()=>{r.delete(i),r.size===0&&n&&(n(),n=null)}}}},is=function(e,t,n){const r=!Array.isArray(e),s=r?[e]:e;if(!s.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=t.length<2;return hr(n,(a,c)=>{let i=!1;const l=[];let f=0,u=we;const d=()=>{if(f)return;u();const m=t(r?l[0]:l,a,c);o?a(m):u=typeof m=="function"?m:we},h=s.map((m,v)=>Dt(m,b=>{l[v]=b,f&=~(1<<v),i&&d()},()=>{f|=1<<v}));return i=!0,d(),function(){St(h),u(),i=!1}})},ls=function(e){let t;return Dt(e,n=>t=n)(),t};function vr(...e){let t=5381;for(const n of e)if(typeof n=="string"){let r=n.length;for(;r;)t=33*t^n.charCodeAt(--r)}else{if(!ArrayBuffer.isView(n))throw new TypeError("value must be a string or TypedArray");{const r=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);let s=r.length;for(;s;)t=33*t^r[--s]}}return(t>>>0).toString(36)}const mr={"<":"\\u003C","\u2028":"\\u2028","\u2029":"\\u2029"},to=new RegExp(`[${Object.keys(mr).join("")}]`,"g"),ee=JSON.stringify,no=new TextEncoder;function gr(e){Wt[0]||function(){function s(a){return 4294967296*(a-Math.floor(a))}let o=2;for(let a=0;a<64;o++){let c=!0;for(let i=2;i*i<=o;i++)if(o%i===0){c=!1;break}c&&(a<8&&(yr[a]=s(o**.5)),Wt[a]=s(o**(1/3)),a++)}}();const t=yr.slice(0),n=function(s){const o=no.encode(s),a=8*o.length,c=512*Math.ceil((a+65)/512),i=new Uint8Array(c/8);i.set(o),i[o.length]=128,wr(i);const l=new Uint32Array(i.buffer);return l[l.length-2]=Math.floor(a/4294967296),l[l.length-1]=a,l}(e);for(let s=0;s<n.length;s+=16){const o=n.subarray(s,s+16);let a,c,i,l=t[0],f=t[1],u=t[2],d=t[3],h=t[4],m=t[5],v=t[6],b=t[7];for(let y=0;y<64;y++)y<16?a=o[y]:(c=o[y+1&15],i=o[y+14&15],a=o[15&y]=(c>>>7^c>>>18^c>>>3^c<<25^c<<14)+(i>>>17^i>>>19^i>>>10^i<<15^i<<13)+o[15&y]+o[y+9&15]|0),a=a+b+(h>>>6^h>>>11^h>>>25^h<<26^h<<21^h<<7)+(v^h&(m^v))+Wt[y],b=v,v=m,m=h,h=d+a|0,d=u,u=f,f=l,l=a+(f&u^d&(f^u))+(f>>>2^f>>>13^f>>>22^f<<30^f<<19^f<<10)|0;t[0]=t[0]+l|0,t[1]=t[1]+f|0,t[2]=t[2]+u|0,t[3]=t[3]+d|0,t[4]=t[4]+h|0,t[5]=t[5]+m|0,t[6]=t[6]+v|0,t[7]=t[7]+b|0}const r=new Uint8Array(t.buffer);return wr(r),_r(r)}const yr=new Uint32Array(8),Wt=new Uint32Array(64);function wr(e){for(let t=0;t<e.length;t+=4){const n=e[t+0],r=e[t+1],s=e[t+2],o=e[t+3];e[t+0]=o,e[t+1]=s,e[t+2]=r,e[t+3]=n}}const he="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");function _r(e){const t=e.length;let n,r="";for(n=2;n<t;n+=3)r+=he[e[n-2]>>2],r+=he[(3&e[n-2])<<4|e[n-1]>>4],r+=he[(15&e[n-1])<<2|e[n]>>6],r+=he[63&e[n]];return n===t+1&&(r+=he[e[n-2]>>2],r+=he[(3&e[n-2])<<4],r+="=="),n===t&&(r+=he[e[n-2]>>2],r+=he[(3&e[n-2])<<4|e[n-1]>>4],r+=he[(15&e[n-1])<<2],r+="="),r}const br=new Uint8Array(16),ro=new Set(["self","unsafe-eval","unsafe-hashes","unsafe-inline","none","strict-dynamic","report-sample","wasm-unsafe-eval","script"]),so=/^(nonce|sha\d\d\d)-/;class $r{#e;#t;#c;#l;#u;#f;#d;#p;#r;#s;#o;#a;#i;#n;#h;constructor(t,n,r){this.#e=t,this.#r=n;const s=this.#r;this.#s=[],this.#o=[],this.#a=[],this.#i=[],this.#n=[];const o=s["script-src"]||s["default-src"],a=s["script-src-elem"],c=s["style-src"]||s["default-src"],i=s["style-src-attr"],l=s["style-src-elem"],f=u=>!!u&&!u.some(d=>d==="unsafe-inline");this.#c=f(o),this.#l=f(a),this.#f=f(c),this.#d=f(i),this.#p=f(l),this.#t=this.#c||this.#l,this.#u=this.#f||this.#d||this.#p,this.script_needs_nonce=this.#t&&!this.#e,this.style_needs_nonce=this.#u&&!this.#e,this.#h=r}add_script(t){if(!this.#t)return;const n=this.#e?`sha256-${gr(t)}`:`nonce-${this.#h}`;this.#c&&this.#s.push(n),this.#l&&this.#o.push(n)}add_style(t){if(!this.#u)return;const n=this.#e?`sha256-${gr(t)}`:`nonce-${this.#h}`;if(this.#f&&this.#a.push(n),this.#d&&this.#i.push(n),this.#p){const r="sha256-9OlNO0DNEeaVzHL4RZwCLsBHA8WBQ8toBp/4F5XV2nc=",s=this.#r;!s["style-src-elem"]||s["style-src-elem"].includes(r)||this.#n.includes(r)||this.#n.push(r),n!==r&&this.#n.push(n)}}get_header(t=!1){const n=[],r={...this.#r};this.#a.length>0&&(r["style-src"]=[...r["style-src"]||r["default-src"]||[],...this.#a]),this.#i.length>0&&(r["style-src-attr"]=[...r["style-src-attr"]||[],...this.#i]),this.#n.length>0&&(r["style-src-elem"]=[...r["style-src-elem"]||[],...this.#n]),this.#s.length>0&&(r["script-src"]=[...r["script-src"]||r["default-src"]||[],...this.#s]),this.#o.length>0&&(r["script-src-elem"]=[...r["script-src-elem"]||[],...this.#o]);for(const s in r){if(t&&(s==="frame-ancestors"||s==="report-uri"||s==="sandbox"))continue;const o=r[s];if(!o)continue;const a=[s];Array.isArray(o)&&o.forEach(c=>{ro.has(c)||so.test(c)?a.push(`'${c}'`):a.push(c)}),n.push(a.join(" "))}return n.join("; ")}}class oo extends $r{get_meta(){const t=this.get_header(!0);if(t)return`<meta http-equiv="content-security-policy" content="${$t(t,!0)}">`}}class ao extends $r{constructor(t,n,r){if(super(t,n,r),Object.values(n).filter(s=>!!s).length>0){const s=n["report-to"]?.length??!1,o=n["report-uri"]?.length??!1;if(!s&&!o)throw Error("`content-security-policy-report-only` must be specified with either the `report-to` or `report-uri` directives, or both")}}}class io{nonce=function(){return crypto.getRandomValues(br),_r(br)}();csp_provider;report_only_provider;constructor({mode:t,directives:n,reportOnly:r},{prerender:s}){const o=t==="hash"||t==="auto"&&s;this.csp_provider=new oo(o,n,this.nonce),this.report_only_provider=new ao(o,r,this.nonce)}get script_needs_nonce(){return this.csp_provider.script_needs_nonce||this.report_only_provider.script_needs_nonce}get style_needs_nonce(){return this.csp_provider.style_needs_nonce||this.report_only_provider.style_needs_nonce}add_script(t){this.csp_provider.add_script(t),this.report_only_provider.add_script(t)}add_style(t){this.csp_provider.add_style(t),this.report_only_provider.add_style(t)}}function xr(){let e,t;return{promise:new Promise((n,r)=>{e=n,t=r}),fulfil:e,reject:t}}function kr(){const e=[xr()];return{iterator:{[Symbol.asyncIterator]:()=>({next:async()=>{const t=await e[0].promise;return t.done||e.shift(),t}})},push:t=>{e[e.length-1].fulfil({value:t,done:!1}),e.push(xr())},done:()=>{e[e.length-1].fulfil({done:!0})}}}function Sr(e,t,n){const r={},s=e.slice(1),o=s.filter(c=>c!==void 0);let a=0;for(let c=0;c<t.length;c+=1){const i=t[c];let l=s[c-a];if(i.chained&&i.rest&&a&&(l=s.slice(c-a,c+1).filter(f=>f).join("/"),a=0),l!==void 0){if(!i.matcher||n[i.matcher](l)){r[i.name]=l;const f=t[c+1],u=s[c+1];f&&!f.rest&&f.optional&&u&&i.chained&&(a=0),f||u||Object.keys(r).length!==o.length||(a=0);continue}if(!i.optional||!i.chained)return;a++}else i.rest&&(r[i.name]="")}if(!a)return r}function Er(e,t,n){const{errors:r,layouts:s,leaf:o}=e,a=[...r,...s.map(c=>c?.[1]),o[1]].filter(c=>typeof c=="number").map(c=>`'${c}': () => ${jr(n._.client.nodes?.[c],t)}`).join(`,
		`);return[`{
	id: ${ee(e.id)}`,`errors: ${ee(e.errors)}`,`layouts: ${ee(e.layouts)}`,`leaf: ${ee(e.leaf)}`,`nodes: {
		${a}
	}
}`].join(`,
	`)}function jr(e,t){if(!e)return"Promise.resolve({})";if(e[0]==="/")return`import('${e}')`;if(Y!=="")return`import('${Y}/${e}')`;let n=function(r,s){const o=r.split(/[/\\]/),a=s.split(/[/\\]/);for(o.pop();o[0]===a[0];)o.shift(),a.shift();let c=o.length;for(;c--;)o[c]="..";return o.concat(a).join("/")}(t.pathname,`${J}/${e}`);return n[0]!=="."&&(n=`./${n}`),`import('${n}')`}function Rr(e,t,n,r){const s=new Headers({"content-type":"application/javascript; charset=utf-8"});if(e){const o=Er(e,n,r),a=`${function(c,i,l){const{errors:f,layouts:u,leaf:d}=c;let h="";for(const m of[...f,...u.map(v=>v?.[1]),d[1]]){if(typeof m!="number")continue;const v=l._.client.css?.[m];for(const b of v??[])h+=`'${Y||J}/${b}',`}return h?`${jr(l._.client.start,i)}.then(x => x.load_css([${h}]));`:""}(e,n,r)}
export const route = ${o}; export const params = ${JSON.stringify(t)};`;return{response:F(a,{headers:s}),body:a}}return{response:F("",{headers:s}),body:""}}const co={...hr(!1),check:()=>!1},Or=new TextEncoder;async function Me({branch:e,fetched:t,options:n,manifest:r,state:s,page_config:o,status:a,error:c=null,event:i,resolve_opts:l,action_result:f}){if(s.prerendering){if(n.csp.mode==="nonce")throw new Error('Cannot use prerendering if config.kit.csp.mode === "nonce"');if(n.app_template_contains_nonce)throw new Error("Cannot use prerendering if page template contains %sveltekit.nonce%")}const{client:u}=r._,d=new Set(u.imports),h=new Set(u.stylesheets),m=new Set(u.fonts),v=new Set,b=new Map;let y;const O=f?.type==="success"||f?.type==="failure"?f.data??null:null;let N=J,p=Y,$=ee(J);if(s.prerendering?.fallback?n.hash_routing&&($="new URL('.', location).pathname.slice(0, -1)"):(N=i.url.pathname.slice(J.length).split("/").slice(2).map(()=>"..").join("/")||".",$=`new URL(${ee(N)}, location).pathname.slice(0, -1)`,(!Y||Y[0]==="/"&&Y!=="/_svelte_kit_assets")&&(p=N)),o.ssr){const x={stores:{page:Ne(null),navigating:Ne(null),updated:co},constructors:await Promise.all(e.map(({node:E})=>{if(!E.component)throw new Error(`Missing +page.svelte component for route ${i.route.id}`);return E.component()})),form:O};let w={};for(let E=0;E<e.length;E+=1)w={...w,...e[E].data},x[`data_${E}`]=w;x.page={error:c,params:i.params,route:i.route,status:a,url:i.url,data:w,form:O,state:{}},J=(g={base:N,assets:p}).base,Y=g.assets;const H={context:new Map([["__request__",{page:x.page}]])};try{y=n.root.render(x,H)}finally{J=ht.base,Y=ht.assets}for(const{node:E}of e){for(const I of E.imports)d.add(I);for(const I of E.stylesheets)h.add(I);for(const I of E.fonts)m.add(I);E.inline_styles&&!u.inline&&Object.entries(await E.inline_styles()).forEach(([I,K])=>b.set(I,K))}}else y={head:"",html:"",css:{code:"",map:null}};var g;let _="",k=y.html;const P=new io(n.csp,{prerender:!!s.prerendering}),S=x=>x.startsWith("/")?J+x:`${p}/${x}`,A=u.inline?u.inline?.style:Array.from(b.values()).join(`
`);if(A){const x=[];P.style_needs_nonce&&x.push(` nonce="${P.nonce}"`),P.add_style(A),_+=`
	<style${x.join("")}>${A}</style>`}for(const x of h){const w=S(x),H=['rel="stylesheet"'];if(b.has(x))H.push("disabled",'media="(max-width: 0)"');else if(l.preload({type:"css",path:w})){const E=['rel="preload"','as="style"'];v.add(`<${encodeURI(w)}>; ${E.join(";")}; nopush`)}_+=`
		<link href="${w}" ${H.join(" ")}>`}for(const x of m){const w=S(x);l.preload({type:"font",path:w})&&(_+=`
		<link ${['rel="preload"','as="font"',`type="font/${x.slice(x.lastIndexOf(".")+1)}"`,`href="${w}"`,"crossorigin"].join(" ")}>`)}const T=`__sveltekit_${n.version_hash}`,{data:q,chunks:R}=function(x,w,H,E,I){let K=1,se=0;const{iterator:D,push:z,done:ie}=kr();function B(j){if(typeof j?.then=="function"){const W=K++;return se+=1,j.then(C=>({data:C})).catch(async C=>({error:await ne(x,w,C)})).then(async({data:C,error:X})=>{let M;se-=1;try{M=te.uneval({id:W,data:C,error:X},B)}catch{X=await ne(x,w,new Error(`Failed to serialize promise while rendering ${x.route.id}`)),C=void 0,M=te.uneval({id:W,data:C,error:X},B)}const ce=E.script_needs_nonce?` nonce="${E.nonce}"`:"";z(`<script${ce}>${I}.resolve(${M})<\/script>
`),se===0&&ie()}),`${I}.defer(${W})`}for(const W in w.hooks.transport){const C=w.hooks.transport[W].encode(j);if(C)return`app.decode('${W}', ${te.uneval(C,B)})`}}try{return{data:`[${H.map(j=>j?`{"type":"data","data":${te.uneval(j.data,B)},${wn(j)}${j.slash?`,"slash":${JSON.stringify(j.slash)}`:""}}`:"null").join(",")}]`,chunks:se>0?D:null}}catch(j){throw new Error(yn(x,j))}}(i,n,e.map(x=>x.server_data),P,T);if(o.ssr&&o.csr&&(k+=`
			${t.map(x=>function(w,H,E=!1){const I={};let K=null,se=null,D=!1;for(const[j,W]of w.response.headers)H(j,W)&&(I[j]=W),j==="cache-control"?K=W:j==="age"?se=W:j==="vary"&&W.trim()==="*"&&(D=!0);const z={status:w.response.status,statusText:w.response.statusText,headers:I,body:w.response_body},ie=JSON.stringify(z).replace(to,j=>mr[j]),B=['type="application/json"',"data-sveltekit-fetched",`data-url="${$t(w.url,!0)}"`];if(w.is_b64&&B.push("data-b64"),w.request_headers||w.request_body){const j=[];w.request_headers&&j.push([...new Headers(w.request_headers)].join(",")),w.request_body&&j.push(w.request_body),B.push(`data-hash="${vr(...j)}"`)}if(!E&&w.method==="GET"&&K&&!D){const j=/s-maxage=(\d+)/g.exec(K)??/max-age=(\d+)/g.exec(K);if(j){const W=+j[1]-+(se??"0");B.push(`data-ttl="${W}"`)}}return`<script ${B.join(" ")}>${ie}<\/script>`}(x,l.filterSerializedResponseHeaders,!!s.prerendering)).join(`
			`)}`),o.csr){const x=r._.client.routes?.find(D=>D.id===i.route.id)??null;if(u.uses_env_dynamic_public&&s.prerendering&&d.add(`${Ke}/env.js`),!u.inline){const D=Array.from(d,z=>S(z)).filter(z=>l.preload({type:"js",path:z}));for(const z of D)v.add(`<${encodeURI(z)}>; rel="modulepreload"; nopush`),n.preload_strategy!=="modulepreload"?_+=`
		<link rel="preload" as="script" crossorigin="anonymous" href="${z}">`:s.prerendering&&(_+=`
		<link rel="modulepreload" href="${z}">`)}if(r._.client.routes&&s.prerendering&&!s.prerendering.fallback){const D=un(i.url.pathname);s.prerendering.dependencies.set(D,Rr(x,i.params,new URL(D,i.url),r))}const w=[],H=u.uses_env_dynamic_public&&s.prerendering,E=[`base: ${$}`];Y&&E.push(`assets: ${ee(Y)}`),u.uses_env_dynamic_public&&E.push(`env: ${H?"null":ee(bt)}`),R&&(w.push("const deferred = new Map();"),E.push(`defer: (id) => new Promise((fulfil, reject) => {
							deferred.set(id, { fulfil, reject });
						})`),E.push(`resolve: ({ id, data, error }) => {
							const try_to_resolve = () => {
								if (!deferred.has(id)) {
									setTimeout(try_to_resolve, 0);
									return;
								}
								const { fulfil, reject } = deferred.get(id);
								deferred.delete(id);
								if (error) reject(error);
								else fulfil(data);
							}
							try_to_resolve();
						}`)),w.push(`${T} = {
						${E.join(`,
						`)}
					};`);const I=["element"];if(w.push("const element = document.currentScript.parentElement;"),o.ssr){const D={form:"null",error:"null"};O&&(D.form=function(B,j,W){const C=X=>{for(const M in W){const ce=W[M].encode(X);if(ce)return`app.decode('${M}', ${te.uneval(ce,C)})`}};return Rn(B,X=>te.uneval(X,C),j)}(O,i.route.id,n.hooks.transport)),c&&(D.error=te.uneval(c));const z=[`node_ids: [${e.map(({node:B})=>B.index).join(", ")}]`,`data: ${q}`,`form: ${D.form}`,`error: ${D.error}`];if(a!==200&&z.push(`status: ${a}`),r._.client.routes){if(x){const B=Er(x,i.url,r).replaceAll(`
`,`
							`);z.push(`params: ${te.uneval(i.params)}`,`server_route: ${B}`)}}else n.embedded&&z.push(`params: ${te.uneval(i.params)}`,`route: ${ee(i.route)}`);const ie="	".repeat(H?7:6);I.push(`{
${ie}	${z.join(`,
${ie}	`)}
${ie}}`)}const K=u.inline?`${u.inline.script}

					__sveltekit_${n.version_hash}.app.start(${I.join(", ")});`:u.app?`Promise.all([
						import(${ee(S(u.start))}),
						import(${ee(S(u.app))})
					]).then(([kit, app]) => {
						kit.start(app, ${I.join(", ")});
					});`:`import(${ee(S(u.start))}).then((app) => {
						app.start(${I.join(", ")})
					});`;H?w.push(`import(${ee(`${N}/${Ke}/env.js`)}).then(({ env }) => {
						${T}.env = env;

						${K.replace(/\n/g,`
	`)}
					});`):w.push(K),n.service_worker&&w.push(`if ('serviceWorker' in navigator) {
						addEventListener('load', function () {
							navigator.serviceWorker.register('${S("service-worker.js")}');
						});
					}`);const se=`
				{
					${w.join(`

					`)}
				}
			`;P.add_script(se),k+=`
			<script${P.script_needs_nonce?` nonce="${P.nonce}"`:""}>${se}<\/script>
		`}const U=new Headers({"x-sveltekit-page":"true","content-type":"text/html"});if(s.prerendering){const x=[],w=P.csp_provider.get_meta();w&&x.push(w),s.prerendering.cache&&x.push(`<meta http-equiv="cache-control" content="${s.prerendering.cache}">`),x.length>0&&(_=x.join(`
`)+_)}else{const x=P.csp_provider.get_header();x&&U.set("content-security-policy",x);const w=P.report_only_provider.get_header();w&&U.set("content-security-policy-report-only",w),v.size&&U.set("link",Array.from(v).join(", "))}_+=y.head;const me=n.templates.app({head:_,body:k,assets:p,nonce:P.nonce,env:dn}),ge=await l.transformPageChunk({html:me,done:!0})||"";return R||U.set("etag",`"${vr(ge)}"`),R?new Response(new ReadableStream({async start(x){x.enqueue(Or.encode(ge+`
`));for await(const w of R)x.enqueue(Or.encode(w));x.close()},type:"bytes"}),{headers:U}):F(ge,{status:a,headers:U})}function Fe(e){return function(t,n){if(t)for(const r in t){if(r[0]==="_"||e.has(r))continue;const s=[...e.values()],o=lo(r,n?.slice(n.lastIndexOf(".")))??`valid exports are ${s.join(", ")}, or anything with a '_' prefix`;throw new Error(`Invalid export '${r}'${n?` in ${n}`:""} (${o})`)}}}function lo(e,t=".js"){const n=[];if(dt.has(e)&&n.push(`+layout${t}`),Pr.has(e)&&n.push(`+page${t}`),zt.has(e)&&n.push(`+layout.server${t}`),Tr.has(e)&&n.push(`+page.server${t}`),qr.has(e)&&n.push(`+server${t}`),n.length>0)return`'${e}' is a valid export in ${n.slice(0,-1).join(", ")}${n.length>1?" or ":""}${n.at(-1)}`}const dt=new Set(["load","prerender","csr","ssr","trailingSlash","config"]),Pr=new Set([...dt,"entries"]),zt=new Set([...dt]),Tr=new Set([...zt,"actions","entries"]),qr=new Set(["GET","POST","PATCH","PUT","DELETE","OPTIONS","HEAD","fallback","prerender","trailingSlash","config","entries"]),uo=Fe(dt),fo=Fe(Pr),po=Fe(zt),ho=Fe(Tr);Fe(qr);class Ar{data;constructor(t){this.data=t}layouts(){return this.data.slice(0,-1)}page(){return this.data.at(-1)}validate(){for(const n of this.layouts())n&&(po(n.server,n.server_id),uo(n.universal,n.universal_id));const t=this.page();t&&(ho(t.server,t.server_id),fo(t.universal,t.universal_id))}#e(t){return this.data.reduce((n,r)=>r?.universal?.[t]??r?.server?.[t]??n,void 0)}csr(){return this.#e("csr")??!0}ssr(){return this.#e("ssr")??!0}prerender(){return this.#e("prerender")??!1}trailing_slash(){return this.#e("trailingSlash")??"never"}get_config(){let t={};for(const n of this.data)(n?.universal?.config||n?.server?.config)&&(t={...t,...n?.universal?.config,...n?.server?.config});return Object.keys(t).length?t:void 0}should_prerender_data(){return this.data.some(t=>t?.server?.load||t?.server?.trailingSlash!==void 0)}}async function Ur({event:e,options:t,manifest:n,state:r,status:s,error:o,resolve_opts:a}){if(e.request.headers.get("x-sveltekit-error"))return Xe(t,s,o.message);const c=[];try{const i=[],l=await n._.nodes[0](),f=new Ar([l]),u=f.ssr(),d=f.csr();if(u){r.error=!0;const h=kt({event:e,state:r,node:l,parent:async()=>({})}),m=await h,v=await Nn({event:e,fetched:c,node:l,parent:async()=>({}),resolve_opts:a,server_data_promise:h,state:r,csr:d});i.push({node:l,server_data:m,data:v},{node:await n._.nodes[1](),data:null,server_data:null})}return await Me({options:t,manifest:n,state:r,page_config:{ssr:u,csr:d},status:s,error:await ne(e,t,o),branch:i,fetched:c,event:e,resolve_opts:a})}catch(i){return i instanceof le?Ye(i.status,i.location):Xe(t,Oe(i),(await ne(e,t,i)).message)}}const Cr=new TextEncoder;function Mt(e,t=200){return F(typeof e=="string"?e:JSON.stringify(e),{status:t,headers:{"content-type":"application/json","cache-control":"private, no-store"}})}function Ft(e){return Mt({type:"redirect",location:e.location})}function Nr(e,t,n){let r=1,s=0;const{iterator:o,push:a,done:c}=kr(),i={...Object.fromEntries(Object.entries(t.hooks.transport).map(([l,f])=>[l,f.encode])),Promise:l=>{if(typeof l?.then=="function"){const f=r++;s+=1;let u="data";return l.catch(async d=>(u="error",ne(e,t,d))).then(async d=>{let h;try{h=te.stringify(d,i)}catch{const m=await ne(e,t,new Error(`Failed to serialize promise while rendering ${e.route.id}`));u="error",h=te.stringify(m,i)}s-=1,a(`{"type":"chunk","id":${f},"${u}":${h}}
`),s===0&&c()}),f}}};try{return{data:`{"type":"data","nodes":[${n.map(l=>l?l.type==="error"||l.type==="skip"?JSON.stringify(l):`{"type":"data","data":${te.stringify(l.data,i)},${wn(l)}${l.slash?`,"slash":${JSON.stringify(l.slash)}`:""}}`:"null").join(",")}]}
`,chunks:s>0?o:null}}catch(l){throw new Error(yn(e,l))}}async function vo(e,t,n,r,s,o,a){if(s.depth>10)return F(`Not found: ${e.url.pathname}`,{status:404});if($n(e)){const c=await r._.nodes[t.leaf]();return async function(i,l,f){const u=f?.actions;if(!u){const d=new ye(405,"Method Not Allowed","POST method not allowed. No form actions exist for this page");return Qe({type:"error",error:await ne(i,l,d)},{status:d.status,headers:{allow:"GET"}})}Sn(u);try{const d=await En(i,u);return Qe(d instanceof gt?{type:"failure",status:d.status,data:jn(d.data,i.route.id,l.hooks.transport)}:{type:"success",status:d?200:204,data:jn(d,i.route.id,l.hooks.transport)})}catch(d){const h=d;return h instanceof le?kn(h):Qe({type:"error",error:await ne(i,l,xn(h))},{status:Oe(h)})}}(e,n,c?.server)}try{const c=o.page();let i,l=200;if(function(p){return p.request.method==="POST"}(e)){if(i=await async function(p,$){const g=$?.actions;if(!g)return p.setHeaders({allow:"GET"}),{type:"error",error:new ye(405,"Method Not Allowed","POST method not allowed. No form actions exist for this page")};Sn(g);try{const _=await En(p,g);return _ instanceof gt?{type:"failure",status:_.status,data:_.data}:{type:"success",status:200,data:_}}catch(_){const k=_;return k instanceof le?{type:"redirect",status:k.status,location:k.location}:{type:"error",error:xn(k)}}}(e,c.server),i?.type==="redirect")return Ye(i.status,i.location);i?.type==="error"&&(l=Oe(i.error)),i?.type==="failure"&&(l=i.status)}const f=o.prerender();if(f){if(c.server?.actions)throw new Error("Cannot prerender pages with actions")}else if(s.prerendering)return new Response(void 0,{status:204});s.prerender_default=f;const u=o.should_prerender_data(),d=wt(e.url.pathname),h=[];if(!(o.ssr()!==!1||s.prerendering&&u))return Be&&i&&e.request.headers.has("x-sveltekit-action"),await Me({branch:[],fetched:h,page_config:{ssr:!1,csr:o.csr()},status:l,error:null,event:e,options:n,manifest:r,state:s,resolve_opts:a});const m=[];let v=null;const b=o.data.map((p,$)=>{if(v)throw v;return Promise.resolve().then(async()=>{try{if(p===c&&i?.type==="error")throw i.error;return await kt({event:e,state:s,node:p,parent:async()=>{const g={};for(let _=0;_<$;_+=1){const k=await b[_];k&&Object.assign(g,k.data)}return g}})}catch(g){throw v=g,v}})}),y=o.csr(),O=o.data.map((p,$)=>{if(v)throw v;return Promise.resolve().then(async()=>{try{return await Nn({event:e,fetched:h,node:p,parent:async()=>{const g={};for(let _=0;_<$;_+=1)Object.assign(g,await O[_]);return g},resolve_opts:a,server_data_promise:b[$],state:s,csr:y})}catch(g){throw v=g,v}})});for(const p of b)p.catch(()=>{});for(const p of O)p.catch(()=>{});for(let p=0;p<o.data.length;p+=1){const $=o.data[p];if($)try{const g=await b[p],_=await O[p];m.push({node:$,server_data:g,data:_})}catch(g){const _=g;if(_ instanceof le){if(s.prerendering&&u){const S=JSON.stringify({type:"redirect",location:_.location});s.prerendering.dependencies.set(d,{response:F(S),body:S})}return Ye(_.status,_.location)}const k=Oe(_),P=await ne(e,n,_);for(;p--;)if(t.errors[p]){const S=t.errors[p],A=await r._.nodes[S]();let T=p;for(;!m[T];)T-=1;return await Me({event:e,options:n,manifest:r,state:s,resolve_opts:a,page_config:{ssr:!0,csr:!0},status:k,error:P,branch:bn(m.slice(0,T+1)).concat({node:A,data:null,server_data:null}),fetched:h})}return Xe(n,k,P.message)}else m.push(null)}if(s.prerendering&&u){let{data:p,chunks:$}=Nr(e,n,m.map(g=>g?.server_data));if($)for await(const g of $)p+=g;s.prerendering.dependencies.set(d,{response:F(p),body:p})}const N=o.ssr();return await Me({event:e,options:n,manifest:r,state:s,resolve_opts:a,page_config:{csr:o.csr(),ssr:N},status:l,error:null,branch:N===!1?[]:bn(m),action_result:i,fetched:h})}catch(c){return await Ur({event:e,options:n,manifest:r,state:s,status:500,error:c,resolve_opts:a})}}const mo=/[\x00-\x1F\x7F()<>@,;:"/[\]?={} \t]/;function Jt(e){if(e?.path===void 0)throw new Error("You must specify a `path` when setting, deleting or serializing cookies")}function go(e,t){const n=e.headers.get("cookie")??"",r=pt(n,{decode:f=>f});let s;const o={},a={httpOnly:!0,sameSite:"lax",secure:t.hostname!=="localhost"||t.protocol!=="http:"},c={get(f,u){const d=o[f];return d&&Bt(t.hostname,d.options.domain)&&Gt(t.pathname,d.options.path)?d.value:pt(n,{decode:u?.decode})[f]},getAll(f){const u=pt(n,{decode:f?.decode});for(const d of Object.values(o))Bt(t.hostname,d.options.domain)&&Gt(t.pathname,d.options.path)&&(u[d.name]=d.value);return Object.entries(u).map(([d,h])=>({name:d,value:h}))},set(f,u,d){f.match(mo),Jt(d),l(f,u,{...a,...d})},delete(f,u){Jt(u),c.set(f,"",{...u,maxAge:0})},serialize(f,u,d){Jt(d);let h=d.path;if(!d.domain||d.domain===t.hostname){if(!s)throw new Error("Cannot serialize cookies until after the route is determined");h=Pn(s,h)}return Qt(f,u,{...a,...d,path:h})}},i=[];function l(f,u,d){if(!s)return void i.push(()=>l(f,u,d));let h=d.path;d.domain&&d.domain!==t.hostname||(h=Pn(s,h)),o[f]={name:f,value:u,options:{...d,path:h}}}return{cookies:c,new_cookies:o,get_cookie_header:function(f,u){const d={...r};for(const h in o){const m=o[h];if(!Bt(f.hostname,m.options.domain)||!Gt(f.pathname,m.options.path))continue;const v=m.options.encode||encodeURIComponent;d[m.name]=v(m.value)}if(u){const h=pt(u,{decode:m=>m});for(const m in h)d[m]=h[m]}return Object.entries(d).map(([h,m])=>`${h}=${m}`).join("; ")},set_internal:l,set_trailing_slash:function(f){s=xt(t.pathname,f),i.forEach(u=>u())}}}function Bt(e,t){if(!t)return!0;const n=t[0]==="."?t.slice(1):t;return e===n||e.endsWith("."+n)}function Gt(e,t){if(!t)return!0;const n=t.endsWith("/")?t.slice(0,-1):t;return e===n||e.startsWith(n+"/")}function Lr(e,t){for(const n of t){const{name:r,value:s,options:o}=n;if(e.append("set-cookie",Qt(r,s,o)),o.path.endsWith(".html")){const a=wt(o.path);e.append("set-cookie",Qt(r,s,{...o,path:a}))}}}let Hr,Vt,Kt,Xt=null;en=function(e){Xt=e},es=function(e){};function yo({event:e,options:t,manifest:n,state:r,get_cookie_header:s,set_internal:o}){return(a,c)=>{const i=(async(l,f)=>{const u=Ir(l,f,e.url);let d=(l instanceof Request?l.mode:f?.mode)??"cors",h=(l instanceof Request?l.credentials:f?.credentials)??"same-origin";return t.hooks.handleFetch({event:e,request:u,fetch:async(m,v)=>{const b=Ir(m,v,e.url),y=new URL(b.url);if(b.headers.has("origin")||b.headers.set("origin",e.url.origin),m!==u&&(d=(m instanceof Request?m.mode:v?.mode)??"cors",h=(m instanceof Request?m.credentials:v?.credentials)??"same-origin"),b.method!=="GET"&&b.method!=="HEAD"||(d!=="no-cors"||y.origin===e.url.origin)&&y.origin!==e.url.origin||b.headers.delete("origin"),y.origin!==e.url.origin){if(`.${y.hostname}`.endsWith(`.${e.url.hostname}`)&&h!=="omit"){const S=s(y,b.headers.get("cookie"));S&&b.headers.set("cookie",S)}return fetch(b)}const O=Y||J,N=decodeURIComponent(y.pathname),p=(N.startsWith(O)?N.slice(O.length):N).slice(1),$=`${p}/index.html`,g=n.assets.has(p)||p in n._.server_assets,_=n.assets.has($)||$ in n._.server_assets;if(g||_){const S=g?p:$;if(r.read){const A=g?n.mimeTypes[p.slice(p.lastIndexOf("."))]:"text/html";return new Response(r.read(S),{headers:A?{"content-type":A}:{}})}if(Xt&&S in n._.server_assets){const A=n._.server_assets[S],T=n.mimeTypes[S.slice(S.lastIndexOf("."))];return new Response(Xt(S),{headers:{"Content-Length":""+A,"Content-Type":T}})}return await fetch(b)}if(_n(n,J+N))return await fetch(b);if(h!=="omit"){const S=s(y,b.headers.get("cookie"));S&&b.headers.set("cookie",S);const A=e.request.headers.get("authorization");A&&!b.headers.has("authorization")&&b.headers.set("authorization",A)}b.headers.has("accept")||b.headers.set("accept","*/*"),b.headers.has("accept-language")||b.headers.set("accept-language",e.request.headers.get("accept-language"));const k=await Mr(b,t,n,{...r,depth:r.depth+1}),P=k.headers.get("set-cookie");if(P)for(const S of Zr.splitCookiesString(P)){const{name:A,value:T,...q}=Zr.parseString(S,{decodeValues:!1}),R=q.path??(y.pathname.split("/").slice(0,-1).join("/")||"/");o(A,T,{path:R,encode:U=>U,...q})}return k}})})(a,c);return i.catch(()=>{}),i}}function Ir(e,t,n){return e instanceof Request?e:new Request(typeof e=="string"?new URL(e,n):e,t)}function wo(e){return Hr??=`export const env=${JSON.stringify(bt)}`,Vt??=`W/${Date.now()}`,Kt??=new Headers({"content-type":"application/javascript; charset=utf-8",etag:Vt}),e.headers.get("if-none-match")===Vt?new Response(void 0,{status:304,headers:Kt}):new Response(Hr,{headers:Kt})}const Dr=({html:e})=>e,Wr=()=>!1,zr=({type:e})=>e==="js"||e==="css",_o=new Set(["GET","HEAD","POST"]),bo=new Set(["GET","HEAD","OPTIONS"]);async function Mr(e,t,n,r){const s=new URL(e.url);if(t.csrf_check_origin&&on(e)&&(e.method==="POST"||e.method==="PUT"||e.method==="PATCH"||e.method==="DELETE")&&e.headers.get("origin")!==s.origin){const p=new Le(403,`Cross-site ${e.method} form submissions are forbidden`);return e.headers.get("accept")==="application/json"?_t(p.body,{status:p.status}):F(p.body.message,{status:p.status})}if(t.hash_routing&&s.pathname!==J+"/"&&s.pathname!=="/[fallback]")return F("Not found",{status:404});let o;const a=s.pathname.endsWith(ln),c=function(p){return p.endsWith(cn)||p.endsWith(yt)}(s.pathname);a?s.pathname=function(p){return p.slice(0,-11)}(s.pathname):c&&(s.pathname=function(p){return p.endsWith(yt)?p.slice(0,-16)+".html":p.slice(0,-12)}(s.pathname)+(s.searchParams.get(Cn)==="1"?"/":"")||"/",s.searchParams.delete(Cn),o=s.searchParams.get(Un)?.split("").map(p=>p==="1"),s.searchParams.delete(Un));const i={},{cookies:l,new_cookies:f,get_cookie_header:u,set_internal:d,set_trailing_slash:h}=go(e,s),m={cookies:l,fetch:null,getClientAddress:r.getClientAddress||(()=>{throw new Error("@sveltejs/adapter-static does not specify getClientAddress. Please raise an issue")}),locals:{},params:{},platform:r.platform,request:e,route:{id:null},setHeaders:p=>{for(const $ in p){const g=$.toLowerCase(),_=p[$];if(g==="set-cookie")throw new Error("Use `event.cookies.set(name, value, options)` instead of `event.setHeaders` to set cookies");if(g in i)throw new Error(`"${$}" header is already set`);i[g]=_,r.prerendering&&g==="cache-control"&&(r.prerendering.cache=_)}},url:s,isDataRequest:c,isSubRequest:r.depth>0};let v;m.fetch=yo({event:m,options:t,manifest:n,state:r,get_cookie_header:u,set_internal:d}),r.emulator?.platform&&(m.platform=await r.emulator.platform({config:{},prerender:!!r.prerendering?.fallback}));const b=r.prerendering?.inside_reroute;try{r.prerendering&&(r.prerendering.inside_reroute=!0),v=await t.hooks.reroute({url:new URL(s),fetch:m.fetch})??s.pathname}catch{return F("Internal Server Error",{status:500})}finally{r.prerendering&&(r.prerendering.inside_reroute=b)}try{v=function(p){return p.split("%25").map(decodeURI).join("%25")}(v)}catch{return F("Malformed URI",{status:400})}if(v!==s.pathname&&!r.prerendering?.fallback&&_n(n,v)){const p=new URL(e.url);p.pathname=c?wt(v):a?un(v):v;const $=await fetch(p,e),g=new Headers($.headers);return g.has("content-encoding")&&(g.delete("content-encoding"),g.delete("content-length")),new Response($.body,{headers:g,status:$.status,statusText:$.statusText})}let y=null;if(J&&!r.prerendering?.fallback){if(!v.startsWith(J))return F("Not found",{status:404});v=v.slice(J.length)||"/"}if(a)return async function(p,$,g){if(!g._.client.routes)return F("Server-side route resolution disabled",{status:400});let _=null,k={};const P=await g._.matchers();for(const S of g._.client.routes){const A=S.pattern.exec(p);if(!A)continue;const T=Sr(A,S.params,P);if(T){_=S,k=Tn(T);break}}return Rr(_,k,$,g).response}(v,new URL(e.url),n);if(v===`/${Ke}/env.js`)return wo(e);if(v.startsWith(`/${Ke}`)){const p=new Headers;return p.set("cache-control","public, max-age=0, must-revalidate"),F("Not found",{status:404,headers:p})}if(!r.prerendering?.fallback){const p=await n._.matchers();for(const $ of n._.routes){const g=$.pattern.exec(v);if(!g)continue;const _=Sr(g,$.params,p);if(_){y=$,m.route={id:y.id},m.params=Tn(_);break}}}let O={transformPageChunk:Dr,filterSerializedResponseHeaders:Wr,preload:zr},N="never";try{const p=y?.page?new Ar(await function(g,_){return Promise.all([...g.layouts.map(k=>k==null?k:_._.nodes[k]()),_._.nodes[g.leaf]()])}(y.page,n)):void 0;if(y){if(s.pathname===J||s.pathname===J+"/"?N="always":p?N=p.trailing_slash():y.endpoint&&(N=(await y.endpoint()).trailingSlash??"never"),!c){const g=xt(s.pathname,N);if(g!==s.pathname&&!r.prerendering?.fallback)return new Response(void 0,{status:308,headers:{"x-sveltekit-normalize":"1",location:(g.startsWith("//")?s.origin+g:g)+(s.search==="?"?"":s.search)}})}if(r.before_handle||r.emulator?.platform){let g={},_=!1;if(y.endpoint){const k=await y.endpoint();g=k.config??g,_=k.prerender??_}else p&&(g=p.get_config()??g,_=p.prerender());r.before_handle&&r.before_handle(m,g,_),r.emulator?.platform&&(m.platform=await r.emulator.platform({config:g,prerender:_}))}}h(N),!r.prerendering||r.prerendering.fallback||r.prerendering.inside_reroute||qn(s);const $=await Re(m,()=>t.hooks.handle({event:m,resolve:(g,_)=>Re(null,()=>async function(k,P,S){try{if(S&&(O={transformPageChunk:S.transformPageChunk||Dr,filterSerializedResponseHeaders:S.filterSerializedResponseHeaders||Wr,preload:S.preload||zr}),t.hash_routing||r.prerendering?.fallback)return await Me({event:k,options:t,manifest:n,state:r,page_config:{ssr:!1,csr:!0},status:200,error:null,branch:[],fetched:[],resolve_opts:O});if(y){const A=k.request.method;let T;if(c)T=await async function(q,R,U,me,ge,x,w){if(!R.page)return new Response(void 0,{status:404});try{const H=[...R.page.layouts,R.page.leaf],E=x??H.map(()=>!0);let I=!1;const K=new URL(q.url);K.pathname=xt(K.pathname,w);const se={...q,url:K},D=H.map((C,X)=>function(M){let ce,Ce=!1;return()=>Ce?ce:(Ce=!0,ce=M())}(async()=>{try{if(I)return{type:"skip"};const M=C==null?C:await me._.nodes[C]();return kt({event:se,state:ge,node:M,parent:async()=>{const ce={};for(let Ce=0;Ce<X;Ce+=1){const Qr=await D[Ce]();Qr&&Object.assign(ce,Qr.data)}return ce}})}catch(M){throw I=!0,M}})),z=D.map(async(C,X)=>E[X]?C():{type:"skip"});let ie=z.length;const B=await Promise.all(z.map((C,X)=>C.catch(async M=>{if(M instanceof le)throw M;return ie=Math.min(ie,X+1),{type:"error",error:await ne(q,U,M),status:M instanceof Le||M instanceof ye?M.status:void 0}}))),{data:j,chunks:W}=Nr(q,U,B);return W?new Response(new ReadableStream({async start(C){C.enqueue(Cr.encode(j));for await(const X of W)C.enqueue(Cr.encode(X));C.close()},type:"bytes"}),{headers:{"content-type":"text/sveltekit-data","cache-control":"private, no-store"}}):Mt(j)}catch(H){const E=H;return E instanceof le?Ft(E):Mt(await ne(q,U,E),500)}}(k,y,t,n,r,o,N);else if(!y.endpoint||y.page&&!function(q){const{method:R,headers:U}=q.request;return!(!sn.includes(R)||Ss.includes(R))||(R!=="POST"||U.get("x-sveltekit-action")!=="true")&&vt(q.request.headers.get("accept")??"*/*",["*","text/html"])!=="text/html"}(k)){if(!y.page)throw new Error("Route is neither page nor endpoint. This should never happen");if(!P)throw new Error("page_nodes not found. This should never happen");if(_o.has(A))T=await vo(k,y.page,t,n,r,P,O);else{const q=new Set(bo);(await n._.nodes[y.page.leaf]())?.server?.actions&&q.add("POST"),A==="OPTIONS"?T=new Response(null,{status:204,headers:{allow:Array.from(q.values()).join(", ")}}):T=mn([...q].reduce((U,me)=>(U[me]=!0,U),{}),A)}}else T=await async function(q,R,U){const me=q.request.method;let ge=R[me]||R.fallback;if(me==="HEAD"&&R.GET&&!R.HEAD&&(ge=R.GET),!ge)return mn(R,me);const x=R.prerender??U.prerender_default;if(x&&(R.POST||R.PATCH||R.PUT||R.DELETE))throw new Error("Cannot prerender endpoints that have mutative methods");if(U.prerendering&&!U.prerendering.inside_reroute&&!x){if(U.depth>0)throw new Error(`${q.route.id} is not prerenderable`);return new Response(void 0,{status:204})}try{const w=await Re(q,()=>ge(q));if(!(w instanceof Response))throw new Error(`Invalid response from route ${q.url.pathname}: handler should return a Response object`);if(U.prerendering&&(!U.prerendering.inside_reroute||x)){const H=new Response(w.clone().body,{status:w.status,statusText:w.statusText,headers:new Headers(w.headers)});if(H.headers.set("x-sveltekit-prerender",String(x)),!U.prerendering.inside_reroute||!x)return H;H.headers.set("x-sveltekit-routeid",encodeURI(q.route.id)),U.prerendering.dependencies.set(q.url.pathname,{response:H,body:null})}return w}catch(w){if(w instanceof le)return new Response(void 0,{status:w.status,headers:{location:w.location}});throw w}}(k,await y.endpoint(),r);if(e.method==="GET"&&y.page&&y.endpoint){const q=T.headers.get("vary")?.split(",")?.map(R=>R.trim().toLowerCase());q?.includes("accept")||q?.includes("*")||(T=new Response(T.body,{status:T.status,statusText:T.statusText,headers:new Headers(T.headers)}),T.headers.append("Vary","Accept"))}return T}if(r.error&&k.isSubRequest){const A=new Headers(e.headers);return A.set("x-sveltekit-error","true"),await fetch(e,{headers:A})}return r.error?F("Internal Server Error",{status:500}):r.depth===0?await Ur({event:k,options:t,manifest:n,state:r,status:404,error:new ye(404,"Not Found",`Not found: ${k.url.pathname}`),resolve_opts:O}):r.prerendering?F("not found",{status:404}):await fetch(e)}catch(A){return await gn(k,t,A)}finally{k.cookies.set=()=>{throw new Error("Cannot use `cookies.set(...)` after the response has been generated")},k.setHeaders=()=>{throw new Error("Cannot use `setHeaders(...)` after the response has been generated")}}}(g,p,_).then(k=>{for(const P in i){const S=i[P];k.headers.set(P,S)}return Lr(k.headers,Object.values(f)),r.prerendering&&g.route.id!==null&&k.headers.set("x-sveltekit-routeid",encodeURI(g.route.id)),k}))}));if($.status===200&&$.headers.has("etag")){let g=e.headers.get("if-none-match");g?.startsWith('W/"')&&(g=g.substring(2));const _=$.headers.get("etag");if(g===_){const k=new Headers({etag:_});for(const P of["cache-control","content-location","date","expires","vary","set-cookie"]){const S=$.headers.get(P);S&&k.set(P,S)}return new Response(void 0,{status:304,headers:k})}}if(c&&$.status>=300&&$.status<=308){const g=$.headers.get("location");if(g)return Ft(new le($.status,g))}return $}catch(p){if(p instanceof le){const $=c?Ft(p):y?.page&&$n(m)?kn(p):Ye(p.status,p.location);return Lr($.headers,Object.values(f)),$}return await gn(m,t,p)}}var ve=null;function Fr(e){return ve===null&&function(){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}(),ve.c??=new Map(function(t){let n=t.p;for(;n!==null;){const r=n.c;if(r!==null)return r;n=n.p}return null}(ve)||void 0)}Ge=function(e){ve={p:ve,c:null,d:null}},Ve=function(){var e=ve,t=e.d;t&&Je.push(...t),ve=e.p};const Jr="<!--[-->",Br="<!--]-->";us=function(e){return"<!---->"+String(e??"")+"<!---->"},vs=function({out:e,css:t,head:n,uid:r}){return{out:e,css:new Set(t),head:{title:n.title,out:n.out,css:new Set(n.css),uid:n.uid},uid:r}},ms=function(e,t){e.out=t.out,e.css=t.css,e.head=t.head,e.uid=t.uid};let Je=[];ys=function(e,t){const n=e.head;n.out+=Jr,t(n),n.out+=Br},as=function(e){return typeof e=="string"?e:e==null?"":e+""},os=function(e,t,n){var r=function(s,o,a){var c=s==null?"":""+s;if(o&&(c=c?c+" "+o:o),a){for(var i in a)if(a[i])c=c?c+" "+i:i;else if(c.length)for(var l=i.length,f=0;(f=c.indexOf(i,f))>=0;){var u=f+l;f!==0&&!pr.includes(c[f-1])||u!==c.length&&!pr.includes(c[u])?f=u:c=(f===0?"":c.substring(0,f))+c.substring(u+1)}}return c===""?null:c}(e,t,n);return r?` class="${je(r,!0)}"`:""},ds=function(e,t){var n=function(r){return r==null?null:String(r)}(e);return n?` style="${je(n,!0)}"`:""},_s=function(e,t,n){if(t in e&&e[t][0]===n)return e[t][2];e[t]?.[1](),e[t]=[n,null,void 0];const r=Dt(n,s=>e[t][2]=s);return e[t][1]=r,e[t][2]},bs=function(e,t){return e.set(t),t},$s=function(e){for(const t in e)e[t][1]()},ps=function(e,t,n,r,s){var o=t.$$slots?.[n];o===!0&&(o=t.children),o!==void 0&&o(e,r)},hs=function(e,t){for(const n in t){const r=e[n],s=t[n];r===void 0&&s!==void 0&&Object.getOwnPropertyDescriptor(e,n)?.set&&(e[n]=s)}},cs=function(e){return e?e.length!==void 0?e:Array.from(e):[]},ws=function(e){var t=ve;(t.d??=[]).push(e)};let Yt=!1;ks=function(){},ts=function(){Yt=!0};let Gr;Gr=function(e){const t=function(n){return class extends eo{constructor(r){super({component:n,...r})}}}(e);return t.render=(n,{context:r}={})=>{const s=function(o,a={}){const c=function(u){let d=1;return()=>`${u}s${d++}`}(a.idPrefix?a.idPrefix+"-":""),i={out:"",css:new Set,head:{title:"",out:"",css:new Set,uid:c},uid:c},l=Je;Je=[],i.out+=Jr,a.context&&(Ge(),ve.c=a.context),o(i,a.props??{},{},{}),a.context&&Ve(),i.out+=Br;for(const u of Je)u();Je=l;let f=i.head.out+i.head.title;for(const{hash:u,code:d}of i.css)f+=`<style id="${u}">${d}</style>`;return{head:f,html:i.out,body:i.out}}(e,{props:n,context:r});return{css:{code:"",map:null},head:s.head,html:s.body}},t}(function(e,t){Ge();let{stores:n,page:r,constructors:s,components:o=[],form:a,data_0:c=null,data_1:i=null}=t;(function(f,u){Fr().set(f,u)})("__svelte__",n),n.page.set(r);const l=s[1];if(s[1]){e.out+="<!--[-->";const f=s[0];e.out+="<!---->",f(e,{data:c,form:a,children:u=>{u.out+="<!---->",l(u,{data:i,form:a}),u.out+="<!---->"},$$slots:{default:!0}}),e.out+="<!---->"}else{e.out+="<!--[!-->";const f=s[0];e.out+="<!---->",f(e,{data:c,form:a}),e.out+="<!---->"}e.out+="<!--]--> ",e.out+="<!--[!-->",e.out+="<!--]-->",Ve()}),nn={app_template_contains_nonce:!1,csp:{mode:"auto",directives:{"upgrade-insecure-requests":!1,"block-all-mixed-content":!1},reportOnly:{"upgrade-insecure-requests":!1,"block-all-mixed-content":!1}},csrf_check_origin:!0,embedded:!1,env_public_prefix:"PUBLIC_",env_private_prefix:"",hash_routing:!1,hooks:null,preload_strategy:"modulepreload",root:Gr,service_worker:!1,templates:{app:({head:e,body:t,assets:n,nonce:r,env:s})=>`<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <!-- \u517C\u5BB9\u6027 meta \u6807\u7B7E -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <!-- \u9884\u52A0\u8F7D\u5173\u952E polyfills -->
    <script>
      // \u7ACB\u5373\u6267\u884C\u7684\u517C\u5BB9\u6027\u68C0\u67E5\u548C\u57FA\u7840 polyfills
      // URLSearchParams polyfill for Chrome 78-
      if (!window.URLSearchParams) {
        window.URLSearchParams = function(search) {
          this.params = new Map();
          if (search) {
            const searchStr = search.startsWith('?') ? search.slice(1) : search;
            searchStr.split('&').forEach(function(pair) {
              const parts = pair.split('=');
              const key = parts[0];
              const value = parts[1];
              if (key) {
                this.params.set(decodeURIComponent(key), decodeURIComponent(value || ''));
              }
            }.bind(this));
          }
        };
        window.URLSearchParams.prototype.get = function(key) {
          return this.params.get(key) || null;
        };
        window.URLSearchParams.prototype.set = function(key, value) {
          this.params.set(key, value);
        };
        window.URLSearchParams.prototype.has = function(key) {
          return this.params.has(key);
        };
      }
      
      // Promise.allSettled polyfill for Chrome 76-
      if (!Promise.allSettled) {
        Promise.allSettled = function(promises) {
          return Promise.all(
            promises.map(function(promise) {
              return Promise.resolve(promise)
                .then(function(value) { return { status: 'fulfilled', value: value }; })
                .catch(function(reason) { return { status: 'rejected', reason: reason }; });
            })
          );
        };
      }
    <\/script>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    `+e+`
  </head>
  <body data-sveltekit-preload-data="hover">
    <div id="svelte-app" style="display: contents">`+t+`</div>
  </body>
</html>
`,error:({status:e,message:t})=>`<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<title>`+t+`</title>

		<style>
			body {
				--bg: white;
				--fg: #222;
				--divider: #ccc;
				background: var(--bg);
				color: var(--fg);
				font-family:
					system-ui,
					-apple-system,
					BlinkMacSystemFont,
					'Segoe UI',
					Roboto,
					Oxygen,
					Ubuntu,
					Cantarell,
					'Open Sans',
					'Helvetica Neue',
					sans-serif;
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100vh;
				margin: 0;
			}

			.error {
				display: flex;
				align-items: center;
				max-width: 32rem;
				margin: 0 1rem;
			}

			.status {
				font-weight: 200;
				font-size: 3rem;
				line-height: 1;
				position: relative;
				top: -0.05rem;
			}

			.message {
				border-left: 1px solid var(--divider);
				padding: 0 0 0 1rem;
				margin: 0 0 0 1rem;
				min-height: 2.5rem;
				display: flex;
				align-items: center;
			}

			.message h1 {
				font-weight: 400;
				font-size: 1em;
				margin: 0;
			}

			@media (prefers-color-scheme: dark) {
				body {
					--bg: #222;
					--fg: #ddd;
					--divider: #666;
				}
			}
		</style>
	</head>
	<body>
		<div class="error">
			<span class="status">`+e+`</span>
			<div class="message">
				<h1>`+t+`</h1>
			</div>
		</div>
	</body>
</html>
`},version_hash:"1uoxw3b"},rn=async function(){return{handle:void 0,handleFetch:void 0,handleError:void 0,init:void 0,reroute:void 0,transport:void 0}};const Vr={get({type:e},t){throw new Error(`Cannot read values from $env/dynamic/${e} while prerendering (attempted to read env.${t.toString()}). Use $env/static/${e} instead`)}};let $o;ss=class{#e;#t;constructor(e){this.#e=nn,this.#t=e}async init({env:e,read:t}){const n={public_prefix:this.#e.env_public_prefix,private_prefix:this.#e.env_private_prefix},r=(function(s,{public_prefix:o,private_prefix:a}){Object.fromEntries(Object.entries(s).filter(([c])=>c.startsWith(a)&&(o===""||!c.startsWith(o))))}(e,n),function(s,{public_prefix:o,private_prefix:a}){return Object.fromEntries(Object.entries(s).filter(([c])=>c.startsWith(o)&&(a===""||!c.startsWith(a))))}(e,n));Yt&&new Proxy({type:"private"},Vr),Zt(Yt?new Proxy({type:"public"},Vr):r),tn(r),t&&en(t),await($o??=(async()=>{try{const s=await rn();this.#e.hooks={handle:s.handle||(({event:o,resolve:a})=>a(o)),handleError:s.handleError||(({error:o})=>{}),handleFetch:s.handleFetch||(({request:o,fetch:a})=>a(o)),reroute:s.reroute||(()=>{}),transport:s.transport||{}},s.init&&await s.init()}catch(s){throw s}})())}async respond(e,t){return Mr(e,this.#e,this.#t,{...t,error:!1,depth:0})}};function xo(){const{set:e,subscribe:t}=Ne(!1);return{subscribe:t,check:async()=>!1}}function Kr(e,t=JSON.parse){try{return t(sessionStorage[e])}catch{}}(we.toString().includes("$$")||/function \w+\(\) \{\}/.test(we.toString()))&&new URL("https://example.com"),Kr("sveltekit:scroll"),Kr("sveltekit:snapshot");function Xr(){return function(e){return Fr().get(e)}("__request__")}xo().check;const Yr={get error(){return Xr().page.error},get status(){return Xr().page.status}};rs=function(e,t){Ge(),e.out+=`<h1>${je(Yr.status)}</h1> <p>${je(Yr.error?.message)}</p>`,Ve()}})();export{es as A,ts as B,ns as C,Zt as D,en as E,tn as F,rs as G,ss as S,ko as __tla,os as a,je as b,as as c,is as d,cs as e,Ge as f,ls as g,us as h,fs as i,ds as j,ps as k,hs as l,vs as m,ms as n,gs as o,Ve as p,ys as q,ws as r,_s as s,bs as t,$s as u,nn as v,Ne as w,rn as x,xs as y,ks as z};
