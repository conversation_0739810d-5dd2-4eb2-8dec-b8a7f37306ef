import{r as i,k as l,p as x,f as c,b as u}from"./vendor_svelte.js";import{e as d}from"./lib.js";function v(e,n){c();let t=null,o=!1;function s(r){o=!0,t=r.error||r.reason||new Error("\u53D1\u751F\u672A\u77E5\u9519\u8BEF"),d(t),r.preventDefault()}function a(r){o=!0,t=r.reason||new Error("\u53D1\u751F\u672A\u5904\u7406\u7684Promise\u9519\u8BEF"),d(t),r.preventDefault()}i(()=>{typeof window<"u"&&(window.removeEventListener("error",s),window.removeEventListener("unhandledrejection",a))}),o?(e.out+="<!--[-->",e.out+=`<div class="error-boundary p-6 bg-red-50 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-lg shadow-md"><div class="flex items-center mb-4"><div class="flex-shrink-0 text-red-600 dark:text-red-400"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg></div> <h3 class="ml-2 text-lg font-semibold text-red-800 dark:text-red-200">\u53D1\u751F\u9519\u8BEF</h3></div> <div class="mb-4 text-red-700 dark:text-red-300">${u(t?.message||"\u672A\u77E5\u9519\u8BEF")}</div> <button class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors">\u91CD\u8BD5</button></div>`):(e.out+="<!--[!-->",e.out+="<!---->",l(e,n,"default",{}),e.out+="<!---->"),e.out+="<!--]-->",x()}export{v as E};
