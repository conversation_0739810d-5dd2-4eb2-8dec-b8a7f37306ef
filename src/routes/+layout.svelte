<script>
  import '../app.css';
  import { onMount } from 'svelte';
  import ErrorBoundary from '../components/ErrorBoundary.svelte';
  import NotificationManager from '../components/Notification/NotificationManager.svelte';
  import DrawerNavigation from '../components/Drawer/DrawerNavigation.svelte';
  import { selectedMenuItem } from '../lib/menuStore';
  import { getCurrentUserInfo, isCurrentUserAdmin } from '../services/userService';
  import { setUserInfo, clearCurrentUser } from '../lib/userStore';
  // 处理菜单选择事件
  function handleMenuSelect(event) {
    console.log('Layout接收到菜单选择事件:', event.detail);
    // 在这里不需要额外处理，因为MenuList组件已经更新了selectedMenuItem
    // 这里只记录日志用于调试
  }
  
  // 加载字体图标并初始化用户信息
  onMount(async () => {
    // 加载字体图标
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
    document.head.appendChild(link);
    
    // 获取用户信息并保存
    try {
      // 获取当前用户信息
      const userInfo = await getCurrentUserInfo();
      console.log('当前用户信息:', userInfo);
      
      // 设置全局用户信息
      if (userInfo && userInfo.uid) {
        // 更新Svelte store
        setUserInfo(userInfo);
        
        // 同时设置全局变量方便非Svelte代码访问
        window.userInfo = {
          ...userInfo,
          isAdmin: await isCurrentUserAdmin()
        };
      } else {
        // 用户未登录，清除用户信息
        clearCurrentUser();
        window.userInfo = null;
        console.info('用户未登录');
      }
    } catch (error) {
      console.error('初始化用户信息失败:', error);
      // 出错时也清除用户信息
      clearCurrentUser();
      window.userInfo = null;
    }
  });
</script>

<svelte:head>
  <title>小状元学习机</title>
</svelte:head>

<ErrorBoundary>
  <slot />
</ErrorBoundary>

<!-- 全局抽屉导航 -->
<DrawerNavigation on:select={handleMenuSelect} />

<!-- 全局通知管理器 -->
<NotificationManager />

<style>
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }
  
  :global(.dark) {
    color-scheme: dark;
  }
  
  :global(.aspect-w-16) {
    position: relative;
    padding-bottom: 56.25%;
  }
  
  :global(.aspect-h-9) {
    position: relative;
  }
  
  :global(.aspect-w-16 > *), :global(.aspect-h-9 > *) {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  
  :global(.aspect-square) {
    position: relative;
    padding-bottom: 100%;
  }
  
  :global(.aspect-square > *) {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
</style> 