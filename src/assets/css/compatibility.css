/**
 * 兼容性相关的CSS样式
 * 针对老版本浏览器的样式优化
 */

/* 减少动画的样式 - 针对性能较差的设备 */
.reduce-animations *,
.reduce-animations *::before,
.reduce-animations *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* 简化UI样式 - 针对老版本浏览器 */
.simplified-ui {
  /* 使用更简单的字体栈 */
  font-family: Arial, sans-serif;
}

.simplified-ui .gradient-bg {
  /* 简化渐变背景为纯色 */
  background: #3b82f6 !important;
}

.simplified-ui .shadow-lg {
  /* 简化阴影效果 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.simplified-ui .backdrop-blur {
  /* 移除模糊效果 */
  backdrop-filter: none !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
}

/* Chrome 78 特殊兼容样式 */
.chrome-78-compat {
  /* 避免使用 CSS Grid 的复杂特性 */
  display: block;
}

.chrome-78-compat .grid {
  display: flex;
  flex-wrap: wrap;
}

.chrome-78-compat .grid > * {
  flex: 1;
  min-width: 200px;
}

/* 华为平板特殊优化 */
.huawei-tablet-compat {
  /* 优化触摸响应 */
  touch-action: manipulation;
}

.huawei-tablet-compat button,
.huawei-tablet-compat .clickable {
  /* 增大点击区域 */
  min-height: 44px;
  min-width: 44px;
}

.huawei-tablet-compat input,
.huawei-tablet-compat textarea {
  /* 优化输入框样式 */
  font-size: 16px; /* 防止缩放 */
  border-radius: 4px;
}

/* 老版本浏览器的Flexbox兼容 */
.legacy-flexbox {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.legacy-flex-center {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

/* 兼容性媒体查询 */
@media screen and (max-width: 768px) {
  .simplified-ui .complex-layout {
    /* 在小屏幕上简化布局 */
    display: block !important;
  }
  
  .simplified-ui .multi-column {
    /* 单列布局 */
    column-count: 1 !important;
  }
}

/* 针对WebView的特殊处理 */
.webview-compat {
  /* 避免使用position: sticky */
  position: relative !important;
}

.webview-compat .sticky {
  position: relative !important;
  top: auto !important;
}

/* 语音识别组件的兼容性样式 */
.asr-compat .recording-animation {
  /* 简化录音动画 */
  animation: simple-pulse 1s infinite;
}

@keyframes simple-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 二维码扫描器的兼容性样式 */
.qr-scanner-compat .scan-line {
  /* 简化扫描线动画 */
  animation: simple-scan 2s linear infinite;
}

@keyframes simple-scan {
  0% { top: 10%; }
  50% { top: 90%; }
  100% { top: 10%; }
}

/* 图片上传组件的兼容性样式 */
.image-upload-compat .progress-bar {
  /* 简化进度条样式 */
  background: linear-gradient(to right, #22c55e 0%, #22c55e var(--progress, 0%), #e5e7eb var(--progress, 0%), #e5e7eb 100%);
}

/* 文本编辑器的兼容性样式 */
.text-editor-compat .rich-text {
  /* 简化富文本编辑器 */
  border: 1px solid #d1d5db;
  padding: 0.75rem;
  min-height: 120px;
}

.text-editor-compat .toolbar {
  /* 简化工具栏 */
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

/* 通知组件的兼容性样式 */
.notification-compat {
  /* 确保通知在老版本浏览器中正确显示 */
  position: fixed;
  z-index: 9999;
  pointer-events: auto;
}

/* 模态框的兼容性样式 */
.modal-compat {
  /* 老版本浏览器的模态框样式 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-compat .modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

/* 打印样式的兼容性 */
@media print {
  .compatibility-warning,
  .notification-compat,
  .modal-compat {
    display: none !important;
  }
}