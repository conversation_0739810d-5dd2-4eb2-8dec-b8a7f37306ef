/**
 * 浏览器兼容性配置
 * 针对不同浏览器版本的特殊处理
 */

// 检测用户代理信息
export function detectUserAgent() {
  const ua = navigator.userAgent;
  
  return {
    isHuaweiTablet: ua.includes('BZC-W00'),
    isOldChrome: /Chrome\/([0-9]+)/.test(ua) && parseInt(RegExp.$1) < 80,
    chromeVersion: /Chrome\/([0-9]+)/.test(ua) ? parseInt(RegExp.$1) : null,
    isAndroid: ua.includes('Android'),
    androidVersion: /Android ([0-9.]+)/.test(ua) ? RegExp.$1 : null,
    isWebView: ua.includes('wv'),
    userAgent: ua
  };
}

// 兼容性配置
export const compatibilityConfig = {
  // 华为平板 Chrome 78 特殊配置
  huaweiTabletChrome78: {
    // 禁用的功能
    disabledFeatures: [
      'advancedWebRTC',
      'modernJavaScript',
      'complexAnimations'
    ],
    
    // 启用的polyfills
    enabledPolyfills: [
      'urlSearchParams',
      'promiseAllSettled',
      'stringReplaceAll',
      'arrayAt'
    ],
    
    // 特殊处理选项
    options: {
      // 降低动画复杂度
      reduceAnimations: true,
      // 增加错误容错
      enhancedErrorHandling: true,
      // 使用简化的UI组件
      useSimplifiedUI: true,
      // 禁用某些实验性API
      disableExperimentalAPIs: true
    }
  },
  
  // 通用老版本浏览器配置
  legacyBrowser: {
    disabledFeatures: [
      'webWorkers',
      'serviceWorker',
      'pushNotifications'
    ],
    
    enabledPolyfills: [
      'fetch',
      'promise',
      'objectAssign'
    ],
    
    options: {
      usePolyfills: true,
      fallbackToBasicFeatures: true
    }
  }
};

// 获取当前环境的兼容性配置
export function getCurrentCompatibilityConfig() {
  const userAgent = detectUserAgent();
  
  // 华为平板 Chrome 78 特殊处理
  if (userAgent.isHuaweiTablet && userAgent.chromeVersion === 78) {
    console.log('应用华为平板 Chrome 78 兼容性配置');
    return {
      ...compatibilityConfig.huaweiTabletChrome78,
      userAgent
    };
  }
  
  // 其他老版本浏览器
  if (userAgent.isOldChrome || (userAgent.chromeVersion && userAgent.chromeVersion < 80)) {
    console.log('应用老版本浏览器兼容性配置');
    return {
      ...compatibilityConfig.legacyBrowser,
      userAgent
    };
  }
  
  // 现代浏览器，无需特殊配置
  return {
    userAgent,
    disabledFeatures: [],
    enabledPolyfills: [],
    options: {}
  };
}

// 应用兼容性配置
export function applyCompatibilityConfig() {
  const config = getCurrentCompatibilityConfig();
  
  // 设置全局兼容性标志
  window.__COMPATIBILITY_CONFIG__ = config;
  
  // 根据配置禁用某些功能
  if (config.disabledFeatures) {
    config.disabledFeatures.forEach(feature => {
      switch (feature) {
        case 'advancedWebRTC':
          // 禁用高级WebRTC功能
          if (window.RTCPeerConnection) {
            window.__WEBRTC_DISABLED__ = true;
          }
          break;
          
        case 'modernJavaScript':
          // 标记需要使用传统JavaScript语法
          window.__USE_LEGACY_JS__ = true;
          break;
          
        case 'complexAnimations':
          // 禁用复杂动画
          document.documentElement.classList.add('reduce-animations');
          break;
          
        case 'webWorkers':
          // 禁用Web Workers
          if (window.Worker) {
            window.__WORKERS_DISABLED__ = true;
          }
          break;
      }
    });
  }
  
  // 应用特殊选项
  if (config.options) {
    if (config.options.reduceAnimations) {
      document.documentElement.classList.add('reduce-animations');
    }
    
    if (config.options.enhancedErrorHandling) {
      setupEnhancedErrorHandling();
    }
    
    if (config.options.useSimplifiedUI) {
      document.documentElement.classList.add('simplified-ui');
    }
  }
  
  return config;
}

// 增强错误处理
function setupEnhancedErrorHandling() {
  // 全局错误捕获
  window.addEventListener('error', (event) => {
    console.error('兼容性错误捕获:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    });
    
    // 防止某些错误导致应用完全崩溃
    if (event.error && event.error.name === 'SyntaxError') {
      event.preventDefault();
      console.warn('语法错误已被兼容性处理器捕获');
    }
  });
  
  // Promise错误捕获
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise错误:', event.reason);
    
    // 防止Promise错误导致应用崩溃
    event.preventDefault();
  });
}

// 检查功能是否被禁用
export function isFeatureDisabled(featureName) {
  const config = window.__COMPATIBILITY_CONFIG__;
  return config && config.disabledFeatures && config.disabledFeatures.includes(featureName);
}

// 检查是否需要使用polyfill
export function needsPolyfill(polyfillName) {
  const config = window.__COMPATIBILITY_CONFIG__;
  return config && config.enabledPolyfills && config.enabledPolyfills.includes(polyfillName);
}