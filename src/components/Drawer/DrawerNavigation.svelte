<script>
  import { menuItems, isDrawerOpen, partnerName } from '../../lib/menuStore';
  import { isDarkMode } from '../../lib/store';
  import { isAdmin, user } from '../../lib/userStore';
  import { onMount } from 'svelte';
  import MenuList from './MenuList.svelte';
  
  export let title = "赶考小状学习机功能模块介绍";

  // 动态计算标题
  $: displayTitle = $partnerName ? `[${$partnerName}]功能模块介绍` : title;
  
  // 记录菜单展开状态
  let expandedMenuState = {};
  
  // 搜索功能
  let searchTerm = "";
  let filteredMenuItems = [];
  
  // 监听搜索词变化，过滤菜单
  $: {
    if (searchTerm.trim() === "") {
      filteredMenuItems = $menuItems;
    } else {
      filteredMenuItems = filterMenuItems($menuItems, searchTerm.toLowerCase());
    }
  }
  
  // 递归过滤菜单项
  function filterMenuItems(items, term) {
    if (!items) return [];
    
    return items
      .map(item => {
        // 检查当前项是否匹配
        const currentMatches = item.name && item.name.toLowerCase().includes(term);
        
        // 处理子项
        let filteredChildren = [];
        if (item.children && item.children.length > 0) {
          filteredChildren = filterMenuItems(item.children, term);
        }
        
        // 如果当前项匹配或者子项中有匹配，则保留此项
        if (currentMatches || filteredChildren.length > 0) {
          // 如果有匹配的子项，则自动展开此父项
          if (filteredChildren.length > 0 && term !== "") {
            expandedMenuState[item.id] = true;
          }
          
          // 返回新对象，包含原项和过滤后的子项
          return {
            ...item,
            children: filteredChildren.length > 0 ? filteredChildren : item.children
          };
        }
        
        // 不匹配则返回null
        return null;
      })
      .filter(Boolean); // 过滤掉null值
  }
  
  // 清空搜索
  function clearSearch() {
    searchTerm = "";
  }
  
  function closeDrawer() {
    $isDrawerOpen = false;
  }
  
  // 跳转到登录页面
  function goToLogin() {
    // 获取当前应用URL
    const currentUrl = window.location.href;
    // 构建登录URL，带上重定向参数
    const loginUrl = `https://www.gankao.com/user/login?redirect=${encodeURIComponent(currentUrl)}`;
    // 跳转到登录页面
    window.location.href = loginUrl;
  }
  
  // 展开所有菜单
  function expandAll() {
    const expandAllItems = (items) => {
      items.forEach(item => {
        if (item.id) {
          expandedMenuState[item.id] = true;
        }
        if (item.children && item.children.length > 0) {
          expandAllItems(item.children);
        }
      });
    };
    
    expandAllItems($menuItems);
    expandedMenuState = {...expandedMenuState}; // 触发更新
  }
  
  // 折叠所有菜单
  function collapseAll() {
    expandedMenuState = {};
  }
  
  // 处理子组件状态变化
  function handleStateChange(event) {
    expandedMenuState = event.detail;
  }
  
  // 保存菜单展开状态到本地存储
  function saveMenuState() {
    try {
      localStorage.setItem('menu-expanded-state', JSON.stringify(expandedMenuState));
    } catch (e) {
      console.error('保存菜单状态失败:', e);
    }
  }
  
  // 从本地存储加载菜单展开状态
  function loadMenuState() {
    try {
      const savedState = localStorage.getItem('menu-expanded-state');
      if (savedState) {
        expandedMenuState = JSON.parse(savedState);
      }
    } catch (e) {
      console.error('加载菜单状态失败:', e);
    }
  }
  
  onMount(() => {
    loadMenuState();
    
    // 监听页面卸载事件，保存菜单状态
    window.addEventListener('beforeunload', saveMenuState);
    
    return () => {
      window.removeEventListener('beforeunload', saveMenuState);
    };
  });
</script>

<div 
  class="fixed inset-y-0 left-0 w-4/5 max-w-xs transform {$isDrawerOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out z-50 flex flex-col"
>
  <div class="flex-1 flex flex-col bg-gradient-to-b from-blue-800 to-blue-900 dark:from-gray-800 dark:to-gray-900 shadow-lg overflow-hidden h-full">
    <div class="flex items-center justify-between p-4 border-b border-blue-700 dark:border-gray-700">
      <div class="flex items-center">
        <div class="text-xl font-bold text-white">{displayTitle}</div>
      </div>
      <button 
        on:click={closeDrawer}
        class="p-2 rounded-full text-white hover:bg-blue-700 dark:hover:bg-gray-700 transition-colors"
        aria-label="关闭导航菜单"
      >
        <i class="fas fa-arrow-left"></i>
      </button>
    </div>
    
    <!-- 搜索框 -->
    <div class="px-3 py-2 border-b border-blue-700 dark:border-gray-700">
      <div class="relative w-full">
        <input
          type="text"
          bind:value={searchTerm}
          placeholder="搜索目录..."
          class="w-full py-1 px-3 pr-8 bg-blue-700 dark:bg-gray-700 text-white rounded-md placeholder-blue-300 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-400 dark:focus:ring-gray-400 text-sm"
        />
        {#if searchTerm}
          <button 
            on:click={clearSearch}
            class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-300 dark:text-gray-400 hover:text-white"
            aria-label="清空搜索"
          >
            <i class="fas fa-times text-xs"></i>
          </button>
        {:else}
          <div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-300 dark:text-gray-400">
            <i class="fas fa-search text-xs"></i>
          </div>
        {/if}
      </div>
    </div>
    
    <!-- 添加展开/折叠全部按钮 -->
    <div class="flex justify-between px-3 py-2 border-b border-blue-700 dark:border-gray-700">
      <button 
        on:click={expandAll}
        class="text-xs text-white opacity-80 hover:opacity-100 transition-opacity"
        aria-label="展开所有菜单项"
      >
        展开全部
      </button>
      <button 
        on:click={collapseAll}
        class="text-xs text-white opacity-80 hover:opacity-100 transition-opacity"
        aria-label="折叠所有菜单项"
      >
        折叠全部
      </button>
    </div>
    
    <div class="flex-1 overflow-y-auto py-4 px-3 pb-0 drawer-content">
      <!-- 显示过滤后的菜单项 -->
      <MenuList 
        items={filteredMenuItems} 
        bind:expandedState={expandedMenuState}
        on:select
      />
      <!-- 底部留空和管理员标识 -->
      <div class="h-4"></div>
    </div>
  </div>
  
  <!-- 管理员标识或登录按钮 -->
  <div class="bg-gradient-to-b from-blue-700 to-blue-900 dark:from-gray-700 dark:to-gray-900 py-2 border-t border-blue-700 dark:border-gray-700 shadow-inner w-full">
    <div class="flex justify-center">
      {#if $user.uid}
        <span class="px-3 py-1 text-sm {$isAdmin ? 'bg-red-500' : 'bg-blue-500'} text-white rounded-full shadow-md flex items-center">
          <i class="fas {$isAdmin ? 'fa-shield-alt' : 'fa-user'} mr-1"></i>
          {$user.nickname || "用户"}
        </span>
      {:else}
        <button 
          on:click={goToLogin}
          class="px-3 py-1 text-sm bg-green-500 text-white rounded-full shadow-md flex items-center hover:bg-green-600 transition-colors"
          aria-label="跳转到登录页面"
        >
          <i class="fas fa-sign-in-alt mr-1"></i>
          登录
        </button>
      {/if}
    </div>
  </div>
</div>

<!-- Backdrop overlay -->
{#if $isDrawerOpen}
  <div 
    class="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300" 
    on:click={closeDrawer}
  ></div>
{/if}

<style>
  .drawer-content {
    /* 自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    overflow-y: auto;
    height: 100%;
  }
  
  .drawer-content::-webkit-scrollbar {
    width: 4px;
  }
  
  .drawer-content::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .drawer-content::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
</style> 