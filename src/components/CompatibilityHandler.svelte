<script>
  import { onMount } from 'svelte';
  import { addNotification } from '$lib/store';
  import { checkCompatibility } from '../utils/polyfills.js';
  import { applyCompatibilityConfig, detectUserAgent } from '../config/compatibility.js';
  
  let compatibilityIssues = [];
  let showCompatibilityWarning = false;
  
  onMount(async () => {
    // 应用兼容性配置
    const config = applyCompatibilityConfig();
    const userAgent = detectUserAgent();
    
    // 动态导入polyfills
    try {
      await import('../utils/polyfills.js');
      console.log('Polyfills 加载成功');
    } catch (error) {
      console.error('Polyfills 加载失败:', error);
    }
    
    // 执行兼容性检查
    compatibilityIssues = checkCompatibility();
    
    if (compatibilityIssues.length > 0) {
      showCompatibilityWarning = true;
      
      // 显示兼容性警告通知
      addNotification(
        `检测到 ${compatibilityIssues.length} 个兼容性问题，部分功能可能受限`,
        'warning',
        5000
      );
      
      // 在控制台输出详细信息
      console.warn('浏览器兼容性问题详情:', compatibilityIssues);
    }
    
    // 特殊处理：华为平板 Chrome 78
    if (userAgent.isHuaweiTablet && userAgent.chromeVersion === 78) {
      console.log('检测到华为平板 Chrome 78 WebView，启用特殊兼容模式');
      enableHuaweiTabletCompatibility();
    }
  });
  
  function enableHuaweiTabletCompatibility() {
    // 针对华为平板 Chrome 78 的特殊处理
    
    // 1. 禁用某些可能有问题的特性
    window.__COMPATIBILITY_MODE__ = {
      disableAdvancedFeatures: true,
      usePolyfills: true,
      userAgent: 'huawei-tablet-chrome78'
    };
    
    // 2. 增强错误处理
    window.addEventListener('error', (event) => {
      console.error('兼容性错误捕获:', event.error);
      
      // 如果是语法错误，可能是ES6+语法不兼容
      if (event.error && event.error.name === 'SyntaxError') {
        addNotification('检测到语法兼容性问题，请尝试刷新页面', 'error');
      }
    });
    
    // 3. 增强Promise错误处理
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise错误:', event.reason);
      
      // 防止某些Promise错误导致应用崩溃
      event.preventDefault();
    });
    
    // 4. 媒体设备API兼容性增强
    if (navigator.mediaDevices && !navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices.getUserMedia = function(constraints) {
        const getUserMedia = navigator.webkitGetUserMedia || 
                           navigator.mozGetUserMedia || 
                           navigator.msGetUserMedia;
        
        if (!getUserMedia) {
          return Promise.reject(new Error('getUserMedia 不支持'));
        }
        
        return new Promise((resolve, reject) => {
          getUserMedia.call(navigator, constraints, resolve, reject);
        });
      };
    }
  }
  
  function dismissWarning() {
    showCompatibilityWarning = false;
  }
  
  function showDetails() {
    const details = compatibilityIssues.join('\n• ');
    alert(`兼容性问题详情：\n\n• ${details}\n\n建议使用更新版本的浏览器以获得最佳体验。`);
  }
</script>

{#if showCompatibilityWarning}
  <div class="compatibility-warning">
    <div class="warning-content">
      <div class="warning-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <div class="warning-text">
        <h4>浏览器兼容性提示</h4>
        <p>检测到您的浏览器版本较旧，部分功能可能无法正常使用。</p>
        <div class="warning-actions">
          <button class="btn-details" on:click={showDetails}>
            查看详情
          </button>
          <button class="btn-dismiss" on:click={dismissWarning}>
            我知道了
          </button>
        </div>
      </div>
      <button class="close-btn" on:click={dismissWarning}>
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>
{/if}

<style>
  .compatibility-warning {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    background-color: #fbbf24;
    color: #92400e;
    padding: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .warning-content {
    display: flex;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    gap: 0.75rem;
  }
  
  .warning-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
  }
  
  .warning-text {
    flex: 1;
  }
  
  .warning-text h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    font-weight: 600;
  }
  
  .warning-text p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.9;
  }
  
  .warning-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }
  
  .btn-details,
  .btn-dismiss {
    padding: 0.25rem 0.75rem;
    border: 1px solid #92400e;
    background: transparent;
    color: #92400e;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-details:hover,
  .btn-dismiss:hover {
    background-color: #92400e;
    color: #fbbf24;
  }
  
  .close-btn {
    background: none;
    border: none;
    color: #92400e;
    font-size: 1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  
  .close-btn:hover {
    background-color: rgba(146, 64, 14, 0.1);
  }
  
  @media (max-width: 640px) {
    .warning-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    
    .close-btn {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
    }
    
    .warning-actions {
      flex-direction: column;
      width: 100%;
    }
    
    .btn-details,
    .btn-dismiss {
      width: 100%;
      text-align: center;
    }
  }
</style>