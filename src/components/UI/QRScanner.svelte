<script>
  import { onMount, onDestroy, createEventDispatcher } from 'svelte';
  import jsQR from 'jsqr';
  import { addNotification } from '$lib/store';
  
  const dispatch = createEventDispatcher();
  
  // 扫描相关变量
  export let showScanner = false;
  
  // 二维码扫描相关变量
  let videoElement;
  let canvasElement;
  let scannerLoading = false;
  let scanInterval; // 保存扫描定时器的引用
  let jsQRReady = false; // jsQR库是否准备就绪
  let canvasContext = null; // 保存canvas上下文
  let scanFailCount = 0; // 扫描失败次数计数
  const MAX_SCAN_RETRY = 3; // 最大扫描重试次数
  
  onMount(() => {
    // 预加载jsQR库
    jsQRReady = true;
    
    // 初始化相机
    if (showScanner) {
      setTimeout(() => {
        initCamera();
      }, 100);
    }
  });
  
  onDestroy(() => {
    // 确保相机资源被释放
    safeStopVideoStream();
  });
  
  // 监听showScanner变化
  $: if (showScanner) {
    setTimeout(() => {
      initCamera();
    }, 100);
  }
  
  // 安全停止视频流
  function safeStopVideoStream() {
    try {
      // 更新加载状态
      scannerLoading = false;
      
      if (videoElement) {
        if (videoElement.srcObject) {
          // 停止所有轨道
          const tracks = videoElement.srcObject.getTracks();
          if (tracks && tracks.length) {
            tracks.forEach(track => {
              try {
                if (track.readyState === 'live') {
                  track.stop();
                }
              } catch (e) {
                console.error('停止视频轨道出错:', e);
              }
            });
          }
          // 清除srcObject引用
          videoElement.srcObject = null;
        }
        // 移除事件监听器
        videoElement.onloadedmetadata = null;
        videoElement.onplay = null;
        videoElement.onerror = null;
      }
      
      // 清除扫描定时器
      if (scanInterval) {
        clearInterval(scanInterval);
        scanInterval = null;
      }
      
      // 重置扫描错误计数
      scanFailCount = 0;
    } catch (e) {
      console.error('安全停止视频流出错:', e);
    }
  }

  // 初始化相机并扫描二维码
  async function initCamera() {
    if (!videoElement || !canvasElement) return;
    
    try {
      // 检查jsQR是否准备就绪
      if (!jsQRReady) {
        console.error('jsQR库未准备就绪');
        addNotification('扫描组件未准备就绪，请刷新页面重试', 'error');
        closeScanner();
        return;
      }
      
      canvasContext = canvasElement.getContext('2d', { willReadFrequently: true });
      
      // 请求相机权限，设置较高的分辨率以提高识别率
      const constraints = {
        video: {
          facingMode: 'environment', // 优先使用后置相机
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 15 }
        }
      };
      
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      
      // 检查扫描器是否已关闭
      if (!showScanner) {
        // 如果扫描器已经关闭，立即释放相机资源
        stream.getTracks().forEach(track => track.stop());
        scannerLoading = false;
        return;
      }
      
      // 设置视频源并处理播放错误
      videoElement.srcObject = stream;
      try {
        await videoElement.play();
        // 视频开始播放，更新加载状态
        scannerLoading = false;
      } catch (playError) {
        // 如果播放被中断（例如界面已关闭），只记录错误而不提示用户
        console.log('视频播放被中断:', playError);
        scannerLoading = false;
        if (!showScanner) {
          safeStopVideoStream();
          return;
        }
      }
      
      // 再次检查扫描器是否已关闭
      if (!showScanner) {
        safeStopVideoStream();
        scannerLoading = false;
        return;
      }
      
      // 使用更高的扫描频率 (100ms)提高响应速度
      scanInterval = setInterval(() => {
        if (!showScanner) {
          safeStopVideoStream();
          scannerLoading = false;
          return;
        }
        
        if (videoElement.readyState === videoElement.HAVE_ENOUGH_DATA) {
          // 使canvas尺寸与视频匹配
          canvasElement.width = videoElement.videoWidth;
          canvasElement.height = videoElement.videoHeight;
          
          // 绘制视频帧到canvas
          canvasContext.drawImage(
            videoElement, 
            0, 0, 
            canvasElement.width, 
            canvasElement.height
          );
          
          // 获取图像数据
          const imageData = canvasContext.getImageData(
            0, 0, 
            canvasElement.width, 
            canvasElement.height
          );
          
          // 使用jsQR进行识别
          const code = jsQR(
            imageData.data, 
            imageData.width, 
            imageData.height, 
            {
              inversionAttempts: "dontInvert", // 加快处理速度
            }
          );
          
          // 处理识别结果
          if (code) {
            console.log("找到二维码!", code);
            
            // 停止扫描
            safeStopVideoStream();
            closeScanner();
            
            // 处理扫描结果
            handleScanResult(code.data);
          }
        }
      }, 100); // 每100ms扫描一次，提高响应速度
      
    } catch (error) {
      console.error('相机初始化失败:', error);
      addNotification('无法访问相机，请检查权限设置', 'error');
      closeScanner();
    }
  }

  // 处理扫描结果
  function handleScanResult(result) {
    try {
      console.log('扫描结果:', result);
      
      // 尝试解析为JSON
      let parsedData = null;
      try {
        parsedData = JSON.parse(result);
      } catch (e) {
        // 不是JSON格式，继续使用字符串处理
      }
      
      // 如果是JSON对象且包含所需字段
      if (parsedData && parsedData.uuid && parsedData.username) {
        dispatch('scan', {
          uuid: parsedData.uuid,
          username: parsedData.username
        });
        return;
      }
      
      // 尝试解析特定格式 "scan:{uuid}:{username}"
      if (result.startsWith('scan:')) {
        const parts = result.split(':');
        if (parts.length >= 3) {
          dispatch('scan', {
            uuid: parts[1],
            username: parts[2]
          });
          return;
        }
      }
      
      // 如果是URL格式
      if (result.startsWith('http')) {
        try {
          const url = new URL(result);
          const uuidParam = url.searchParams.get('uuid');
          const nameParam = url.searchParams.get('name') || url.searchParams.get('username');
          
          if (uuidParam) {
            dispatch('scan', {
              uuid: uuidParam,
              username: nameParam || "未知用户"
            });
            return;
          }
        } catch (e) {
          // URL解析失败，继续尝试其他格式
        }
      }
      
      // 其他格式：尝试直接使用作为UUID
      if (result && result.length > 5) {
        dispatch('scan', {
          uuid: result.trim(),
          username: "扫描用户"
        });
        return;
      }
      
      // 无法识别的格式，这里也属于扫描错误
      scanFailCount++;
      console.log(`二维码识别失败，重试次数: ${scanFailCount}/${MAX_SCAN_RETRY}`);
      
      if (scanFailCount >= MAX_SCAN_RETRY) {
        // 三次失败后显示提示
        addNotification('无法识别的二维码内容，请确认二维码格式正确', 'error');
        scanFailCount = 0; // 重置失败计数
        
        // 关闭扫描器
        closeScanner();
      } else {
        // 未达到最大重试次数，重新开始扫描
        console.log('重新尝试扫描二维码...');
        
        // 继续扫描，不关闭扫描器
        if (!scanInterval && videoElement && canvasElement) {
          // 重新启动扫描
          initCamera();
        }
      }
    } catch (error) {
      console.error('处理扫描结果失败:', error);
      
      // 将异常也视为扫描错误，增加失败计数
      scanFailCount++;
      console.log(`扫描处理异常，重试次数: ${scanFailCount}/${MAX_SCAN_RETRY}`);
      
      if (scanFailCount >= MAX_SCAN_RETRY) {
        // 三次失败后关闭扫描器并显示提示
        addNotification('处理二维码失败: ' + error.message, 'error');
        closeScanner(); // 关闭扫描器
        scanFailCount = 0; // 重置失败计数
      } else {
        // 未达到最大重试次数，重新开始扫描
        console.log('重新尝试扫描二维码...');
        
        // 继续扫描，不关闭扫描器
        if (!scanInterval && videoElement && canvasElement) {
          // 启动相同的扫描逻辑，重新尝试
          initCamera();
        }
      }
    }
  }

  // 关闭二维码扫描器
  function closeScanner() {
    dispatch('close');
  }
</script>

{#if showScanner}
  <div class="qr-scanner-container">
    <div class="qr-scanner-header">
      <button class="qr-scanner-close" on:click={closeScanner}>
        <i class="fas fa-arrow-left"></i>
      </button>
      <h3 class="qr-scanner-title">扫描二维码</h3>
      <div class="qr-scanner-light">
        <button class="flash-toggle" on:click={() => addNotification('闪光灯功能暂未实现', 'info')}>
          <i class="fas fa-bolt"></i>
        </button>
      </div>
    </div>
    
    <div class="qr-scanner-body">
      {#if !jsQRReady}
        <div class="qr-scanner-loading">
          <div class="spinner"></div>
          <p>正在加载扫描组件...</p>
          <button class="retry-button" on:click={() => window.location.reload()}>
            刷新页面
          </button>
        </div>
      {:else}
        <div class="video-container">
          <video id="qr-video" bind:this={videoElement} playsinline autoplay muted></video>
          <div class="scan-region-highlight">
            <div class="corner top-left"></div>
            <div class="corner top-right"></div>
            <div class="corner bottom-left"></div>
            <div class="corner bottom-right"></div>
            <div class="scan-line"></div>
          </div>
          <canvas id="qr-canvas" bind:this={canvasElement} style="display:none;"></canvas>
          
          <!-- 相机权限加载状态 -->
          {#if scannerLoading}
            <div class="camera-loading">
              <div class="spinner"></div>
              <p>正在请求相机权限...</p>
            </div>
          {/if}
        </div>
        <div class="scan-instructions">
          <p>将二维码对准扫描框，支持多种格式</p>
          <div class="supported-formats">
            <span class="format-badge">URL链接</span>
            <span class="format-badge">JSON</span>
            <span class="format-badge">自定义文本</span>
          </div>
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  /* 二维码扫描器样式 */
  .qr-scanner-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #000;
    z-index: 1000;
    display: flex;
    flex-direction: column;
  }
  
  .qr-scanner-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    color: white;
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .qr-scanner-close, .flash-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }
  
  .qr-scanner-close:hover, .flash-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .qr-scanner-title {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
  }
  
  .qr-scanner-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
  }
  
  .qr-scanner-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    text-align: center;
    padding: 2rem;
  }
  
  .retry-button {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #22c55e;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
  }
  
  .retry-button:hover {
    background-color: #16a34a;
  }
  
  .spinner {
    width: 48px;
    height: 48px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  .video-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .camera-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 10;
  }
  
  .scan-region-highlight {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 70%;
    max-width: 300px;
    aspect-ratio: 1/1;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(34, 197, 94, 0.5);
    box-shadow: 0 0 0 4000px rgba(0, 0, 0, 0.5);
    border-radius: 10px;
  }
  
  .corner {
    position: absolute;
    width: 20px;
    height: 20px;
  }
  
  .top-left {
    top: -3px;
    left: -3px;
    border-top: 3px solid #22c55e;
    border-left: 3px solid #22c55e;
    border-top-left-radius: 7px;
  }
  
  .top-right {
    top: -3px;
    right: -3px;
    border-top: 3px solid #22c55e;
    border-right: 3px solid #22c55e;
    border-top-right-radius: 7px;
  }
  
  .bottom-left {
    bottom: -3px;
    left: -3px;
    border-bottom: 3px solid #22c55e;
    border-left: 3px solid #22c55e;
    border-bottom-left-radius: 7px;
  }
  
  .bottom-right {
    bottom: -3px;
    right: -3px;
    border-bottom: 3px solid #22c55e;
    border-right: 3px solid #22c55e;
    border-bottom-right-radius: 7px;
  }
  
  .scan-line {
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #22c55e;
    top: 50%;
    transform: translateY(-50%);
    animation: scanLine 2s linear infinite;
  }
  
  @keyframes scanLine {
    0% {
      top: 15%;
    }
    50% {
      top: 85%;
    }
    100% {
      top: 15%;
    }
  }
  
  .scan-instructions {
    position: absolute;
    bottom: 10%;
    left: 0;
    right: 0;
    text-align: center;
    color: white;
    padding: 1rem;
    font-size: 0.9rem;
    background-color: rgba(0, 0, 0, 0.6);
  }
  
  .supported-formats {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }
  
  .format-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.5);
    border-radius: 12px;
    font-size: 0.7rem;
  }
  
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style> 