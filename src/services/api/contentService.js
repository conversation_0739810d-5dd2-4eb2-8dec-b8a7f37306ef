import { get } from 'svelte/store';
import { leafNodes } from '../../lib/menuStore';
import { getAdjacentModules } from '../../utils/navigationUtils';
import { getModuleDetail, editModuleDetail } from './zoneModuleService';

/**
 * 将API返回的模块详情转换为内部使用格式
 * @param {Object} apiData - API返回的数据
 * @param {string} mcode - 模块代码
 * @returns {Object} 转换后的内容数据
 */
function convertApiContentFormat(apiData, mcode) {
  // 获取当前叶子节点列表和相邻模块
  const allLeafNodes = get(leafNodes);
  const { prev, next } = getAdjacentModules(mcode, allLeafNodes);
  
  // 提取图片
  const images = apiData.app_module_banners
    .filter(banner => banner.image) // 过滤掉没有图片的轮播项
    .map(banner => banner.image);
  
  return {
    mcode: mcode,
    description: apiData.content || "",
    images: images,
    moduleId: apiData.id,
    schema: apiData.schema,
    name: apiData.name,
    prevPage: prev,
    nextPage: next
  };
}

/**
 * 通过mcode获取内容
 * @param {string} mcode - 菜单代码
 * @returns {Promise<Object|null>} 内容数据对象或null
 */
export async function getContentByMcode(mcode) {
  if (!mcode) {
    return null;
  }

  // 接收partnerEName参数
  let partnerEName = null;

  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    partnerEName = urlParams.get('partnerEName') || null;
  }
  
  // 从API获取数据
  const apiData = await getModuleDetail(mcode, partnerEName);
  
  if (apiData) {
    // 转换API数据为内部格式
    return convertApiContentFormat(apiData, mcode);
  }
  
  return null;
}

/**
 * 保存内容
 * @param {string} mcode - 菜单代码
 * @param {Object} data - 要保存的内容数据
 * @returns {Promise<Object>} 保存结果
 */
export async function saveContent(mcode, data) {
  if (!data.moduleId) {
    throw new Error('保存失败：缺少模块ID');
  }
  
  // 将内部格式转换为API需要的格式
  const images = data.images.map(image => ({
    type: 1, // 默认是图片
    image: image
  }));
  
  // 调用API保存
  await editModuleDetail(data.moduleId, data.description, images);
  
  return {
    success: true,
    message: '保存成功',
    data: data
  };
} 