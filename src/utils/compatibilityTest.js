/**
 * 兼容性测试工具
 * 用于验证各种兼容性修复是否正常工作
 */

export class CompatibilityTester {
  constructor() {
    this.testResults = [];
  }

  // 运行所有兼容性测试
  async runAllTests() {
    console.log('开始运行兼容性测试...');
    
    this.testResults = [];
    
    // 基础API测试
    this.testURLSearchParams();
    this.testPromiseAllSettled();
    this.testStringReplaceAll();
    this.testArrayAt();
    
    // 媒体设备API测试
    await this.testMediaDevices();
    
    // WebSocket测试
    this.testWebSocket();
    
    // Fetch API测试
    await this.testFetchAPI();
    
    // 输出测试结果
    this.outputResults();
    
    return this.testResults;
  }

  // 测试URLSearchParams
  testURLSearchParams() {
    try {
      const params = new URLSearchParams('?test=value&name=测试');
      const testValue = params.get('test');
      const nameValue = params.get('name');
      
      this.addResult('URLSearchParams', testValue === 'value' && nameValue === '测试', 
        testValue === 'value' ? null : `期望 'value'，实际 '${testValue}'`);
    } catch (error) {
      this.addResult('URLSearchParams', false, error.message);
    }
  }

  // 测试Promise.allSettled
  testPromiseAllSettled() {
    if (typeof Promise.allSettled === 'function') {
      Promise.allSettled([
        Promise.resolve('成功'),
        Promise.reject('失败'),
        Promise.resolve('成功2')
      ]).then(results => {
        const success = results.length === 3 && 
                       results[0].status === 'fulfilled' && 
                       results[1].status === 'rejected';
        this.addResult('Promise.allSettled', success, 
          success ? null : '结果格式不正确');
      }).catch(error => {
        this.addResult('Promise.allSettled', false, error.message);
      });
    } else {
      this.addResult('Promise.allSettled', false, '方法不存在');
    }
  }

  // 测试String.prototype.replaceAll
  testStringReplaceAll() {
    try {
      const testString = 'hello world hello';
      const result = testString.replaceAll ? 
                    testString.replaceAll('hello', '你好') : 
                    testString.split('hello').join('你好');
      
      const expected = '你好 world 你好';
      this.addResult('String.replaceAll', result === expected, 
        result === expected ? null : `期望 '${expected}'，实际 '${result}'`);
    } catch (error) {
      this.addResult('String.replaceAll', false, error.message);
    }
  }

  // 测试Array.prototype.at
  testArrayAt() {
    try {
      const testArray = [1, 2, 3, 4, 5];
      const lastItem = testArray.at ? testArray.at(-1) : testArray[testArray.length - 1];
      
      this.addResult('Array.at', lastItem === 5, 
        lastItem === 5 ? null : `期望 5，实际 ${lastItem}`);
    } catch (error) {
      this.addResult('Array.at', false, error.message);
    }
  }

  // 测试媒体设备API
  async testMediaDevices() {
    try {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        // 不实际请求权限，只测试API是否存在
        this.addResult('MediaDevices.getUserMedia', true, null);
      } else {
        this.addResult('MediaDevices.getUserMedia', false, 'API不存在');
      }
    } catch (error) {
      this.addResult('MediaDevices.getUserMedia', false, error.message);
    }
  }

  // 测试WebSocket
  testWebSocket() {
    try {
      if (typeof WebSocket !== 'undefined') {
        // 只测试构造函数是否存在，不实际连接
        this.addResult('WebSocket', true, null);
      } else {
        this.addResult('WebSocket', false, 'WebSocket不支持');
      }
    } catch (error) {
      this.addResult('WebSocket', false, error.message);
    }
  }

  // 测试Fetch API
  async testFetchAPI() {
    try {
      if (typeof fetch === 'function') {
        // 测试fetch是否存在，不实际发送请求
        this.addResult('Fetch API', true, null);
      } else {
        this.addResult('Fetch API', false, 'Fetch API不支持');
      }
    } catch (error) {
      this.addResult('Fetch API', false, error.message);
    }
  }

  // 添加测试结果
  addResult(testName, passed, error) {
    this.testResults.push({
      name: testName,
      passed,
      error,
      timestamp: new Date().toISOString()
    });
  }

  // 输出测试结果
  outputResults() {
    console.log('\n=== 兼容性测试结果 ===');
    
    const passedTests = this.testResults.filter(r => r.passed);
    const failedTests = this.testResults.filter(r => !r.passed);
    
    console.log(`总计: ${this.testResults.length} 项测试`);
    console.log(`通过: ${passedTests.length} 项`);
    console.log(`失败: ${failedTests.length} 项`);
    
    if (failedTests.length > 0) {
      console.log('\n失败的测试:');
      failedTests.forEach(test => {
        console.log(`❌ ${test.name}: ${test.error || '未知错误'}`);
      });
    }
    
    if (passedTests.length > 0) {
      console.log('\n通过的测试:');
      passedTests.forEach(test => {
        console.log(`✅ ${test.name}`);
      });
    }
    
    console.log('===================\n');
  }

  // 获取兼容性报告
  getCompatibilityReport() {
    const userAgent = navigator.userAgent;
    const passedCount = this.testResults.filter(r => r.passed).length;
    const totalCount = this.testResults.length;
    const compatibilityScore = totalCount > 0 ? (passedCount / totalCount * 100).toFixed(1) : 0;
    
    return {
      userAgent,
      compatibilityScore: `${compatibilityScore}%`,
      passedTests: passedCount,
      totalTests: totalCount,
      testResults: this.testResults,
      recommendations: this.generateRecommendations()
    };
  }

  // 生成兼容性建议
  generateRecommendations() {
    const failedTests = this.testResults.filter(r => !r.passed);
    const recommendations = [];
    
    if (failedTests.some(t => t.name.includes('URLSearchParams'))) {
      recommendations.push('建议升级浏览器以获得更好的URL处理支持');
    }
    
    if (failedTests.some(t => t.name.includes('Promise'))) {
      recommendations.push('建议使用Promise polyfill以获得完整的异步处理支持');
    }
    
    if (failedTests.some(t => t.name.includes('MediaDevices'))) {
      recommendations.push('摄像头和麦克风功能可能无法正常使用，建议升级浏览器');
    }
    
    if (failedTests.some(t => t.name.includes('WebSocket'))) {
      recommendations.push('实时通信功能可能受限，建议使用支持WebSocket的浏览器');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('您的浏览器兼容性良好，所有功能都应该正常工作');
    }
    
    return recommendations;
  }
}

// 导出便捷函数
export async function runCompatibilityTest() {
  const tester = new CompatibilityTester();
  await tester.runAllTests();
  return tester.getCompatibilityReport();
}

// 在开发环境下自动运行测试
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  // 延迟执行，确保所有polyfills都已加载
  setTimeout(async () => {
    const report = await runCompatibilityTest();
    console.log('兼容性报告:', report);
    
    // 将报告保存到全局变量，方便调试
    window.__COMPATIBILITY_REPORT__ = report;
  }, 2000);
}