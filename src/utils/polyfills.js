/**
 * 浏览器兼容性 Polyfills
 * 针对 Chrome 78 及更老版本的兼容性处理
 */

// 检测并报告兼容性问题
function checkCompatibility() {
  // 检查是否在浏览器环境中
  if (typeof window === 'undefined') {
    return [];
  }

  const issues = [];
  
  // 检查关键 API
  if (!window.MediaRecorder) {
    issues.push('MediaRecorder API 不支持，语音录制功能可能无法使用');
  }
  
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    issues.push('Camera/Microphone API 不支持，摄像头和麦克风功能可能无法使用');
  }
  
  if (!window.WebSocket) {
    issues.push('WebSocket 不支持，实时通信功能无法使用');
  }
  
  if (!window.Worker) {
    issues.push('Web Workers 不支持，后台处理功能受限');
  }
  
  // 检查 ES6+ 特性
  try {
    new Function('const test = () => {};')();
  } catch (e) {
    issues.push('箭头函数不支持，需要代码转译');
  }
  
  if (issues.length > 0) {
    console.warn('检测到兼容性问题:', issues);
    return issues;
  }
  
  console.log('浏览器兼容性检查通过');
  return [];
}

// 检查是否在浏览器环境中
if (typeof window !== 'undefined') {
  // 浏览器环境，执行polyfills

  // URL 构造函数 Polyfill (Chrome 78 支持，但某些边缘情况可能有问题)
  if (!window.URL || !window.URL.prototype.searchParams) {
    console.warn('URL API 兼容性问题，加载 polyfill');
    
    // 简单的 URLSearchParams polyfill
    if (!window.URLSearchParams) {
      window.URLSearchParams = class URLSearchParams {
        constructor(search) {
          this.params = new Map();
          if (search) {
            const searchStr = search.startsWith('?') ? search.slice(1) : search;
            const pairs = searchStr.split('&');
            for (let i = 0; i < pairs.length; i++) {
              const pair = pairs[i];
              if (pair) {
                const equalIndex = pair.indexOf('=');
                let key, value;
                if (equalIndex >= 0) {
                  key = pair.substring(0, equalIndex);
                  value = pair.substring(equalIndex + 1);
                } else {
                  key = pair;
                  value = '';
                }
                if (key) {
                  try {
                    this.params.set(decodeURIComponent(key), decodeURIComponent(value || ''));
                  } catch (e) {
                    // 如果解码失败，使用原始值
                    this.params.set(key, value || '');
                  }
                }
              }
            }
          }
        }
        
        get(key) {
          return this.params.get(key) || null;
        }
        
        set(key, value) {
          this.params.set(key, value);
        }
        
        has(key) {
          return this.params.has(key);
        }
        
        toString() {
          const pairs = [];
          this.params.forEach((value, key) => {
            pairs.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));
          });
          return pairs.join('&');
        }
      };
    }
    
    // URL 构造函数增强
    const OriginalURL = window.URL;
    window.URL = class URL extends OriginalURL {
      constructor(url, base) {
        try {
          super(url, base);
        } catch (error) {
          // 如果原生 URL 构造失败，尝试手动解析
          if (base && !url.startsWith('http')) {
            const baseUrl = base.endsWith('/') ? base.slice(0, -1) : base;
            const fullUrl = url.startsWith('/') ? baseUrl + url : baseUrl + '/' + url;
            super(fullUrl);
          } else {
            throw error;
          }
        }
        
        // 确保 searchParams 存在
        if (!this.searchParams) {
          const search = this.search;
          this.searchParams = new URLSearchParams(search);
        }
      }
    };
  }

  // Promise.allSettled polyfill (Chrome 76+ 支持)
  if (!Promise.allSettled) {
    Promise.allSettled = function(promises) {
      return Promise.all(
        promises.map(function(promise) {
          return Promise.resolve(promise)
            .then(function(value) { 
              return { status: 'fulfilled', value: value }; 
            })
            .catch(function(reason) { 
              return { status: 'rejected', reason: reason }; 
            });
        })
      );
    };
  }

  // String.prototype.replaceAll polyfill (Chrome 85+ 支持)
  if (!String.prototype.replaceAll) {
    String.prototype.replaceAll = function(search, replace) {
      return this.split(search).join(replace);
    };
  }

  // Array.prototype.at polyfill (Chrome 92+ 支持)
  if (!Array.prototype.at) {
    Array.prototype.at = function(index) {
      const len = this.length;
      const relativeIndex = index < 0 ? len + index : index;
      return (relativeIndex >= 0 && relativeIndex < len) ? this[relativeIndex] : undefined;
    };
  }

  // Object.fromEntries polyfill (Chrome 73+ 支持，但为了保险)
  if (!Object.fromEntries) {
    Object.fromEntries = function(iterable) {
      const obj = {};
      for (const entry of iterable) {
        const key = entry[0];
        const value = entry[1];
        obj[key] = value;
      }
      return obj;
    };
  }

  // MediaDevices API 兼容性检查
  if (navigator.mediaDevices === undefined) {
    navigator.mediaDevices = {};
  }

  if (navigator.mediaDevices.getUserMedia === undefined) {
    navigator.mediaDevices.getUserMedia = function(constraints) {
      // 尝试使用旧的 API
      const getUserMedia = navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia;
      
      if (!getUserMedia) {
        return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
      }
      
      return new Promise(function(resolve, reject) {
        getUserMedia.call(navigator, constraints, resolve, reject);
      });
    };
  }

  // Fetch API 增强 (Chrome 78 支持，但添加错误处理)
  if (window.fetch) {
    const originalFetch = window.fetch.bind(window);
    window.fetch = function(url, options) {
      options = options || {};
      // 添加默认超时
      const timeout = options.timeout || 30000;
      
      // 检查是否支持AbortController
      if (typeof AbortController !== 'undefined') {
        const controller = new AbortController();
        const timeoutId = setTimeout(function() { 
          controller.abort(); 
        }, timeout);
        
        const fetchOptions = Object.assign({}, options, {
          signal: controller.signal
        });
        
        return originalFetch(url, fetchOptions)
          .then(function(response) {
            clearTimeout(timeoutId);
            return response;
          })
          .catch(function(error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
              throw new Error('Request timeout');
            }
            throw error;
          });
      } else {
        // 不支持AbortController，使用简单的超时处理
        return Promise.race([
          originalFetch(url, options),
          new Promise(function(_, reject) {
            setTimeout(function() { 
              reject(new Error('Request timeout')); 
            }, timeout);
          })
        ]);
      }
    };
  }

  // WebSocket 连接增强
  if (window.WebSocket) {
    const OriginalWebSocket = window.WebSocket;
    window.WebSocket = class WebSocket extends OriginalWebSocket {
      constructor(url, protocols) {
        super(url, protocols);
        
        // 添加连接超时处理
        const timeout = setTimeout(() => {
          if (this.readyState === WebSocket.CONNECTING) {
            console.warn('WebSocket 连接超时，尝试关闭');
            this.close();
          }
        }, 10000);
        
        this.addEventListener('open', function() {
          clearTimeout(timeout);
        });
        
        this.addEventListener('error', function() {
          clearTimeout(timeout);
        });
      }
    };
  }

  // 将兼容性检查函数挂载到全局
  window.checkCompatibility = checkCompatibility;
  
  // 自动执行兼容性检查
  checkCompatibility();
}

// 导出兼容性检查函数
export { checkCompatibility };