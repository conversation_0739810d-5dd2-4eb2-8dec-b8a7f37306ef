<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <!-- 兼容性 meta 标签 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <!-- 预加载关键 polyfills -->
    <script>
      // 立即执行的兼容性检查和基础 polyfills
      // URLSearchParams polyfill for Chrome 78-
      if (!window.URLSearchParams) {
        window.URLSearchParams = function(search) {
          this.params = new Map();
          if (search) {
            const searchStr = search.startsWith('?') ? search.slice(1) : search;
            searchStr.split('&').forEach(function(pair) {
              const parts = pair.split('=');
              const key = parts[0];
              const value = parts[1];
              if (key) {
                this.params.set(decodeURIComponent(key), decodeURIComponent(value || ''));
              }
            }.bind(this));
          }
        };
        window.URLSearchParams.prototype.get = function(key) {
          return this.params.get(key) || null;
        };
        window.URLSearchParams.prototype.set = function(key, value) {
          this.params.set(key, value);
        };
        window.URLSearchParams.prototype.has = function(key) {
          return this.params.has(key);
        };
      }
      
      // Promise.allSettled polyfill for Chrome 76-
      if (!Promise.allSettled) {
        Promise.allSettled = function(promises) {
          return Promise.all(
            promises.map(function(promise) {
              return Promise.resolve(promise)
                .then(function(value) { return { status: 'fulfilled', value: value }; })
                .catch(function(reason) { return { status: 'rejected', reason: reason }; });
            })
          );
        };
      }
    </script>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    %sveltekit.head%
  </head>
  <body data-sveltekit-preload-data="hover">
    <div id="svelte-app" style="display: contents">%sveltekit.body%</div>
  </body>
</html>
