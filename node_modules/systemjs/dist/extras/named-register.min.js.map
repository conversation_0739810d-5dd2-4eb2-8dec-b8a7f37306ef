{"version": 3, "file": "##.min.js", "names": ["global", "setRegisterRegistry", "systemInstance", "registerRegistry", "Object", "create", "namedRegisterAliases", "System", "firstNamedDefine", "firstName", "systemJSPrototype", "constructor", "prototype", "SystemJS", "call", "this", "register", "name", "deps", "declare", "metas", "apply", "arguments", "define", "Promise", "resolve", "then", "id", "parentURL", "err", "instantiate", "url", "firstParentUrl", "meta", "result", "getRegister", "self"], "sources": ["named-register.js"], "sourcesContent": ["(function () {\n\n  /*\r\n   * SystemJS named register extension\r\n   * Supports System.register('name', [..deps..], function (_export, _context) { ... })\r\n   *\r\n   * Names are written to the registry as-is\r\n   * System.register('x', ...) can be imported as System.import('x')\r\n   */\r\n  (function (global) {\r\n    var System = global.System;\r\n    setRegisterRegistry(System);\r\n    var systemJSPrototype = System.constructor.prototype;\r\n    var constructor = System.constructor;\r\n    var SystemJS = function () {\r\n      constructor.call(this);\r\n      setRegisterRegistry(this);\r\n    };\r\n    SystemJS.prototype = systemJSPrototype;\r\n    System.constructor = SystemJS;\r\n\r\n    var firstNamedDefine, firstName;\r\n\r\n    function setRegisterRegistry(systemInstance) {\r\n      systemInstance.registerRegistry = Object.create(null);\r\n      systemInstance.namedRegisterAliases = Object.create(null);\r\n    }\r\n\r\n    var register = systemJSPrototype.register;\r\n    systemJSPrototype.register = function (name, deps, declare, metas) {\r\n      if (typeof name !== 'string')\r\n        return register.apply(this, arguments);\r\n      var define = [deps, declare, metas];\r\n      this.registerRegistry[name] = define;\r\n      if (!firstNamedDefine) {\r\n        firstNamedDefine = define;\r\n        firstName = name;\r\n      }\r\n      Promise.resolve().then(function () {\r\n        firstNamedDefine = null;\r\n        firstName = null;\r\n      });\r\n      return register.apply(this, [deps, declare, metas]);\r\n    };\r\n\r\n    var resolve = systemJSPrototype.resolve;\r\n    systemJSPrototype.resolve = function (id, parentURL) {\r\n      try {\r\n        // Prefer import map (or other existing) resolution over the registerRegistry\r\n        return resolve.call(this, id, parentURL);\r\n      } catch (err) {\r\n        if (id in this.registerRegistry) {\r\n          return this.namedRegisterAliases[id] || id;\r\n        }\r\n        throw err;\r\n      }\r\n    };\r\n\r\n    var instantiate = systemJSPrototype.instantiate;\r\n    systemJSPrototype.instantiate = function (url, firstParentUrl, meta) {\r\n      var result = this.registerRegistry[url];\r\n      if (result) {\r\n        this.registerRegistry[url] = null;\r\n        return result;\r\n      } else {\r\n        return instantiate.call(this, url, firstParentUrl, meta);\r\n      }\r\n    };\r\n\r\n    var getRegister = systemJSPrototype.getRegister;\r\n    systemJSPrototype.getRegister = function (url) {\r\n      // Calling getRegister() because other extras need to know it was called so they can perform side effects\r\n      var register = getRegister.call(this, url);\r\n\r\n      if (firstName && url) {\r\n        this.namedRegisterAliases[firstName] = url;\r\n      }\r\n      var result = firstNamedDefine || register;\r\n      firstNamedDefine = null;\r\n      firstName = null;\r\n      return result;\r\n    };\r\n  })(typeof self !== 'undefined' ? self : global);\n\n})();\n"], "mappings": "CASE,SAAWA,GAcT,SAASC,EAAoBC,GAC3BA,EAAeC,iBAAmBC,OAAOC,OAAO,MAChDH,EAAeI,qBAAuBF,OAAOC,OAAO,KACtD,CAhBA,IAAIE,EAASP,EAAOO,OACpBN,EAAoBM,GACpB,IASIC,EAAkBC,EATlBC,EAAoBH,EAAOI,YAAYC,UACvCD,EAAcJ,EAAOI,YACrBE,EAAW,WACbF,EAAYG,KAAKC,MACjBd,EAAoBc,KACtB,EACAF,EAASD,UAAYF,EACrBH,EAAOI,YAAcE,EASrB,IAAIG,EAAWN,EAAkBM,SACjCN,EAAkBM,SAAW,SAAUC,EAAMC,EAAMC,EAASC,GAC1D,GAAoB,iBAATH,EACT,OAAOD,EAASK,MAAMN,KAAMO,WAC9B,IAAIC,EAAS,CAACL,EAAMC,EAASC,GAU7B,OATAL,KAAKZ,iBAAiBc,GAAQM,EACzBf,IACHA,EAAmBe,EACnBd,EAAYQ,GAEdO,QAAQC,UAAUC,MAAK,WACrBlB,EAAmB,KACnBC,EAAY,IACd,IACOO,EAASK,MAAMN,KAAM,CAACG,EAAMC,EAASC,GAC9C,EAEA,IAAIK,EAAUf,EAAkBe,QAChCf,EAAkBe,QAAU,SAAUE,EAAIC,GACxC,IAEE,OAAOH,EAAQX,KAAKC,KAAMY,EAAIC,EAMhC,CALE,MAAOC,GACP,GAAIF,KAAMZ,KAAKZ,iBACb,OAAOY,KAAKT,qBAAqBqB,IAAOA,EAE1C,MAAME,CACR,CACF,EAEA,IAAIC,EAAcpB,EAAkBoB,YACpCpB,EAAkBoB,YAAc,SAAUC,EAAKC,EAAgBC,GAC7D,IAAIC,EAASnB,KAAKZ,iBAAiB4B,GACnC,OAAIG,GACFnB,KAAKZ,iBAAiB4B,GAAO,KACtBG,GAEAJ,EAAYhB,KAAKC,KAAMgB,EAAKC,EAAgBC,EAEvD,EAEA,IAAIE,EAAczB,EAAkByB,YACpCzB,EAAkByB,YAAc,SAAUJ,GAExC,IAAIf,EAAWmB,EAAYrB,KAAKC,KAAMgB,GAElCtB,GAAasB,IACfhB,KAAKT,qBAAqBG,GAAasB,GAEzC,IAAIG,EAAS1B,GAAoBQ,EAGjC,OAFAR,EAAmB,KACnBC,EAAY,KACLyB,CACT,CACD,CAzED,CAyEmB,oBAATE,KAAuBA,KAAOpC"}