{"version": 3, "file": "##.min.js", "names": ["errMsg", "errCode", "msg", "global", "unsupportedRequire", "Error", "requireExportsModule", "define", "arg1", "arg2", "arg3", "deps", "exec", "isNamedRegister", "name", "depArg", "execArg", "Array", "isArray", "amdRegister", "amdDefineDeps", "amdDefineExec", "createSetter", "idx", "setters", "push", "ns", "depModules", "__use<PERSON>efault", "default", "exports", "module", "splice", "i", "length", "id", "index", "amdExec", "_export", "_context", "execute", "uri", "meta", "url", "amdResult", "apply", "undefined", "createAMDRegister", "System", "registerRegistry", "register", "console", "warn", "amd", "self"], "sources": ["amd.js"], "sourcesContent": ["(function () {\n\n  function errMsg(errCode, msg) {\r\n    return (msg || \"\") + \" (SystemJS Error#\" + errCode + \" \" + \"https://github.com/systemjs/systemjs/blob/main/docs/errors.md#\" + errCode + \")\";\r\n  }\n\n  /*\r\n   * Support for AMD loading\r\n   */\r\n  (function (global) {\r\n    function unsupportedRequire () {\r\n      throw Error(errMsg(5, 'AMD require not supported.'));\r\n    }\r\n\r\n    var requireExportsModule = ['require', 'exports', 'module'];\r\n\r\n    function createAMDRegister (amdDefineDeps, amdDefineExec) {\r\n      var exports = {};\r\n      var module = { exports: exports };\r\n      var depModules = [];\r\n      var setters = [];\r\n      var splice = 0;\r\n      for (var i = 0; i < amdDefineDeps.length; i++) {\r\n        var id = amdDefineDeps[i];\r\n        var index = setters.length;\r\n        if (id === 'require') {\r\n          depModules[i] = unsupportedRequire;\r\n          splice++;\r\n        }\r\n        else if (id === 'module') {\r\n          depModules[i] = module;\r\n          splice++;\r\n        }\r\n        else if (id === 'exports') {\r\n          depModules[i] = exports;\r\n          splice++;\r\n        }\r\n        else {\r\n          createSetter(i);\r\n        }\r\n        if (splice)\r\n          amdDefineDeps[index] = id;\r\n      }\r\n      if (splice)\r\n        amdDefineDeps.length -= splice;\r\n      var amdExec = amdDefineExec;\r\n      return [amdDefineDeps, function (_export, _context) {\r\n        _export({ default: exports, __useDefault: true });\r\n        return {\r\n          setters: setters,\r\n          execute: function () {\r\n            module.uri = _context.meta.url;\r\n            var amdResult = amdExec.apply(exports, depModules);\r\n            if (amdResult !== undefined)\r\n              module.exports = amdResult;\r\n            _export(module.exports);\r\n            _export('default', module.exports);\r\n          }\r\n        };\r\n      }];\r\n\r\n      // needed to avoid iteration scope issues\r\n      function createSetter(idx) {\r\n        setters.push(function (ns) {\r\n          depModules[idx] = ns.__useDefault ? ns.default : ns;\r\n        });\r\n      }\r\n    }\r\n\r\n    global.define = function (arg1, arg2, arg3) {\r\n      var isNamedRegister = typeof arg1 === 'string';\r\n      var name = isNamedRegister ? arg1 : null;\r\n      var depArg = isNamedRegister ? arg2 : arg1;\r\n      var execArg = isNamedRegister ? arg3 : arg2;\r\n\r\n      // The System.register(deps, exec) arguments\r\n      var deps, exec;\r\n\r\n      // define([], function () {})\r\n      if (Array.isArray(depArg)) {\r\n        deps = depArg;\r\n        exec = execArg;\r\n      }\r\n      // define({})\r\n      else if (typeof depArg === 'object') {\r\n        deps = [];\r\n        exec = function () { return depArg };\r\n      }\r\n      // define(function () {})\r\n      else if (typeof depArg === 'function') {\r\n        deps = requireExportsModule;\r\n        exec = depArg;\r\n      } else {\r\n        throw Error(errMsg(9, 'Invalid call to AMD define()'));\r\n      }\r\n\r\n      var amdRegister = createAMDRegister(deps, exec);\r\n\r\n      if (isNamedRegister) {\r\n        if (System.registerRegistry) {\r\n          System.registerRegistry[name] = amdRegister;\r\n          System.register(name, amdRegister[0], amdRegister[1]);\r\n        } else\r\n          console.warn(errMsg('W6', 'Include named-register.js for full named define support'));\r\n          // TODO: create new warning number and documentation for using named define without named-register extra\r\n          System.register(amdRegister[0], amdRegister[1]);\r\n      } else\r\n        System.register(amdRegister[0], amdRegister[1]);\r\n    };\r\n    global.define.amd = {};\r\n  })(typeof self !== 'undefined' ? self : global);\n\n})();\n"], "mappings": "CAAA,WAEE,SAASA,EAAOC,EAASC,GACvB,OAAQA,GAAO,IAAM,oBAAsBD,EAApC,kEAAuHA,EAAU,GAC1I,EAKA,SAAWE,GACT,SAASC,IACP,MAAMC,MAAML,EAAO,EAAG,8BACxB,CAEA,IAAIM,EAAuB,CAAC,UAAW,UAAW,UAuDlDH,EAAOI,OAAS,SAAUC,EAAMC,EAAMC,GACpC,IAMIC,EAAMC,EANNC,EAAkC,iBAATL,EACzBM,EAAOD,EAAkBL,EAAO,KAChCO,EAASF,EAAkBJ,EAAOD,EAClCQ,EAAUH,EAAkBH,EAAOD,EAMvC,GAAIQ,MAAMC,QAAQH,GAChBJ,EAAOI,EACPH,EAAOI,OAGJ,GAAsB,iBAAXD,EACdJ,EAAO,GACPC,EAAO,WAAc,OAAOG,CAAO,MAGhC,IAAsB,mBAAXA,EAId,MAAMV,MAAML,EAAO,EAAG,iCAHtBW,EAAOL,EACPM,EAAOG,CAGT,CAEA,IAAII,EAhFN,SAA4BC,EAAeC,GA8CzC,SAASC,EAAaC,GACpBC,EAAQC,MAAK,SAAUC,GACrBC,EAAWJ,GAAOG,EAAGE,aAAeF,EAAGG,QAAUH,CACnD,GACF,CA5CA,IALA,IAAII,EAAU,CAAC,EACXC,EAAS,CAAED,QAASA,GACpBH,EAAa,GACbH,EAAU,GACVQ,EAAS,EACJC,EAAI,EAAGA,EAAIb,EAAcc,OAAQD,IAAK,CAC7C,IAAIE,EAAKf,EAAca,GACnBG,EAAQZ,EAAQU,OACT,YAAPC,GACFR,EAAWM,GAAK7B,EAChB4B,KAEc,WAAPG,GACPR,EAAWM,GAAKF,EAChBC,KAEc,YAAPG,GACPR,EAAWM,GAAKH,EAChBE,KAGAV,EAAaW,GAEXD,IACFZ,EAAcgB,GAASD,EAC3B,CACIH,IACFZ,EAAcc,QAAUF,GAC1B,IAAIK,EAAUhB,EACd,MAAO,CAACD,EAAe,SAAUkB,EAASC,GAExC,OADAD,EAAQ,CAAET,QAASC,EAASF,cAAc,IACnC,CACLJ,QAASA,EACTgB,QAAS,WACPT,EAAOU,IAAMF,EAASG,KAAKC,IAC3B,IAAIC,EAAYP,EAAQQ,MAAMf,EAASH,QACrBmB,IAAdF,IACFb,EAAOD,QAAUc,GACnBN,EAAQP,EAAOD,SACfQ,EAAQ,UAAWP,EAAOD,QAC5B,EAEJ,EAQF,CA6BoBiB,CAAkBpC,EAAMC,GAEtCC,GACEmC,OAAOC,kBACTD,OAAOC,iBAAiBnC,GAAQK,EAChC6B,OAAOE,SAASpC,EAAMK,EAAY,GAAIA,EAAY,KAElDgC,QAAQC,KAAKpD,EAAO,KAAM,4DAE1BgD,OAAOE,SAAS/B,EAAY,GAAIA,EAAY,KAE9C6B,OAAOE,SAAS/B,EAAY,GAAIA,EAAY,GAChD,EACAhB,EAAOI,OAAO8C,IAAM,CAAC,CACtB,CArGD,CAqGmB,oBAATC,KAAuBA,KAAOnD,OAEzC,CAhHD"}