!function(){function e(e,r){return(r||"")+" (SystemJS Error#"+e+" https://github.com/systemjs/systemjs/blob/main/docs/errors.md#"+e+")"}!function(r){function t(){throw Error(e(5,"AMD require not supported."))}var n=["require","exports","module"];r.define=function(r,o,s){var u,i,f="string"==typeof r,l=f?r:null,a=f?o:r,d=f?s:o;if(Array.isArray(a))u=a,i=d;else if("object"==typeof a)u=[],i=function(){return a};else{if("function"!=typeof a)throw Error(e(9,"Invalid call to AMD define()"));u=n,i=a}var c=function(e,r){function n(e){i.push((function(r){u[e]=r.__useDefault?r.default:r}))}for(var o={},s={exports:o},u=[],i=[],f=0,l=0;l<e.length;l++){var a=e[l],d=i.length;"require"===a?(u[l]=t,f++):"module"===a?(u[l]=s,f++):"exports"===a?(u[l]=o,f++):n(l),f&&(e[d]=a)}f&&(e.length-=f);var c=r;return[e,function(e,r){return e({default:o,__useDefault:!0}),{setters:i,execute:function(){s.uri=r.meta.url;var t=c.apply(o,u);void 0!==t&&(s.exports=t),e(s.exports),e("default",s.exports)}}}]}(u,i);f?(System.registerRegistry?(System.registerRegistry[l]=c,System.register(l,c[0],c[1])):console.warn(e("W6","Include named-register.js for full named define support")),System.register(c[0],c[1])):System.register(c[0],c[1])},r.define.amd={}}("undefined"!=typeof self?self:global)}();
//# sourceMappingURL=amd.min.js.map
