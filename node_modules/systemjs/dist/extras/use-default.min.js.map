{"version": 3, "file": "##.min.js", "names": ["global", "systemJSPrototype", "System", "constructor", "prototype", "originalImport", "import", "apply", "this", "arguments", "then", "ns", "__use<PERSON>efault", "default", "self"], "sources": ["use-default.js"], "sourcesContent": ["(function () {\n\n  /*\r\n   * Interop for AMD modules to return the direct AMD binding instead of a\r\n   * `{ default: amdModule }` object from `System.import`\r\n   * \r\n   * Note: This extra is deprecated and will be removed in the next major.\r\n   */\r\n  (function (global) {\r\n    var systemJSPrototype = global.System.constructor.prototype;\r\n    var originalImport = systemJSPrototype.import;\r\n\r\n    systemJSPrototype.import = function () {\r\n      return originalImport.apply(this, arguments).then(function (ns) {\r\n        return ns.__useDefault ? ns.default : ns;\r\n      });\r\n    };\r\n  })(typeof self !== 'undefined' ? self : global);\n\n})();\n"], "mappings": "CAQE,SAAWA,GACT,IAAIC,EAAoBD,EAAOE,OAAOC,YAAYC,UAC9CC,EAAiBJ,EAAkBK,OAEvCL,EAAkBK,OAAS,WACzB,OAAOD,EAAeE,MAAMC,KAAMC,WAAWC,MAAK,SAAUC,GAC1D,OAAOA,EAAGC,aAAeD,EAAGE,QAAUF,CACxC,GACF,CACD,CATD,CASmB,oBAATG,KAAuBA,KAAOd"}