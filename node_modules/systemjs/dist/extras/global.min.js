!function(t){function n(n){return!t.hasOwnProperty(n)||!isNaN(n)&&n<t.length||c&&t[n]&&"undefined"!=typeof window&&t[n].parent===window}var r,e,i,o=t.System.constructor.prototype,u=o.import;o.import=function(o,f,a){return function(){for(var o in r=e=void 0,t)n(o)||(r?e||(e=o):r=o,i=o)}(),u.call(this,o,f,a)};var f=[[],function(){return{}}],a=o.getRegister;o.getRegister=function(){var o=a.call(this);if(o)return o;var u,c=function(o){var u,f,a=0;for(var c in t)if(!n(c)){if(0===a&&c!==r||1===a&&c!==e)return c;u?(i=c,f=o&&f||c):u=c===i,a++}return f}(this.firstGlobalProp);if(!c)return f;try{u=t[c]}catch(s){return f}return[[],function(t){return{execute:function(){t(u),t({default:u,__useDefault:!0})}}}]};var c="undefined"!=typeof navigator&&-1!==navigator.userAgent.indexOf("Trident")}("undefined"!=typeof self?self:global);
//# sourceMappingURL=global.min.js.map
